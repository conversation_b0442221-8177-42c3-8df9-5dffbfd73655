# MS-User 微服务架构规范

## 1. 架构概述
ms-user 微服务采用分层架构模式，遵循单一职责原则和依赖倒置原则，确保系统的可维护性和可扩展性。

## 2. 分层架构

### 2.1 架构层次
```
┌─────────────────────────────────────┐
│           Controller Layer          │  ← 控制器层
├─────────────────────────────────────┤
│            Service Layer            │  ← 业务逻辑层
├─────────────────────────────────────┤
│            Mapper Layer             │  ← 数据访问层
├─────────────────────────────────────┤
│           Database Layer            │  ← 数据存储层
└─────────────────────────────────────┘
```

### 2.2 层次职责

#### Controller Layer (控制器层)
- **职责**: 处理HTTP请求，参数校验，调用Service层
- **规范**:
  - 不包含业务逻辑
  - 统一异常处理
  - 参数校验使用@Valid
  - 返回统一响应格式MsResponse

```java
@RestController
@RequestMapping("/user")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @PostMapping("/add")
    public MsResponse<UserDTO> addUser(@Valid @RequestBody AddUserRequest request) {
        UserDTO userDTO = userService.addUser(request);
        return MsResponse.success(userDTO);
    }
}
```

#### Service Layer (业务逻辑层)
- **职责**: 核心业务逻辑，事务管理，调用Mapper层
- **规范**:
  - 接口与实现分离
  - 事务边界控制
  - 业务异常处理
  - 缓存策略实现

```java
@Service
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public UserDTO addUser(AddUserRequest request) {
        // 业务逻辑实现
        // 数据校验、业务处理、数据持久化
    }
}
```

#### Mapper Layer (数据访问层)
- **职责**: 数据库操作，SQL执行
- **规范**:
  - 使用MyBatis注解或XML配置
  - 参数使用@Param注解
  - 返回类型明确定义

```java
@Mapper
public interface UserMapper {
    int addUser(UserInfoDAO userInfoDAO);
    UserInfoDAO getUserById(@Param("userId") String userId);
    List<UserInfoDAO> getUserList(GetUserCondition condition);
}
```

## 3. 包结构规范

### 3.1 标准包结构
```
com.microservice.user/
├── MsUserApplication.java              # 启动类
├── annotation/                         # 自定义注解
│   ├── OptLog.java                    # 操作日志注解
│   └── ValidateToken.java             # Token校验注解
├── aspect/                            # AOP切面
│   ├── LogAspect.java                # 日志切面
│   └── TokenAspect.java              # Token切面
├── common/                            # 通用组件
│   ├── Common.java                   # 通用工具
│   └── base/                         # 基础类
├── config/                            # 配置类
│   ├── SwaggerConfig.java            # Swagger配置
│   ├── RedisConfig.java              # Redis配置
│   └── MybatisConfig.java            # MyBatis配置
├── constant/                          # 常量定义
│   ├── UserConstant.java             # 用户常量
│   ├── SystemConstant.java           # 系统常量
│   └── IdentityConstant.java         # 身份常量
├── controller/                        # 控制器层
│   ├── UserController.java           # 用户控制器
│   ├── IdentityController.java       # 身份控制器
│   └── PingController.java           # 健康检查
├── entity/                            # 实体类
│   ├── condition/                    # 查询条件
│   │   ├── GetUserCondition.java     # 用户查询条件
│   │   └── GetIdentityCondition.java # 身份查询条件
│   ├── dao/                          # 数据访问对象
│   │   ├── UserInfoDAO.java          # 用户信息DAO
│   │   └── IdentityInfoDAO.java      # 身份信息DAO
│   ├── dto/                          # 数据传输对象
│   │   └── ms/                       # 微服务DTO
│   │       ├── UserDTO.java          # 用户DTO
│   │       └── IdentityDTO.java      # 身份DTO
│   ├── request/                      # 请求对象
│   │   └── ms/                       # 微服务请求
│   │       ├── MsBaseRequest.java    # 基础请求
│   │       └── user/                 # 用户请求
│   │           ├── AddUserRequest.java
│   │           └── UpdateUserRequest.java
│   └── response/                     # 响应对象
│       └── ms/                       # 微服务响应
│           └── MsResponse.java       # 统一响应
├── exception/                         # 异常处理
│   ├── UserException.java            # 用户异常
│   ├── CommonException.java          # 通用异常
│   └── GlobalExceptionHandler.java   # 全局异常处理
├── listener/                          # 事件监听器
│   ├── ApplicationReadyEventListener.java
│   └── DependenciesCheckListener.java
├── mapper/                            # 数据访问层
│   ├── UserMapper.java               # 用户Mapper
│   └── IdentityMapper.java           # 身份Mapper
├── service/                           # 业务逻辑层
│   ├── UserService.java              # 用户服务接口
│   ├── IdentityService.java          # 身份服务接口
│   └── impl/                         # 服务实现
│       ├── UserServiceImpl.java      # 用户服务实现
│       └── IdentityServiceImpl.java  # 身份服务实现
└── util/                             # 工具类
    ├── StringUtil.java               # 字符串工具
    ├── TimeUtil.java                 # 时间工具
    ├── ConfigUtil.java               # 配置工具
    └── RedisUtil.java                # Redis工具
```

### 3.2 包命名规范
- 包名全部小写
- 使用单数形式
- 按功能模块划分子包
- 避免使用缩写

## 4. 依赖关系规范

### 4.1 依赖方向
```
Controller → Service → Mapper → Database
     ↓         ↓         ↓
   Request   Business   Data
   Response  Logic     Access
```

### 4.2 依赖注入规范
```java
// 推荐：构造器注入
@Service
public class UserServiceImpl implements UserService {
    private final UserMapper userMapper;
    private final RedisUtil redisUtil;
    
    public UserServiceImpl(UserMapper userMapper, RedisUtil redisUtil) {
        this.userMapper = userMapper;
        this.redisUtil = redisUtil;
    }
}

// 可接受：字段注入（简单场景）
@Autowired
private UserMapper userMapper;
```

## 5. 配置管理规范

### 5.1 配置文件结构
```
config/
├── application.properties              # 主配置文件
├── application-dev.properties          # 开发环境配置
├── application-test.properties         # 测试环境配置
└── application-prod.properties         # 生产环境配置
```

### 5.2 配置类规范
```java
@Configuration
@EnableConfigurationProperties
public class RedisConfig {
    
    @Bean
    @ConditionalOnMissingBean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        // Redis配置实现
    }
}
```

## 6. 事务管理规范

### 6.1 事务边界
- Service层方法级别控制事务
- 读操作使用@Transactional(readOnly = true)
- 写操作指定回滚异常类型

```java
@Service
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl implements UserService {
    
    @Transactional(readOnly = true)
    public UserDTO getUserById(String userId) {
        // 只读事务
    }
    
    @Transactional(rollbackFor = Exception.class)
    public UserDTO addUser(AddUserRequest request) {
        // 写事务
    }
}
```

## 7. 缓存策略规范

### 7.1 缓存层次
```
Application Cache (Redis)
         ↓
   Database Cache
         ↓
    Database
```

### 7.2 缓存使用规范
```java
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private RedisUtil redisUtil;
    
    public UserDTO getUserById(String userId) {
        // 1. 先查缓存
        String cacheKey = "user:" + userId;
        UserDTO userDTO = redisUtil.get(cacheKey, UserDTO.class);
        
        if (userDTO == null) {
            // 2. 查数据库
            UserInfoDAO userDAO = userMapper.getUserById(userId);
            userDTO = UserDTO.create(userDAO);
            
            // 3. 写入缓存
            redisUtil.set(cacheKey, userDTO, 3600);
        }
        
        return userDTO;
    }
}
```

## 8. 异常处理架构

### 8.1 异常层次
```
BusinessException (业务异常)
    ├── UserException (用户异常)
    ├── IdentityException (身份异常)
    └── SystemException (系统异常)
```

### 8.2 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(UserException.class)
    public MsResponse<Object> handleUserException(UserException e) {
        return MsResponse.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public MsResponse<Object> handleException(Exception e) {
        logger.error("系统异常", e);
        return MsResponse.error("50000", "系统内部错误");
    }
}
```

## 9. 日志架构规范

### 9.1 日志级别使用
- **DEBUG**: 调试信息，开发环境使用
- **INFO**: 业务流程信息
- **WARN**: 警告信息，需要关注
- **ERROR**: 错误信息，需要处理

### 9.2 日志格式规范
```java
// 业务日志
logger.info("用户登录成功, userId: {}, ip: {}", userId, ip);

// 异常日志
logger.error("用户添加失败, request: {}", request, exception);

// 性能日志
long startTime = System.currentTimeMillis();
// 业务处理
long endTime = System.currentTimeMillis();
logger.info("方法执行耗时: {}ms", endTime - startTime);
```

## 10. 安全架构规范

### 10.1 认证授权
- Token基础认证
- 接口权限控制
- 敏感操作审计

### 10.2 数据安全
- 敏感数据加密
- SQL注入防护
- XSS攻击防护

## 11. 监控与健康检查

### 11.1 健康检查接口
```java
@RestController
public class PingController {
    
    @GetMapping("/ping")
    public MsResponse<PingDTO> ping() {
        // 检查依赖服务状态
        // 返回服务健康状态
    }
}
```

### 11.2 性能监控
- 接口响应时间监控
- 数据库连接池监控
- Redis连接监控
- JVM内存监控
