# MS-User 微服务 API 设计规范

## 1. API 设计原则

### 1.1 RESTful 设计原则
- 使用HTTP动词表示操作类型
- 使用名词表示资源
- 统一的URL结构
- 无状态设计

### 1.2 统一响应格式
所有API接口必须使用统一的响应格式：

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MsResponse<T> {
    private String code;        // 响应码
    private String message;     // 响应消息  
    private T data;            // 响应数据
    private Long timestamp;     // 时间戳
    
    public static <T> MsResponse<T> success(T data) {
        return MsResponse.<T>builder()
                .code("00000")
                .message("success")
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    public static <T> MsResponse<T> error(String code, String message) {
        return MsResponse.<T>builder()
                .code(code)
                .message(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
```

## 2. URL 设计规范

### 2.1 URL 结构
```
/{service}/{version}/{resource}/{action}
```

### 2.2 URL 示例
```
POST   /user/v1/users/add              # 添加用户
GET    /user/v1/users/{userId}         # 获取用户详情
PUT    /user/v1/users/{userId}         # 更新用户
DELETE /user/v1/users/{userId}         # 删除用户
POST   /user/v1/users/list             # 获取用户列表
POST   /user/v1/users/batch            # 批量操作
```

### 2.3 URL 命名规范
- 使用小写字母
- 使用连字符分隔单词
- 资源名使用复数形式
- 避免使用动词

## 3. HTTP 方法使用规范

| HTTP方法 | 用途 | 示例 |
|---------|------|------|
| GET | 获取资源 | GET /users/{id} |
| POST | 创建资源/复杂查询 | POST /users/add |
| PUT | 更新资源 | PUT /users/{id} |
| DELETE | 删除资源 | DELETE /users/{id} |
| PATCH | 部分更新 | PATCH /users/{id} |

## 4. 请求参数规范

### 4.1 基础请求类
```java
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MsBaseRequest {
    private String appChannelId;    // 应用渠道ID
    private String dataChannelId;   // 数据渠道ID
    private String token;           // 访问令牌
    private String requestId;       // 请求ID
}
```

### 4.2 请求参数校验
```java
@Data
@EqualsAndHashCode(callSuper = true)
public class AddUserRequest extends MsBaseRequest {
    @NotEmpty(message = "用户名不能为空")
    @Length(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间")
    private String userName;
    
    @NotEmpty(message = "密码不能为空")
    @Length(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    private List<String> groupIdList;
    private Map<String, Object> extraContent;
}
```

### 4.3 分页查询参数
```java
@Data
@EqualsAndHashCode(callSuper = true)
public class GetUserListRequest extends MsBaseRequest {
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;
    
    @Min(value = 1, message = "每页数量必须大于0")
    @Max(value = 100, message = "每页数量不能超过100")
    private Integer limit = 10;
    
    private String userName;        // 用户名模糊查询
    private List<String> userIdList; // 用户ID列表
    private String startDateTime;   // 开始时间
    private String endDateTime;     // 结束时间
    private Integer status;         // 状态
}
```

## 5. 响应数据规范

### 5.1 单个对象响应
```java
@PostMapping("/add")
public MsResponse<UserDTO> addUser(@Valid @RequestBody AddUserRequest request) {
    UserDTO userDTO = userService.addUser(request);
    return MsResponse.success(userDTO);
}
```

### 5.2 列表数据响应
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataListDTO<T> {
    private Integer dataCount;      // 总数量
    private List<T> dataList;       // 数据列表
    
    public static <T> DataListDTO<T> create(List<T> dataList, Integer dataCount) {
        return DataListDTO.<T>builder()
                .dataList(dataList)
                .dataCount(dataCount)
                .build();
    }
}

@PostMapping("/list")
public MsResponse<DataListDTO<UserDTO>> getUserList(@RequestBody GetUserListRequest request) {
    DataListDTO<UserDTO> result = userService.getUserList(request);
    return MsResponse.success(result);
}
```

### 5.3 布尔操作响应
```java
@DeleteMapping("/{userId}")
public MsResponse<Boolean> deleteUser(@PathVariable String userId) {
    boolean result = userService.deleteUser(userId);
    return MsResponse.success(result);
}
```

## 6. 错误码规范

### 6.1 错误码结构
```
错误码格式: XYZAB
X: 错误级别 (1-系统级, 2-服务级, 3-业务级)
Y: 服务标识 (0-通用, 1-用户, 2-身份, 3-部门...)
Z: 模块标识 (0-通用, 1-增加, 2-删除, 3-修改, 4-查询...)
AB: 具体错误序号 (01-99)
```

### 6.2 常用错误码
```java
public class ErrorCode {
    // 成功
    public static final String SUCCESS = "00000";
    
    // 系统级错误 (1xxxx)
    public static final String SYSTEM_ERROR = "10000";
    public static final String PARAM_ERROR = "10001";
    public static final String TOKEN_ERROR = "10002";
    
    // 用户服务错误 (21xxx)
    public static final String USER_NOT_FOUND = "21001";
    public static final String USER_ALREADY_EXISTS = "21002";
    public static final String USER_PASSWORD_ERROR = "21003";
    public static final String USER_STATUS_ERROR = "21004";
    
    // 身份服务错误 (22xxx)
    public static final String IDENTITY_NOT_FOUND = "22001";
    public static final String IDENTITY_ALREADY_EXISTS = "22002";
}
```

## 7. 接口文档规范

### 7.1 Swagger 注解使用
```java
@Api(tags = "用户管理")
@RestController
@RequestMapping("/user")
public class UserController {
    
    @ApiOperation(value = "添加用户", notes = "创建新用户账号")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功"),
        @ApiResponse(code = 400, message = "参数错误"),
        @ApiResponse(code = 500, message = "系统错误")
    })
    @PostMapping("/add")
    public MsResponse<UserDTO> addUser(
            @ApiParam(value = "用户信息", required = true) 
            @Valid @RequestBody AddUserRequest request) {
        UserDTO userDTO = userService.addUser(request);
        return MsResponse.success(userDTO);
    }
}
```

### 7.2 实体类文档注解
```java
@ApiModel(description = "用户信息")
@Data
public class UserDTO {
    @ApiModelProperty(value = "用户ID", example = "123456")
    private String userId;
    
    @ApiModelProperty(value = "用户名", example = "zhangsan")
    private String userName;
    
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;
    
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private String createTime;
}
```

## 8. 接口版本管理

### 8.1 版本控制策略
- URL路径版本控制: `/v1/users`
- 向后兼容原则
- 废弃接口标记

### 8.2 版本升级规范
```java
@RestController
@RequestMapping("/v1/user")
@Deprecated // 标记废弃版本
public class UserV1Controller {
    // 旧版本接口实现
}

@RestController
@RequestMapping("/v2/user")
public class UserV2Controller {
    // 新版本接口实现
}
```

## 9. 接口安全规范

### 9.1 Token 验证
```java
@ValidateToken // 自定义注解进行Token校验
@PostMapping("/add")
public MsResponse<UserDTO> addUser(@Valid @RequestBody AddUserRequest request) {
    // 接口实现
}
```

### 9.2 参数校验
```java
@PostMapping("/add")
public MsResponse<UserDTO> addUser(@Valid @RequestBody AddUserRequest request) {
    // @Valid 注解自动校验请求参数
    // 校验失败会抛出 MethodArgumentNotValidException
}
```

### 9.3 敏感信息处理
```java
@Data
public class UserDTO {
    private String userId;
    private String userName;
    
    @JsonIgnore // 敏感信息不返回给前端
    private String password;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String internalField; // 只接收不返回
}
```

## 10. 接口性能规范

### 10.1 分页查询
```java
@PostMapping("/list")
public MsResponse<DataListDTO<UserDTO>> getUserList(@RequestBody GetUserListRequest request) {
    // 使用 PageHelper 进行分页
    PageHelper.startPage(request.getPage(), request.getLimit());
    List<UserDTO> userList = userService.getUserList(request);
    PageInfo<UserDTO> pageInfo = new PageInfo<>(userList);
    
    DataListDTO<UserDTO> result = DataListDTO.<UserDTO>builder()
            .dataList(pageInfo.getList())
            .dataCount((int) pageInfo.getTotal())
            .build();
    
    return MsResponse.success(result);
}
```

### 10.2 批量操作
```java
@PostMapping("/batch/add")
public MsResponse<List<UserDTO>> batchAddUsers(
        @Valid @RequestBody BatchAddUserRequest request) {
    // 限制批量操作数量
    if (request.getUserList().size() > 100) {
        throw new UserException("21005", "批量添加用户数量不能超过100个");
    }
    
    List<UserDTO> result = userService.batchAddUsers(request);
    return MsResponse.success(result);
}
```

## 11. 接口测试规范

### 11.1 单元测试
```java
@SpringBootTest
@AutoConfigureTestDatabase
class UserControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Test
    void testAddUser() throws Exception {
        AddUserRequest request = new AddUserRequest();
        request.setUserName("testuser");
        request.setPassword("123456");
        
        mockMvc.perform(post("/user/add")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("00000"));
    }
}
```

### 11.2 集成测试
- 使用 TestContainers 进行数据库集成测试
- Mock 外部依赖服务
- 测试覆盖率要求 > 70%

## 12. 接口监控规范

### 12.1 日志记录
```java
@OptLog // 自定义注解记录操作日志
@PostMapping("/add")
public MsResponse<UserDTO> addUser(@Valid @RequestBody AddUserRequest request) {
    long startTime = System.currentTimeMillis();
    try {
        UserDTO result = userService.addUser(request);
        logger.info("添加用户成功, userId: {}, 耗时: {}ms", 
                   result.getUserId(), System.currentTimeMillis() - startTime);
        return MsResponse.success(result);
    } catch (Exception e) {
        logger.error("添加用户失败, request: {}, 耗时: {}ms", 
                    request, System.currentTimeMillis() - startTime, e);
        throw e;
    }
}
```

### 12.2 性能监控
- 接口响应时间监控
- 接口调用频率统计
- 异常率监控
- 慢接口告警
