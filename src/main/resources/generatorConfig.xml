<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<!-- 配置生成器 -->
<generatorConfiguration>

    <!--
        可以用于加载配置项或者配置文件
        resource：配置资源加载地址，使用resource，从classpath开始找，比如com/myproject/generatorConfig.properties
        url：配置资源加载地质，使用URL的方式，比如file:///C:/myfolder/generatorConfig.properties.
        注意，两个属性只能选址一个
     -->
    <!--导入属性配置-->
<!--    <properties resource="datasource.properties"/>-->

    <!--指定特定数据库的jdbc驱动jar包的位置-->
    <!-- 暂时不清楚怎么指定相对路径，只能指定maven仓库中的jar包 -->
    <classPathEntry location="C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.21\mysql-connector-java-8.0.21.jar"/>

    <!--
    context:生成一组对象的环境
    id:必选，上下文id，用于在生成错误时提示
    targetRuntime:
        1，MyBatis3：默认的值，生成基于MyBatis3.x以上版本的内容，包括XXXBySample；
        2，MyBatis3Simple：类似MyBatis3，只是不生成XXXBySample；
-->
    <context id="default" targetRuntime="MyBatis3">

        <!--生成的bean是没有tostring方法的，所以如果要想生成tostring方法的话，需要在generatorConfig.xml中加上如下配置-->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"></plugin>
        <!-- 该元素最多可以配置1个,可以生成文件的注释 -->
        <commentGenerator>
            <!--阻止生成的注释包含时间戳,默认为false-->
            <property name="suppressDate" value="true"/>
            <!--阻止生成注释,默认为false-->
            <property name="suppressAllComments" value="true"/>
            <!--设置要使用的Java文件的编码-->
            <property name="javaFileEncoding" value="UTF-8"/>
        </commentGenerator>

        <!--jdbc的数据库连接，引入上述的配置文件datasource.properties的内容,这里直接写死也可以 -->
        <jdbcConnection
                driverClass="com.mysql.cj.jdbc.Driver"
                connectionURL="*************************************************************"
                userId="root"
                password="YiyouDev+2021">
        </jdbcConnection>

        <!-- 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- Model模型生成器,用来生成数据库对应的实体类
            targetPackage     指定生成的实体类 所在的包名
            targetProject     指定在该项目下所在的路径
        -->
        <javaModelGenerator targetPackage="com.pdzx.entity" targetProject="D:\eeeyou\workSpace\java\src\main">
            <!-- 是否允许子包，eg：fase路径com.pdzx.entity， true:com.pdzx.entity..[schemaName] -->
            <property name="enableSubPackages" value="false"/>
            <!-- 是否对model添加 构造函数 -->
            <property name="constructorBased" value="false"/>
            <!-- 建立的Model对象是否 不可改变  即生成的Model对象不会有 setter方法，只有构造方法 -->
            <property name="immutable" value="false"/>
            <!-- 设置是否在getter方法中，对String类型字段调用trim()方法 -->
            <!--如果设置为true就会生成类似这样  public void setUsername(String username) {this.username = username == null ? null : username.trim();}-->
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>

        <!--mapper映射文件生成所在的目录 为每一个数据库的表生成对应的SqlMap文件,就是用xml写SQL的方式 -->
        <sqlMapGenerator targetPackage="com.pdzx.mapper" targetProject="D:\eeeyou\workSpace\java\src\main">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        <!--
                生成易于使用的针对Model对象和XML配置文件 的代码,即dao层接口文件
                type="ANNOTATEDMAPPER",基于注解的Mapper接口，不会有对应的XML映射文件
                type="MIXEDMAPPER",XML和注解的混合形式，(上面这种情况中的)SqlProvider注解方法会被XML替代
                type="XMLMAPPER",所有的方法都在XML中，接口调用依赖XML文件。
        -->
        <!-- targetPackage：mapper接口dao生成的位置 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.pdzx.dao" targetProject="D:\eeeyou\workSpace\java\src\main">
            <!-- enableSubPackages:是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false" />
        </javaClientGenerator>

        <!--
        tableName为对应的数据库表
         domainObjectName是要生成的实体类
          enable*ByExample是否生成 example类   -->
        <table tableName="label_manage_scope"
               domainObjectName="LabelManageScope"
               enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false"
               enableSelectByExample="false"
               selectByExampleQueryId="false">
            <!-- 上面的属性都可以使用子标签形式表示 -->
            <!-- 是否使用真实字段名，设置为false将自动驼峰转换 -->
            <property name="useActualColumnNames" value="false" />
        </table>

    </context>

</generatorConfiguration>