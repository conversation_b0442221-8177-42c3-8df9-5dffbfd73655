<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.DepartmentInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.DepartmentInfoDAO">
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="parent_id" property="parentId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        department_id
        ,
        department_name,
        parent_id,
        extra_content,
        sort,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.departmentId">
                AND department_id = #{condition.departmentId}
            </if>
            <if test="null != condition.departmentName">
                AND department_name = #{condition.departmentName}
            </if>
            <if test="null != condition.parentId">
                AND parent_id = #{condition.parentId}
            </if>
            <if test="null != condition.departmentIdList">
                <foreach collection="condition.departmentIdList" item="departmentId" open="AND department_id IN ("
                         close=")"
                         separator=",">
                    #{departmentId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addDepartment" parameterType="com.microservice.user.entity.dao.DepartmentInfoDAO">
        INSERT INTO department_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != departmentId">
                department_id,
            </if>
            <if test="null != departmentName">
                department_name,
            </if>
            <if test="null != parentId">
                parent_id,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != sort">
                sort,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != departmentId">
                #{departmentId},
            </if>
            <if test="null != departmentName">
                #{departmentName},
            </if>
            <if test="null != parentId">
                #{parentId},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != sort">
                #{sort},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteDepartment">
        DELETE
        FROM department_info
        <include refid="whereClause"/>
    </delete>

    <update id="updateDepartment">
        UPDATE department_info
        <set>
            <if test="null != value.departmentName">department_name = #{value.departmentName},</if>
            <if test="null != value.parentId">parent_id = #{value.parentId},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.sort">sort = #{value.sort},</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <include refid="whereClause"/>
    </update>


    <select id="getDepartment" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM department_info
        <include refid="whereClause"/>
        LIMIT 1
    </select>

    <select id="getDepartmentList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM department_info
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getDepartmentCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM department_info
        <include refid="whereClause"/>
    </select>

</mapper>