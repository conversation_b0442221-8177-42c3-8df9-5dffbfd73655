<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.RosterTeamMapper">
    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.RosterTeamDAO">
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="is_show" jdbcType="TINYINT" property="isShow"/>
        <result column="is_system" jdbcType="TINYINT" property="isSystem"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="field" jdbcType="VARCHAR" property="field"/>
        <result column="count_max" jdbcType="INTEGER" property="countMax"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="company_id" jdbcType="CHAR" property="companyId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="app_channel_id" jdbcType="CHAR" property="appChannelId"/>
        <result column="data_channel_id" jdbcType="CHAR" property="dataChannelId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,field,title, is_show,is_system,sort,count_max,remark,company_id, create_time, update_time, app_channel_id, data_channel_id, is_delete
    </sql>
    
    <sql id="whereClause">
        <where>
            <if test="condition.isDelete != null">
                and is_delete = #{condition.isDelete}
            </if>
            <if test="condition.companyId != null">
                and company_id = #{condition.companyId,jdbcType=CHAR}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="condition.companyId != null and condition.companyId != ''">
                and company_id = #{condition.companyId}
            </if>
            <if test="condition.isShow != null ">
                and is_show = #{condition.isShow}
            </if>
            <if test="condition.isSystem != null ">
                and is_system = #{condition.isSystem}
            </if>
            <if test="condition.name != null and condition.name != ''">
                and title = #{condition.name}
            </if>
            <if test="condition.startDateTime != null and condition.startDateTime != ''">
                and create_time <![CDATA[ >= ]]> #{condition.startDateTime}
            </if>
            <if test="condition.endDateTime != null and condition.endDateTime != ''">
                and create_time <![CDATA[ <= ]]> #{condition.endDateTime}
            </if>
            <if test="condition.primaryKeyIds != null">
                <foreach collection="condition.primaryKeyIds" item="id" open="AND id IN ("
                         close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="condition.rosterFields != null">
                <foreach collection="condition.rosterFields" item="field" open="AND field IN ("
                         close=")"
                         separator=",">
                    #{field}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from roster_team
        where id = #{id}
    </select>

    <select id="selectRosterTeamList" resultType="com.microservice.user.entity.dao.RosterTeamDAO"
            parameterType="com.microservice.user.entity.condition.GetRosterConfigCondition">
        select
        <include refid="Base_Column_List"/>
        from roster_team
        <include refid="whereClause"/>
        ORDER BY sort
        <if test="limit > 0">
            LIMIT #{start}, #{limit}
        </if>
    </select>
    
    <select id="countRosterTeam" resultType="java.lang.Integer"
            parameterType="com.microservice.user.entity.condition.GetRosterConfigCondition">
       select count(*) from roster_team
        <include refid="whereClause"/>
    </select>

    <select id="getSortMax" resultType="java.lang.Integer">
        SELECT MAX(sort) from roster_team
        <include refid="whereClause"/>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from roster_team
        where id = #{id}
    </delete>

    <insert id="insertRosterTeam" parameterType="com.microservice.user.entity.dao.RosterTeamDAO" useGeneratedKeys="true"
            keyProperty="id">
        insert into roster_team
        (id,
         field,
         title,
         is_show,
         is_system,
         sort,
         company_id,
         count_max,
         remark,
         create_time,
         update_time,
         app_channel_id,
         data_channel_id,
         is_delete)
        values (#{id},
                #{field},
                #{title},
                #{isShow},
                #{isSystem},
                #{sort},
                #{companyId},
                #{countMax},
                #{remark},
                #{createTime},
                #{updateTime},
                #{appChannelId},
                #{dataChannelId},
                #{isDelete})
    </insert>

    <update id="updateRosterTeam" parameterType="com.microservice.user.entity.dao.RosterTeamDAO">
        update roster_team
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="isShow != null">
                is_show = #{isShow},
            </if>
            <if test="isSystem != null">
                is_system = #{isSystem},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="countMax != null">
                count_max = #{countMax},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>