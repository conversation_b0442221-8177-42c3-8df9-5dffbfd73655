<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.GroupDataRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.GroupDataRelationDAO">
        <result column="data" property="data"/>
        <result column="group_id" property="groupId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        data
        ,
        group_id,
        create_time
    </sql>

    <insert id="insert" parameterType="com.microservice.user.entity.dao.GroupDataRelationDAO">
        INSERT INTO group_data_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != data">
                data,
            </if>
            <if test="null != groupId">
                group_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != data">
                #{data},
            </if>
            <if test="null != groupId">
                #{groupId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
        </trim>
    </insert>

    <delete id="deleteGroupDataRelation">
        DELETE
        FROM group_data_relation
        <where>
            <if test="null != condition.groupId">
                AND group_id = #{condition.groupId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != condition.data">
                AND data = #{condition.data}
            </if>
            <if test="null != condition.dataList">
                <foreach collection="condition.dataList" item="data" open="AND data IN (" close=")"
                         separator=",">
                    #{data}
                </foreach>
            </if>
        </where>
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.GroupDataRelationDAO">
        UPDATE group_data_relation
        <set>
            <if test="null != data">data = #{data},</if>
            <if test="null != groupId">group_id = #{groupId},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getGDRList" resultType="com.microservice.user.entity.dao.GroupDataRelationDAO">
        SELECT
        gdr.* , igr.identity_id AS identityId
        FROM
        group_data_relation gdr
        INNER JOIN identity_group_relation igr ON gdr.group_id = igr.group_id
        <where>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND igr.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.identityId">
                AND igr.identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND igr.group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != condition.groupId">
                AND igr.group_id = #{condition.groupId}
            </if>
            <if test="null != condition.data">
                AND gdr.data = #{condition.data}
            </if>
        </where>
    </select>

    <insert id="addGroupDataRelationList">
        INSERT INTO group_data_relation
        (group_id, data)
        VALUES
        <foreach collection="dataList" item="data" index="index" separator=",">
            (#{data.groupId}, #{data.data})
        </foreach>
    </insert>

    <select id="getGroupDataRelationList" resultType="com.microservice.user.entity.dao.GroupDataRelationDAO">
        SELECT * FROM group_data_relation
        <where>
            <if test="null != condition.groupId">
                AND group_id = #{condition.groupId}
            </if>
            <if test="null != condition.data">
                AND data = #{condition.data}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>