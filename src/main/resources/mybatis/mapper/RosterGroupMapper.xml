<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.RosterGroupMapper">
    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.RosterGroupDAO">
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="roster_rule_team_id" jdbcType="CHAR" property="rosterRuleTeamId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="field" jdbcType="VARCHAR" property="field"/>
        <result column="is_add" jdbcType="INTEGER" property="isAdd"/>
        <result column="is_System" jdbcType="INTEGER" property="isSystem"/>
        <result column="count_max" jdbcType="INTEGER" property="countMax"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="is_show" jdbcType="TINYINT" property="isShow"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="company_id" jdbcType="CHAR" property="companyId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="app_channel_id" jdbcType="CHAR" property="appChannelId"/>
        <result column="data_channel_id" jdbcType="CHAR" property="dataChannelId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,roster_rule_team_id,`name`, field,is_system,is_add,count_max, sort, is_show, remark, company_id,
    create_time, update_time, app_channel_id, data_channel_id, is_delete
    </sql>

    <sql id="whereClause">
        <where>
            <if test="condition.isDelete != null">
                and is_delete = #{condition.isDelete}
            </if>
            <if test="condition.companyId != null">
                and company_id = #{condition.companyId,jdbcType=CHAR}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="condition.isShow != null ">
                and is_show = #{condition.isShow}
            </if>
            <if test="condition.isSystem != null ">
                and is_systm = #{condition.isSystem}
            </if>
            <if test="condition.name != null and condition.name != ''">
                and `name` = #{condition.name}
            </if>
            <if test="condition.startDateTime != null and condition.startDateTime != ''">
                and create_time <![CDATA[ >= ]]> #{condition.startDateTime}
            </if>
            <if test="condition.endDateTime != null and condition.endDateTime != ''">
                and create_time <![CDATA[ <= ]]> #{condition.endDateTime}
            </if>
            <if test="condition.primaryKeyIds != null">
                <foreach collection="condition.primaryKeyIds" item="id" open="AND id IN ("
                         close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="condition.rosterTeamIds != null">
                <foreach collection="condition.rosterTeamIds" item="rosterTeamId" open="AND roster_rule_team_id IN ("
                         close=")"
                         separator=",">
                    #{rosterTeamId}
                </foreach>
            </if>
            <if test="condition.rosterFields != null">
                <foreach collection="condition.rosterFields" item="field" open="AND field IN ("
                         close=")"
                         separator=",">
                    #{field}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from roster_group
        where id = #{id}
    </select>

    <select id="selectRosterGroupList" resultType="com.microservice.user.entity.dao.RosterGroupDAO"
            parameterType="com.microservice.user.entity.condition.GetRosterConfigCondition">
        select
        <include refid="Base_Column_List"/>
        from roster_group
        <include refid="whereClause"/>
        ORDER BY sort
        <if test="limit > 0">
            limit #{start},#{limit}
        </if>
    </select>

    <select id="countRosterGroup" resultType="java.lang.Integer"
            parameterType="com.microservice.user.entity.condition.GetRosterConfigCondition">
        select count(*)
        from roster_group
        <include refid="whereClause"/>
    </select>

    <select id="getSortMax" resultType="java.lang.Integer">
        SELECT MAX(sort) from roster_group
        <include refid="whereClause"/>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from roster_group
        where id = #{id}
    </delete>

    <insert id="insertRosterGroup" parameterType="com.microservice.user.entity.dao.RosterGroupDAO" useGeneratedKeys="true" keyProperty="id">
        insert into roster_group
            (id,
             roster_rule_team_id,
             `name`,
             field,
             is_system,
             is_add,
             count_max,
             sort,
             is_show,
             remark,
             company_id,
             create_time,
             update_time,
             app_channel_id,
             data_channel_id,
             is_delete)
        values (#{id},
                #{rosterRuleTeamId},
                #{name},
                #{field},
                #{isSystem},
                #{isAdd},
                #{countMax},
                #{sort},
                #{isShow},
                #{remark},
                #{companyId},
                #{createTime},
                #{updateTime},
                #{appChannelId},
                #{dataChannelId},
                #{isDelete})
    </insert>

    <update id="updateRosterGroup" parameterType="com.microservice.user.entity.dao.RosterGroupDAO">
        update roster_group
        <set>
            <if test="rosterRuleTeamId != null">
                roster_rule_team_id = #{rosterRuleTeamId},
            </if>
            <if test="name != null and name != ''">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="countMax != null">
                count_max = #{countMax,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="isShow != null">
                is_show = #{isShow,jdbcType=TINYINT},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null and companyId != ''">
                company_id = #{companyId,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>