<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.CompanyInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.CompanyInfoDAO">
        <result column="company_id" property="companyId"/>
        <result column="company_name" property="companyName"/>
        <result column="parent_id" property="parentId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="sort" property="sort"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        company_id
        ,
        company_name,
        parent_id,
        extra_content,
        sort,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
    </sql>

    <sql id="customizeColumnList">
        <choose>
            <when test="null != columnList and columnList.size() > 0">
                <foreach collection="columnList" item="column" separator=",">
                    <![CDATA[ ${column} ]]>
                </foreach>
            </when>
            <otherwise>
                company_id
                ,
                company_name,
                parent_id,
                extra_content,
                sort,
                app_channel_id,
                data_channel_id,
                create_time,
                update_time
            </otherwise>
        </choose>
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.companyId">
                AND company_id = #{condition.companyId}
            </if>
            <if test="null != condition.parentId">
                AND parent_id = #{condition.parentId}
            </if>
            <if test="null != condition.companyName">
                AND company_name = #{condition.companyName}
            </if>
            <if test="null != condition.startDateTime">
                <![CDATA[
                AND create_time >= #{condition.startDateTime}
                ]]>
            </if>
            <if test="null != condition.endDateTime">
                <![CDATA[
                AND create_time <= #{condition.endDateTime}
                ]]>
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.companyIdList">
                <foreach collection="condition.companyIdList" item="companyId" open="AND company_id IN (" close=")"
                         separator=",">
                    #{companyId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.extraContentInCondition">
                <foreach collection="condition.extraContentInCondition.entrySet()" index="key" item="valList">
                    AND extra_content ->> '$.${key}' IN
                    <foreach collection="valList" item="item" open="(" close=")"
                             separator=",">
                        #{item}
                    </foreach>
                </foreach>
            </if>
            <if test="condition.applySql != null and condition.applySql != ''">
                and ( ${condition.applySql} )
            </if>
        </where>
    </sql>

    <insert id="addCompany" parameterType="com.microservice.user.entity.dao.CompanyInfoDAO">
        INSERT INTO company_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != companyId">
                company_id,
            </if>
            <if test="null != companyName">
                company_name,
            </if>
            <if test="null != parentId">
                parent_id,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != sort">
                sort,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != companyId">
                #{companyId},
            </if>
            <if test="null != companyName">
                #{companyName},
            </if>
            <if test="null != parentId">
                #{parentId},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != sort">
                #{sort},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteCompany">
        DELETE
        FROM company_info
        <include refid="whereClause"/>
    </delete>

    <update id="updateCompany" parameterType="com.microservice.user.entity.dao.CompanyInfoDAO">
        UPDATE company_info
        <set>
            <if test="null != value.companyId">company_id = #{value.companyId},</if>
            <if test="null != value.companyName">company_name = #{value.companyName},</if>
            <if test="null != value.parentId">parent_id = #{value.parentId},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.sort">sort = #{value.sort},</if>
            <if test="null != value.appChannelId">app_channel_id = #{value.appChannelId},</if>
            <if test="null != value.dataChannelId">data_channel_id = #{value.dataChannelId},</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <include refid="whereClause"/>
    </update>


    <select id="getCompany" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM company_info
        <include refid="whereClause"/>
        LIMIT 1
    </select>

    <select id="getCompanyList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM company_info
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getCompanyMapList" resultType="java.util.Map">
        SELECT
        <include refid="customizeColumnList"/>
        FROM company_info
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getCompanyCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM company_info
        <include refid="whereClause"/>
    </select>


    <select id="getCompanyListByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        company_info
        <if test="list != null and !list.isEmpty()">
            WHERE company_id IN
            <foreach collection="list" item="companyId" open="(" close=")" separator=",">
                #{companyId}
            </foreach>
        </if>
        <if test="list == null or list.isEmpty()">
            WHERE 1 = 0
        </if>
    </select>
    <select id="getAllDescendantCompanies" resultMap="BaseResultMap">
        <if test="_parameter != null and companyId != null and companyId != ''">
            WITH RECURSIVE CompanyHierarchy AS (
            SELECT
            company_id,
            company_name,
            parent_id,
            extra_content,
            sort,
            app_channel_id,
            data_channel_id,
            create_time,
            update_time
            FROM
            company_info
            WHERE
            company_id = #{companyId}

            UNION ALL

            SELECT
            ci.company_id,
            ci.company_name,
            ci.parent_id,
            ci.extra_content,
            ci.sort,
            ci.app_channel_id,
            ci.data_channel_id,
            ci.create_time,
            ci.update_time
            FROM
            company_info ci
            INNER JOIN
            CompanyHierarchy ch ON ci.parent_id = ch.company_id
            )
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            CompanyHierarchy;
        </if>
        <if test="_parameter == null or companyId == null or companyId == ''">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            company_info
            WHERE
            1 = 0;
        </if>
    </select>
</mapper>