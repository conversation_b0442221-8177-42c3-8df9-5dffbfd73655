<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.IdentityInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.IdentityInfoDAO">
        <result column="identity_id" property="identityId"/>
        <result column="identity_name" property="identityName"/>
        <result column="user_id" property="userId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="sort" property="sort"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        identity_id
        ,
        identity_name,
        user_id,
        extra_content,
        sort,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.userId">
                AND user_id = #{condition.userId}
            </if>
            <if test="null != condition.userIdList">
                <foreach collection="condition.userIdList" item="userId" open="AND user_id IN (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
        </where>
    </sql>

    <select id="getIdentityWithDepartmentList" resultMap="BaseResultMap">
        SELECT
        identity_id,
        FROM identity_info
        <include refid="whereClause"/>
        ORDER BY create_time ASC
        LIMIT 1
    </select>

    <insert id="addIdentity" parameterType="com.microservice.user.entity.dao.IdentityInfoDAO">
        INSERT INTO identity_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                identity_id,
            </if>
            <if test="null != identityName">
                identity_name,
            </if>
            <if test="null != userId">
                user_id,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != sort">
                sort,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                #{identityId},
            </if>
            <if test="null != identityName">
                #{identityName},
            </if>
            <if test="null != userId">
                #{userId},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != sort">
                #{sort},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteIdentity">
        DELETE
        FROM identity_info
        <include refid="whereClause"/>
    </delete>

    <update id="updateIdentity" parameterType="com.microservice.user.entity.dao.IdentityInfoDAO">
        UPDATE identity_info
        <set>
            <if test="null != value.identityName">identity_name = #{value.identityName},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.sort">sort = #{value.sort},</if>
            <if test="null != value.appChannelId">app_channel_id = #{value.appChannelId},</if>
            <if test="null != value.dataChannelId">data_channel_id = #{value.dataChannelId},</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <include refid="whereClause"/>
    </update>


    <select id="getIdentity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_info
        <include refid="whereClause"/>
        ORDER BY create_time ASC
        LIMIT 1
    </select>

    <select id="getIdentityList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_info
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getIdentityCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM identity_info
        <include refid="whereClause"/>
    </select>

    <select id="getIdentityWithCompanyList"
            resultType="com.microservice.user.entity.dao.IdentityInfoWithCompanyInfoDAO">
        SELECT
        ii.*,
        ci.company_id AS companyId,
        ci.company_name AS companyName,
        ci.extra_content AS companyExtraContent
        FROM
        identity_info ii
        LEFT JOIN identity_company_relation icr ON ii.identity_id = icr.identity_id
        LEFT JOIN company_info ci ON icr.company_id = ci.company_id
        <where>
            <if test="null != condition.userIdList">
                <foreach collection="condition.userIdList" item="userId" open="AND ii.user_id IN (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
        </where>
        ORDER BY ii.create_time DESC
    </select>

    <select id="getIdentityWithUserAndCompanyList"
            resultType="com.microservice.user.entity.dao.IdentityWithUserAndCompanyDAO">
        SELECT
        ii.identity_id,
        ii.identity_name AS identityName,
        ii.extra_content AS identityExtraContent,
        ii.app_channel_id AS appChannelId,
        ii.data_channel_id AS dataChannelId,
        ii.create_time AS createTime,
        ii.update_time AS updateTime,
        ii.sort AS sort,
        ui.user_id AS userId,
        ui.user_name AS userName,
        ui.extra_content AS userExtraContent,
        ci.company_id AS companyId,
        ci.company_name AS companyName,
        ci.extra_content AS companyExtraContent
        FROM
        identity_info ii
        LEFT JOIN identity_company_relation icr ON ii.identity_id = icr.identity_id
        LEFT JOIN company_info ci ON icr.company_id = ci.company_id
        LEFT JOIN user_info ui ON ui.user_id = ii.user_id
        <where>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND ii.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
        </where>
        ORDER BY ii.create_time DESC
    </select>
    <select id="getIdentityByDepartmentIds" resultType="java.lang.String">
        SELECT
        ii.identity_id
        FROM
        identity_department_relation di
        JOIN identity_info ii ON ii.identity_id = di.identity_id and ii.extra_content ->'$.employeeStatus' = 1
        <where>
            <if test="null != departmentIds">
                <foreach collection="departmentIds" item="departmentId" open="AND di.department_id IN ("
                         close=")"
                         separator=",">
                    #{departmentId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getIdentityByCompanyId" parameterType="string" resultType="com.microservice.user.entity.dao.IdentityByCompanyDAO">
        SELECT A.`identity_id`,
               A.`extra_content` AS `identity_extra_content`,
               C.`user_id`,
               C.`user_name`,
               C.`extra_content` AS `user_extra_content`
        FROM `identity_info` A
                 LEFT JOIN `identity_company_relation` B ON A.`identity_id` = B.`identity_id`
                 LEFT JOIN `user_info` C ON A.`user_id` = C.`user_id`
        WHERE B.`company_id` = #{companyId}
    </select>

    <select id="getIdentityByCompanyIdOrUserId" resultType="com.microservice.user.entity.dao.IdentityInfoWithCompanyInfoDAO">
        SELECT
            ii.*,
            icr.company_id
        FROM
            identity_company_relation icr
            INNER JOIN identity_info ii ON icr.identity_id = ii.identity_id
            INNER JOIN user_info ui ON ii.user_id = ui.user_id
        <where>
            AND icr.company_id = #{companyId}
            <if test="userIdList != null and userIdList.size > 0">
                AND ii.user_id in
                <foreach collection="userIdList" item="userId" close=")" open="(" separator=",">
                    #{userId}
                </foreach>
            </if>
        </where>
        ORDER BY
            ii.create_time DESC
    </select>
    <select id="getIdentityByEmployeeStatus" resultType="java.lang.String">
        SELECT
        ii.identity_id
        FROM
        identity_info ii
        WHERE
        ii.extra_content ->'$.employeeStatus' = 1
        <if test="identityIds != null and identityIds.size > 0">
            AND ii.identity_id in
            <foreach collection="identityIds" item="identityId" close=")" open="(" separator=",">
                #{identityId}
            </foreach>
        </if>
    </select>
</mapper>