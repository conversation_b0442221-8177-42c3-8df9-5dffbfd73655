<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.EntityRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.EntityRelationDAO">
        <result column="entity_id" property="entityId"/>
        <result column="target_entity_id" property="targetIdentityId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        entity_id
        ,
        target_entity_id,
        extra_content,
        create_time,
        update_time
    </sql>

    <insert id="insert" parameterType="com.microservice.user.entity.dao.EntityRelationDAO">
        INSERT INTO entity_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != entityId">
                entity_id,
            </if>
            <if test="null != targetIdentityId">
                target_entity_id,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != entityId">
                #{entityId},
            </if>
            <if test="null != targetIdentityId">
                #{targetIdentityId},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete">
        DELETE
        FROM entity_relation
        WHERE id = #{id}
    </delete>

    <update id="updateEntityRelation">
        UPDATE entity_relation
        <set>
            <if test="null != value.entityId">entity_id = #{value.entityId},</if>
            <if test="null != value.targetIdentityId">target_entity_id = #{value.targetIdentityId},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <where>
            <if test="null != condition.entityId">
                AND entity_id = #{condition.entityId}
            </if>
            <if test="null != condition.targetEntityId">
                AND target_entity_id = #{condition.targetEntityId}
            </if>
            <if test="null != condition.entityIdList">
                <foreach collection="condition.entityIdList" item="entityId" open="AND entity_id IN (" close=")"
                         separator=",">
                    #{entityId}
                </foreach>
            </if>
            <if test="null != condition.targetEntityIdList">
                <foreach collection="condition.targetEntityIdList" item="targetEntityId"
                         open="AND target_entity_id IN (" close=")"
                         separator=",">
                    #{targetEntityId}
                </foreach>
            </if>
        </where>
    </update>


    <select id="getEntityRelation" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM entity_relation
        <where>
            <if test="null != condition.entityId">
                AND entity_id = #{condition.entityId}
            </if>
            <if test="null != condition.targetEntityId">
                AND target_entity_id = #{condition.targetEntityId}
            </if>
        </where>
        LIMIT 1
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM entity_relation
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM entity_relation
    </select>

    <insert id="addEntityRelationList">
        INSERT INTO entity_relation
        (entity_id, target_entity_id, extra_content)
        VALUES
        <foreach collection="entityRelationList" item="entityRelation" index="index" separator=",">
            (#{entityRelation.entityId}, #{entityRelation.targetEntityId}, #{entityRelation.extraContent})
        </foreach>
    </insert>

    <select id="getERWithEntityList" resultType="com.microservice.user.entity.dao.ERWithEntityDAO">
        SELECT
        er.entity_id AS entityId,
        ei1.entity_name AS entityName,
        ei1.type AS entityType,
        er.target_entity_id AS targetEntityId,
        ei2.entity_name AS targetEntityName,
        ei2.type AS targetEntityType,
        er.extra_content AS extraContent,
        er.create_time AS createTime,
        er.update_time AS updateTime
        FROM
        entity_relation er
        LEFT JOIN entity_info ei1 ON er.entity_id = ei1.entity_id
        LEFT JOIN entity_info ei2 ON er.target_entity_id = ei2.entity_id
        <where>
            <if test="null != condition.entityId">
                AND er.entity_id = #{condition.entityId}
            </if>
            <if test="null != condition.targetEntityId">
                AND er.target_entity_id = #{condition.targetEntityId}
            </if>
            <if test="null != condition.entityIdList">
                <foreach collection="condition.entityIdList" item="entityId" open="AND ei1.entity_id IN (" close=")"
                         separator=",">
                    #{entityId}
                </foreach>
            </if>
            <if test="null != condition.targetEntityIdList">
                <foreach collection="condition.targetEntityIdList" item="targetEntityId"
                         open="AND ei2.target_entity_id IN (" close=")"
                         separator=",">
                    #{targetEntityId}
                </foreach>
            </if>
            <if test="null != condition.dataChannelId">
                AND ei1.data_channel_id = #{condition.dataChannelId}
                AND ei2.data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND er.extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getERWithEntityCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        entity_relation er
        LEFT JOIN entity_info ei1 ON er.entity_id = ei1.entity_id
        LEFT JOIN entity_info ei2 ON er.target_entity_id = ei2.entity_id
        <where>
            <if test="null != condition.entityId">
                AND er.entity_id = #{condition.entityId}
            </if>
            <if test="null != condition.targetEntityId">
                AND er.target_entity_id = #{condition.targetEntityId}
            </if>
            <if test="null != condition.entityIdList">
                <foreach collection="condition.entityIdList" item="entityId" open="AND ei1.entity_id IN (" close=")"
                         separator=",">
                    #{entityId}
                </foreach>
            </if>
            <if test="null != condition.targetEntityIdList">
                <foreach collection="condition.targetEntityIdList" item="targetEntityId"
                         open="AND ei2.target_entity_id IN (" close=")"
                         separator=",">
                    #{targetEntityId}
                </foreach>
            </if>
            <if test="null != condition.dataChannelId">
                AND ei1.data_channel_id = #{condition.dataChannelId}
                AND ei2.data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND er.extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </select>
</mapper>