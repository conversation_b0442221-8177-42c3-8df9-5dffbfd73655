<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.TokenInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.TokenInfoDAO">
        <result column="id" property="id"/>
        <result column="token_id" property="tokenId"/>
        <result column="token" property="token"/>
        <result column="user_id" property="userId"/>
        <result column="identity_id" property="identityId"/>
        <result column="ip" property="ip"/>
        <result column="end_timestamp" property="endTimestamp"/>
        <result column="token_status" property="tokenStatus"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        token_id,
        token,
        user_id,
        identity_id,
        ip,
        end_timestamp,
        token_status,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.id">
                AND id = #{condition.id}
            </if>
            <if test="null != condition.userId">
                AND user_id = #{condition.userId}
            </if>
            <if test="null != condition.userIdList">
                <foreach collection="condition.userIdList" item="userId" open="AND user_id IN (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.token">
                AND token = #{condition.token}
            </if>
            <if test="null != condition.tokenList">
                <foreach collection="condition.tokenList" item="token" open="AND token IN (" close=")"
                         separator=",">
                    #{token}
                </foreach>
            </if>
            <if test="null != condition.tokenId">
                AND token_id = #{condition.tokenId}
            </if>
            <if test="null != condition.tokenIdList">
                <foreach collection="condition.tokenIdList" item="tokenId" open="AND token_id IN (" close=")"
                         separator=",">
                    #{tokenId}
                </foreach>
            </if>
            <if test="null != condition.tokenStatus">
                AND token_status = #{condition.tokenStatus}
            </if>
            <if test="null != condition.ip">
                AND ip = #{condition.ip}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
        </where>
    </sql>

    <insert id="addToken" parameterType="com.microservice.user.entity.dao.TokenInfoDAO" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        INSERT INTO token_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != tokenId">
                token_id,
            </if>
            <if test="null != token">
                token,
            </if>
            <if test="null != userId">
                user_id,
            </if>
            <if test="null != identityId">
                identity_id,
            </if>
            <if test="null != ip">
                ip,
            </if>
            <if test="null != endTimestamp">
                end_timestamp,
            </if>
            <if test="null != tokenStatus">
                token_status,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != tokenId">
                #{tokenId},
            </if>
            <if test="null != token">
                #{token},
            </if>
            <if test="null != userId">
                #{userId},
            </if>
            <if test="null != identityId">
                #{identityId},
            </if>
            <if test="null != ip">
                #{ip},
            </if>
            <if test="null != endTimestamp">
                #{endTimestamp},
            </if>
            <if test="null != tokenStatus">
                #{tokenStatus},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteToken">
        DELETE
        FROM token_info
        <include refid="whereClause"/>
    </delete>

    <update id="updateToken" parameterType="com.microservice.user.entity.dao.TokenInfoDAO">
        UPDATE token_info
        <set>
            <if test="null != value.token">token = #{value.token},</if>
            <if test="null != value.userId">user_id = #{value.userId},</if>
            <if test="null != value.identityId">identity_id = #{value.identityId},</if>
            <if test="null != value.ip">ip = #{value.ip},</if>
            <if test="null != value.endTimestamp">end_timestamp = #{value.endTimestamp},</if>
            <if test="null != value.tokenStatus">token_status = #{value.tokenStatus},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <include refid="whereClause"/>
    </update>


    <select id="getToken" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM token_info
        <include refid="whereClause"/>
        ORDER BY create_time DESC, `id` DESC
        LIMIT 1
    </select>

    <select id="getTokenList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM token_info
        <include refid="whereClause"/>
        ORDER BY `create_time` DESC, `id` DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getLastUserTokenListGroupByUserId" resultMap="BaseResultMap">
        SELECT *
        FROM token_info t1
        RIGHT JOIN (SELECT MAX(id) max_id, user_id FROM token_info GROUP BY user_id) t2
        ON t1.user_id = t2.user_id
        AND t1.id = t2.max_id
        <where>
            <if test="null != condition.userIdList">
                <foreach collection="condition.userIdList" item="userId" open="AND t1.user_id IN (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
        </where>
        ORDER BY t1.token_status ASC, t1.create_time DESC, t1.id DESC
    </select>

    <select id="getLastUserTokenListGroupByUserIdAndAppChannelId" resultMap="BaseResultMap">
        SELECT *
        FROM token_info t1
        RIGHT JOIN (SELECT MAX(id) max_id, user_id FROM token_info GROUP BY user_id, app_channel_id) t2
        ON t1.user_id = t2.user_id
        AND t1.id = t2.max_id
        <where>
            <if test="null != condition.userIdList">
                <foreach collection="condition.userIdList" item="userId" open="AND t1.user_id IN (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="null != condition.appChannelId">
                AND t1.app_channel_id = #{condition.appChannelId}
            </if>
        </where>
        ORDER BY t1.token_status ASC, t1.create_time DESC, t1.id DESC
    </select>

    <select id="getLastUserTokenListGroupByIdentityId" resultMap="BaseResultMap">
        SELECT *
        FROM token_info t1
        RIGHT JOIN (SELECT MAX(id) max_id, identity_id FROM token_info GROUP BY identity_id) t2
        ON t1.identity_id = t2.identity_id
        AND t1.id = t2.max_id
        <where>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND t1.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
        </where>
        ORDER BY t1.token_status ASC, t1.create_time DESC, t1.id DESC
    </select>

    <select id="getLastUserTokenListGroupByIdentityIdAndAppChannelId" resultMap="BaseResultMap">
        SELECT *
        FROM token_info t1
        RIGHT JOIN (SELECT MAX(id) max_id, identity_id FROM token_info GROUP BY identity_id, app_channel_id) t2
        ON t1.identity_id = t2.identity_id
        AND t1.id = t2.max_id
        <where>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND t1.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.appChannelId">
                AND t1.app_channel_id = #{condition.appChannelId}
            </if>
        </where>
        ORDER BY t1.token_status ASC, t1.create_time DESC, t1.id DESC
    </select>

    <select id="getTokenCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM `token_info`
        <include refid="whereClause"/>
    </select>
</mapper>