<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.CompanyPositionRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.CompanyPositionRelationDAO">
        <result column="company_id" property="companyId"/>
        <result column="position_id" property="positionId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        company_id
        ,
        position_id,
        create_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.companyId">
                AND company_id = #{condition.companyId}
            </if>
            <if test="null != condition.positionId">
                AND position_id = #{condition.positionId}
            </if>
            <if test="null != condition.companyIdList">
                <foreach collection="condition.companyIdList" item="companyId" open="AND company_id IN ("
                         close=")"
                         separator=",">
                    #{companyId}
                </foreach>
            </if>
            <if test="null != condition.positionIdList">
                <foreach collection="condition.positionIdList" item="positionId" open="AND position_id IN ("
                         close=")"
                         separator=",">
                    #{positionId}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="getCPRWhereClause">
        <where>
            <if test="null != condition.companyId">
                AND cpr.company_id = #{condition.companyId}
            </if>
            <if test="null != condition.positionId">
                AND cpr.position_id = #{condition.positionId}
            </if>
            <if test="null != condition.companyIdList">
                <foreach collection="condition.companyIdList" item="companyId" open="AND cpr.company_id IN ("
                         close=")"
                         separator=",">
                    #{companyId}
                </foreach>
            </if>
            <if test="null != condition.positionIdList">
                <foreach collection="condition.positionIdList" item="positionId" open="AND cpr.position_id IN ("
                         close=")"
                         separator=",">
                    #{positionId}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addCompanyPositionRelation" parameterType="com.microservice.user.entity.dao.CompanyPositionRelationDAO">
        INSERT INTO company_position_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != companyId">
                company_id,
            </if>
            <if test="null != positionId">
                position_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != companyId">
                #{companyId},
            </if>
            <if test="null != positionId">
                #{positionId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
        </trim>
    </insert>

    <delete id="delete">
        DELETE
        FROM company_position_relation
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.CompanyPositionRelationDAO">
        UPDATE company_position_relation
        <set>
            <if test="null != companyId">company_id = #{companyId},</if>
            <if test="null != positionId">position_id = #{positionId},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM company_position_relation
        WHERE id = #{id}
    </select>

    <select id="getCompanyPositionRelationList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM company_position_relation
        <include refid="whereClause"/>
    </select>

    <select id="getCompanyPositionRelationCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM company_position_relation
        <include refid="whereClause"/>
    </select>

    <select id="getCPRWithPositionList" resultType="com.microservice.user.entity.dao.CPRWithPositionDAO">
        SELECT *
        FROM company_position_relation cpr
        INNER JOIN position_info pi ON cpr.position_id = pi.position_id
        <include refid="getCPRWhereClause"/>
        ORDER BY pi.sort ASC, pi.create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getCPRWithPositionCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM company_position_relation cpr
        INNER JOIN position_info pi ON cpr.position_id = pi.position_id
        <include refid="getCPRWhereClause"/>
    </select>

    <delete id="deleteCompanyPositionRelation">
        DELETE FROM `company_position_relation`
        <include refid="whereClause"/>
    </delete>
</mapper>