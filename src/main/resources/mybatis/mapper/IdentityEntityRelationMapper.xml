<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.IdentityEntityRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.IdentityEntityRelationDAO">
        <result column="identity_id" property="identityId"/>
        <result column="entity_id" property="entityId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        identity_id
        ,
        entity_id,
        extra_content,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.entityId">
                AND entity_id = #{condition.entityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.entityIdList">
                <foreach collection="condition.entityIdList" item="entityId" open="AND entity_id IN (" close=")"
                         separator=",">
                    #{entityId}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.microservice.user.entity.dao.IdentityEntityRelationDAO">
        INSERT INTO identity_entity_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != identityId and '' != identityId">
                identity_id,
            </if>
            <if test="null != entityId and '' != entityId">
                entity_id,
            </if>
            <if test="null != extraContent and '' != extraContent">
                extra_content,
            </if>
            <if test="null != createTime and '' != createTime">
                create_time,
            </if>
            <if test="null != updateTime and '' != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != identityId and '' != identityId">
                #{identityId},
            </if>
            <if test="null != entityId and '' != entityId">
                #{entityId},
            </if>
            <if test="null != extraContent and '' != extraContent">
                #{extraContent},
            </if>
            <if test="null != createTime and '' != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime and '' != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteIdentityEntityRelation">
        DELETE
        FROM identity_entity_relation
        <include refid="whereClause"/>
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.IdentityEntityRelationDAO">
        UPDATE identity_entity_relation
        <set>
            <if test="null != identityId and '' != identityId">identity_id = #{identityId},</if>
            <if test="null != entityId and '' != entityId">entity_id = #{entityId},</if>
            <if test="null != extraContent and '' != extraContent">extra_content = #{extraContent},</if>
            <if test="null != createTime and '' != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime and '' != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getIdentityEntityRelationList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_entity_relation
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getIdentityEntityRelationCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM identity_entity_relation
        <include refid="whereClause"/>
    </select>

    <insert id="addIdentityEntityRelationList">
        INSERT INTO identity_entity_relation
        (identity_id, entity_id)
        VALUES
        <foreach collection="identityEntityRelationList" item="identityEntityRelation" index="index" separator=",">
            (#{identityEntityRelation.identityId}, #{identityEntityRelation.entityId})
        </foreach>
    </insert>
</mapper>