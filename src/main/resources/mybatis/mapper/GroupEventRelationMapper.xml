<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.GroupEventRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.GroupEventRelationDAO">
        <result column="event_id" property="eventId"/>
        <result column="group_id" property="groupId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        event_id
        ,
        group_id,
        create_time
    </sql>

    <insert id="insert" parameterType="com.microservice.user.entity.dao.GroupEventRelationDAO">
        INSERT INTO group_event_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != eventId">
                event_id,
            </if>
            <if test="null != groupId">
                group_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != eventId">
                #{eventId},
            </if>
            <if test="null != groupId">
                #{groupId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
        </trim>
    </insert>

    <delete id="deleteGroupEventRelation">
        DELETE
        FROM group_event_relation
        <where>
            <if test="null != condition.groupId">
                AND group_id = #{condition.groupId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != condition.eventId">
                AND event_id = #{condition.eventId}
            </if>
            <if test="null != condition.eventIdList">
                <foreach collection="condition.eventIdList" item="eventId" open="AND event_id IN (" close=")"
                         separator=",">
                    #{eventId}
                </foreach>
            </if>
        </where>
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.GroupEventRelationDAO">
        UPDATE group_event_relation
        <set>
            <if test="null != eventId">event_id = #{eventId},</if>
            <if test="null != groupId">group_id = #{groupId},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM group_event_relation
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM group_event_relation
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM group_event_relation
    </select>

    <select id="getGERList" resultType="com.microservice.user.entity.dao.GroupEventRelationDAO">
        SELECT
        ger.* , igr.identity_id AS identityId
        FROM
        group_event_relation ger
        INNER JOIN identity_group_relation igr ON ger.group_id = igr.group_id
        <where>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND igr.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.identityId">
                AND igr.identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND igr.group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != condition.groupId">
                AND igr.group_id = #{condition.groupId}
            </if>
            <if test="null != condition.eventId">
                AND ger.event_id = #{condition.eventId}
            </if>
        </where>
    </select>

    <insert id="addGroupEventRelationList">
        INSERT INTO group_event_relation
        (group_id, event_id)
        VALUES
        <foreach collection="dataList" item="data" index="index" separator=",">
            (#{data.groupId}, #{data.eventId})
        </foreach>
    </insert>

    <select id="getGroupEventRelationList" resultType="com.microservice.user.entity.dao.GroupEventRelationDAO">
        SELECT * FROM group_event_relation
        <where>
            <if test="null != condition.groupId">
                AND group_id = #{condition.groupId}
            </if>
            <if test="null != condition.eventId">
                AND event_id = #{condition.eventId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>