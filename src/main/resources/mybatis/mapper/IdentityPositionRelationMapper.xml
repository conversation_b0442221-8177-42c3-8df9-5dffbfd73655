<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.IdentityPositionRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.IdentityPositionRelationDAO">
        <result column="identity_id" property="identityId"/>
        <result column="position_id" property="positionId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        identity_id
        ,
        position_id,
        extra_content,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.positionId">
                AND position_id = #{condition.positionId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.positionIdList">
                <foreach collection="condition.positionIdList" item="positionId" open="AND position_id IN (" close=")"
                         separator=",">
                    #{positionId}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="iprWhereClause">
        <where>
            <if test="null != condition.positionId">
                AND ipr.position_id = #{condition.positionId}
            </if>
            <if test="null != condition.positionIdList">
                <foreach collection="condition.positionIdList" item="positionId" open="AND ipr.position_id IN ("
                         close=")"
                         separator=",">
                    #{positionId}
                </foreach>
            </if>
            <if test="null != condition.identityId">
                AND ipr.identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND ipr.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND ii.extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.extraContentInCondition">
                <foreach collection="condition.extraContentInCondition.entrySet()" index="key" item="valList">
                    AND ii.extra_content ->> '$.${key}' IN
                    <foreach collection="valList" item="item" open="(" close=")"
                             separator=",">
                        #{item}
                    </foreach>
                </foreach>
            </if>
            <if test="null != condition.appChannelId">
                AND ii.app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND ii.data_channel_id = #{condition.dataChannelId}
            </if>
        </where>
    </sql>

    <insert id="insert" parameterType="com.microservice.user.entity.dao.IdentityPositionRelationDAO">
        INSERT INTO identity_position_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                identity_id,
            </if>
            <if test="null != positionId">
                position_id,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                #{identityId},
            </if>
            <if test="null != positionId">
                #{positionId},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteIdentityPositionRelation">
        DELETE
        FROM identity_position_relation
        <include refid="whereClause"/>
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.IdentityPositionRelationDAO">
        UPDATE identity_position_relation
        <set>
            <if test="null != identityId">identity_id = #{identityId},</if>
            <if test="null != positionId">position_id = #{positionId},</if>
            <if test="null != extraContent">extra_content = #{extraContent},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_position_relation
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_position_relation
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM identity_position_relation
    </select>

    <select id="getIPRWithPositionList" resultType="com.microservice.user.entity.dao.IPRWithPositionDAO">
        SELECT ipr.*,
        pi.*,
        ii.user_id
        FROM identity_position_relation ipr
        INNER JOIN identity_info ii ON ipr.identity_id = ii.identity_id
        INNER JOIN position_info pi ON ipr.position_id = pi.position_id
        <where>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND ipr.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.positionIdList">
                <foreach collection="condition.positionIdList" item="positionId" open="AND ipr.position_id IN ("
                         close=")"
                         separator=",">
                    #{positionId}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="addIdentityPositionRelationList">
        INSERT INTO identity_position_relation
        (identity_id, position_id)
        VALUES
        <foreach collection="identityPositionRelationList" item="identityPositionRelation" index="index"
                 separator=",">
            (#{identityPositionRelation.identityId}, #{identityPositionRelation.positionId})
        </foreach>
    </insert>

    <select id="getIdentityPositionRelationList"
            resultType="com.microservice.user.entity.dao.IdentityPositionRelationDAO">
        SELECT * FROM
        identity_position_relation
        <include refid="whereClause"/>
        ORDER BY create_time DESC
    </select>

    <select id="getIdentityPositionRelationCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        identity_position_relation
        <include refid="whereClause"/>
    </select>

    <select id="getIPRWithIdentityList" resultType="com.microservice.user.entity.dao.IPRWithIdentityDAO">
        SELECT ii.*, ipr.position_id
        FROM identity_position_relation ipr
        INNER JOIN identity_info ii ON ipr.identity_id = ii.identity_id
        <include refid="iprWhereClause"/>
        ORDER BY ii.create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getIPRWithIdentityCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM identity_position_relation ipr
        INNER JOIN identity_info ii ON ipr.identity_id = ii.identity_id
        <include refid="iprWhereClause"/>
    </select>

    <select id="getPositionList" resultType="com.microservice.user.entity.dao.IPRWithPositionDAO">
        SELECT
        ipr.*,
        pi.*,
        ii.user_id
        FROM
            identity_position_relation ipr
            INNER JOIN identity_info ii ON ipr.identity_id = ii.identity_id
            INNER JOIN position_info pi ON ipr.position_id = pi.position_id
            <if test="identityIdList == null or identityIdList.size == 0">
                INNER JOIN identity_company_relation icr on icr.identity_id = ipr.identity_id
            </if>
        <where>
            <if test="identityIdList == null or identityIdList.size == 0">
                AND icr.company_id = #{companyId}
            </if>
            <if test="identityIdList != null and identityIdList.size > 0">
                AND ipr.identity_id IN
                <foreach collection="identityIdList" item="identityId" open="(" close=")" separator=",">
                    #{identityId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>