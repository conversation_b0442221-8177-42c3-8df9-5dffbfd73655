<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.DepartmentPositionRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.DepartmentPositionRelationDAO">
        <result column="department_id" property="departmentId"/>
        <result column="position_id" property="positionId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        department_id
        ,
        position_id,
        create_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.departmentId">
                AND department_id = #{condition.departmentId}
            </if>
            <if test="null != condition.positionId">
                AND position_id = #{condition.positionId}
            </if>
            <if test="null != condition.departmentIdList">
                <foreach collection="condition.departmentIdList" item="departmentId" open="AND department_id IN ("
                         close=")"
                         separator=",">
                    #{departmentId}
                </foreach>
            </if>
            <if test="null != condition.positionIdList">
                <foreach collection="condition.positionIdList" item="positionId" open="AND position_id IN ("
                         close=")"
                         separator=",">
                    #{positionId}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addDepartmentPositionRelation" parameterType="com.microservice.user.entity.dao.DepartmentPositionRelationDAO">
        INSERT INTO department_position_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != departmentId">
                department_id,
            </if>
            <if test="null != positionId">
                position_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != departmentId">
                #{departmentId},
            </if>
            <if test="null != positionId">
                #{positionId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
        </trim>
    </insert>

    <delete id="deleteDepartmentPositionRelation">
        DELETE
        FROM department_position_relation
        <include refid="whereClause"/>
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.DepartmentPositionRelationDAO">
        UPDATE department_position_relation
        <set>
            <if test="null != departmentId">department_id = #{departmentId},</if>
            <if test="null != positionId">position_id = #{positionId},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM department_position_relation
        WHERE id = #{id}
    </select>

    <select id="getDepartmentPositionRelationList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM department_position_relation
        <include refid="whereClause"/>
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM department_position_relation
        <include refid="whereClause"/>
    </select>

    <select id="getDPRWithPositionList" resultType="com.microservice.user.entity.dao.DPRWithPositionDAO">
        SELECT *
        FROM department_position_relation dpr
        INNER JOIN position_info pi ON dpr.position_id = pi.position_id
        <where>
            <if test="null != condition.departmentId">
                AND dpr.department_id = #{condition.departmentId}
            </if>
            <if test="null != condition.departmentIdList">
                <foreach collection="condition.departmentIdList" item="departmentId" open="AND dpr.department_id IN ("
                         close=")"
                         separator=",">
                    #{departmentId}
                </foreach>
            </if>
            <if test="null != condition.positionId">
                AND dpr.position_id = #{condition.positionId}
            </if>
            <if test="null != condition.positionIdList">
                <foreach collection="condition.positionIdList" item="positionId" open="AND dpr.position_id IN ("
                         close=")"
                         separator=",">
                    #{positionId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getDPRWithPositionCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM department_position_relation dpr
        INNER JOIN position_info pi ON dpr.position_id = pi.position_id
        <where>
            <if test="null != condition.departmentId">
                AND dpr.department_id = #{condition.departmentId}
            </if>
        </where>
    </select>
</mapper>