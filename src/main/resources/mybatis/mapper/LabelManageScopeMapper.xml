<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.LabelManageScopeMapper">
  <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.LabelManageScopeDAO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="label_id" jdbcType="INTEGER" property="labelId" />
    <result column="label_relation_id" jdbcType="INTEGER" property="labelRelationId" />
    <result column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="relation_type" jdbcType="BIT" property="relationType" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="extra_content" jdbcType="LONGVARCHAR" property="extraContent" />
  </resultMap>
  <sql id="Base_Column_List">
    id, label_id, label_relation_id, relation_id, relation_type, status, create_time, 
    update_time, extra_content
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from label_manage_scope
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByLabelRelationId"
          resultType="com.microservice.user.entity.dto.ms.label.LabelManageScopeDTO">
    SELECT
      relationId,relationType,
      NAME  manageScope
    FROM
      (
        SELECT
          lr.relation_id relationId,lr.relation_type relationType,
          ci.company_name name
        FROM
          label_manage_scope lr
            JOIN company_info ci ON ci.company_id = lr.relation_id  AND lr.relation_type = 3
        WHERE lr.label_relation_id = #{labelRelationId,jdbcType=INTEGER} AND lr.STATUS = 1
        UNION
        SELECT
          lr.relation_id relationId,lr.relation_type relationType,
          dr.department_name NAME
        FROM
          label_manage_scope lr
            JOIN department_info dr ON dr.department_id = lr.relation_id  AND lr.relation_type = 1
        WHERE lr.label_relation_id = #{labelRelationId,jdbcType=INTEGER} and lr.STATUS = 1
        UNION
        SELECT
          lr.relation_id relationId,lr.relation_type relationType,
          REPLACE (
                  CASE WHEN iden.extra_content -> '$.stageName' IS NULL OR iden.extra_content -> '$.stageName' = '' THEN
                         CASE WHEN ui.extra_content -> '$.realName' IS NULL OR ui.extra_content -> '$.realName' = '' THEN
                                ui.extra_content -> '$.nickname'
                              ELSE ui.extra_content -> '$.realName'  END
                       ELSE iden.extra_content -> '$.stageName' END,
                  '"',
                  ''
          ) uname
        FROM
          label_manage_scope lr
            JOIN identity_info iden ON iden.identity_id = lr.relation_id AND lr.relation_type = 2
            JOIN user_info ui ON ui.user_id = iden.user_id
        WHERE
          lr.label_relation_id = #{labelRelationId,jdbcType=INTEGER} and lr.STATUS = 1
      ) temp_tab

  </select>
  <select id="selectByRelationId" resultType="com.microservice.user.entity.dto.ms.label.LabelRelationDTO">
    select
        distinct
      lr.relation_id relationId, lr.relation_type relationType
    from
      label_manage_scope lms
       join label_relation lr on lr.id = lms.label_relation_id and lr.status = 1
    where
        1=1
        <if test="null != labelId and labelId!=''">
          AND lms.label_id = #{labelId,jdbcType=VARCHAR}
        </if>
      <if test="null != labelIdList and labelIdList.size > 0">
        <foreach collection="labelIdList" item="labelId" open="AND lms.label_id IN ("
                 close=")"
                 separator=",">
          #{labelId}
        </foreach>
      </if>
      <if test="null != relationIds and relationIds.size > 0">
        <foreach collection="relationIds" item="relationId" open="AND lms.relation_id IN ("
                 close=")"
                 separator=",">
          #{relationId}
        </foreach>
      </if>
       and lms.relation_type = #{relationType,jdbcType=BIT} and lms.status = 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from label_manage_scope
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.microservice.user.entity.dao.LabelManageScopeDAO">
    insert into label_manage_scope (id, label_id, label_relation_id, 
      relation_id, relation_type, status, 
      create_time, update_time, extra_content
      )
    values (#{id,jdbcType=INTEGER}, #{labelId,jdbcType=INTEGER}, #{labelRelationId,jdbcType=INTEGER}, 
      #{relationId,jdbcType=VARCHAR}, #{relationType,jdbcType=BIT}, #{status,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{extraContent,jdbcType=LONGVARCHAR}
      )
  </insert>

  <insert id="insertBatch" parameterType="com.microservice.user.entity.dao.LabelManageScopeDAO">
    insert into label_manage_scope ( label_id, label_relation_id,
                                    relation_id, relation_type, status,
                                    create_time, update_time, extra_content
    ) values
    <foreach collection="labelManageScopeDAOS" item="labelManageScopeDAO" separator=",">
        ( #{labelManageScopeDAO.labelId,jdbcType=INTEGER}, #{labelManageScopeDAO.labelRelationId,jdbcType=INTEGER},
            #{labelManageScopeDAO.relationId,jdbcType=VARCHAR}, #{labelManageScopeDAO.relationType,jdbcType=BIT}, #{labelManageScopeDAO.status,jdbcType=BIT},
            #{labelManageScopeDAO.createTime,jdbcType=TIMESTAMP}, #{labelManageScopeDAO.updateTime,jdbcType=TIMESTAMP}, #{labelManageScopeDAO.extraContent,jdbcType=LONGVARCHAR}
           )
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="com.microservice.user.entity.dao.LabelManageScopeDAO">
    insert into label_manage_scope
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="labelId != null">
        label_id,
      </if>
      <if test="labelRelationId != null">
        label_relation_id,
      </if>
      <if test="relationId != null">
        relation_id,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="extraContent != null">
        extra_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="labelId != null">
        #{labelId,jdbcType=VARCHAR},
      </if>
      <if test="labelRelationId != null">
        #{labelRelationId,jdbcType=INTEGER},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extraContent != null">
        #{extraContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.microservice.user.entity.dao.LabelManageScopeDAO">
    update label_manage_scope
    <set>
      <if test="labelId != null">
        label_id = #{labelId,jdbcType=INTEGER},
      </if>
      <if test="labelRelationId != null">
        label_relation_id = #{labelRelationId,jdbcType=INTEGER},
      </if>
      <if test="relationId != null">
        relation_id = #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        relation_type = #{relationType,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extraContent != null">
        extra_content = #{extraContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.microservice.user.entity.dao.LabelManageScopeDAO">
    update label_manage_scope
    set label_id = #{labelId,jdbcType=INTEGER},
      label_relation_id = #{labelRelationId,jdbcType=INTEGER},
      relation_id = #{relationId,jdbcType=VARCHAR},
      relation_type = #{relationType,jdbcType=BIT},
      status = #{status,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      extra_content = #{extraContent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.microservice.user.entity.dao.LabelManageScopeDAO">
    update label_manage_scope
    set label_id = #{labelId,jdbcType=INTEGER},
      label_relation_id = #{labelRelationId,jdbcType=INTEGER},
      relation_id = #{relationId,jdbcType=VARCHAR},
      relation_type = #{relationType,jdbcType=BIT},
      status = #{status,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateStatusByLabelRelationId">
    update label_manage_scope
    set status = #{status,jdbcType=BIT}
    where label_relation_id = #{labelRelationId,jdbcType=INTEGER}
      and label_id = #{labelId,jdbcType=VARCHAR}
  </update>
</mapper>