<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.CompanyLabelRelationMapper">
  <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.CompanyLabelRelationDAO">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="label_id" jdbcType="INTEGER" property="labelId"/>
    <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
    <result column="status" jdbcType="BIT" property="status"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, label_id, company_id, status, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from company_label_relation where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from company_label_relation where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.microservice.user.entity.dao.CompanyLabelRelationDAO">
        insert into company_label_relation (id, label_id, company_id, status, create_time, update_time )
        values (#{id,jdbcType=INTEGER},
        #{labelId,jdbcType=INTEGER},
        #{companyId,jdbcType=VARCHAR},
        #{status,jdbcType=BIT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP} )
  </insert>
  <insert id="insertSelective" parameterType="com.microservice.user.entity.dao.CompanyLabelRelationDAO">
    insert into company_label_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null"> id, </if>
      <if test="labelId != null"> label_id, </if>
      <if test="companyId != null"> company_id, </if>
      <if test="status != null"> status, </if>
      <if test="createTime != null"> create_time, </if>
      <if test="updateTime != null"> update_time, </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null"> #{id,jdbcType=INTEGER}, </if>
      <if test="labelId != null"> #{labelId,jdbcType=INTEGER}, </if>
      <if test="companyId != null"> #{companyId,jdbcType=VARCHAR}, </if>
      <if test="status != null"> #{status,jdbcType=BIT}, </if>
      <if test="createTime != null"> #{createTime,jdbcType=TIMESTAMP}, </if>
      <if test="updateTime != null"> #{updateTime,jdbcType=TIMESTAMP}, </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.microservice.user.entity.dao.CompanyLabelRelationDAO">
    update company_label_relation
    <set>
      <if test="labelId != null"> label_id = #{labelId,jdbcType=INTEGER}, </if>
      <if test="companyId != null"> company_id = #{companyId,jdbcType=VARCHAR}, </if>
      <if test="status != null"> status = #{status,jdbcType=BIT}, </if>
      <if test="createTime != null"> create_time = #{createTime,jdbcType=TIMESTAMP}, </if>
      <if test="updateTime != null"> update_time = #{updateTime,jdbcType=TIMESTAMP}, </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.microservice.user.entity.dao.CompanyLabelRelationDAO">
        update
            company_label_relation
        set label_id = #{labelId,jdbcType=INTEGER}, company_id = #{companyId,jdbcType=VARCHAR}, status = #{status,jdbcType=BIT},
            create_time = #{createTime,jdbcType=TIMESTAMP}, update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER} </update>
  <update id="updateStatusByLabelId">
    update  company_label_relation
    set status = #{status,jdbcType=BIT} where label_id = #{labelId,jdbcType=VARCHAR}
  </update>
</mapper>