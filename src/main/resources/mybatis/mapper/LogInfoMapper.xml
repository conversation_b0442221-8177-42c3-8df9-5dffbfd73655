<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.LogInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.LogInfoDAO">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="opt" property="opt"/>
        <result column="content" property="content"/>
        <result column="extra_content" property="extraContent"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        type,
        opt,
        content,
        extra_content,
        app_channel_id,
        data_channel_id,
        create_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.id">
                AND id = #{condition.id}
            </if>
            <if test="null != condition.type">
                AND type = #{condition.type}
            </if>
            <if test="null != condition.opt">
                AND opt = #{condition.opt}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addLog" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.microservice.user.entity.dao.LogInfoDAO">
        INSERT INTO log_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != type">
                type,
            </if>
            <if test="null != opt">
                opt,
            </if>
            <if test="null != content">
                content,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != type">
                #{type},
            </if>
            <if test="null != opt">
                #{opt},
            </if>
            <if test="null != content">
                #{content},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime">
                #{createTime}
            </if>
        </trim>
    </insert>

    <delete id="delete">
        DELETE
        FROM log_info
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.LogInfoDAO">
        UPDATE log_info
        <set>
            <if test="null != type">type = #{type},</if>
            <if test="null != content">content = #{content},</if>
            <if test="null != extraContent">extra_content = #{extraContent},</if>
            <if test="null != appChannelId">app_channel_id = #{appChannelId},</if>
            <if test="null != dataChannelId">data_channel_id = #{dataChannelId},</if>
            <if test="null != createTime">create_time = #{createTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM log_info
        WHERE id = #{id}
    </select>

    <select id="getLogList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM log_info
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getLogCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM log_info
        <include refid="whereClause"/>
    </select>

    <delete id="deleteLog">
        DELETE FROM `log_info`
        <include refid="whereClause"/>
    </delete>
</mapper>