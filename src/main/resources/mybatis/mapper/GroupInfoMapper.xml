<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.GroupInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.GroupInfoDAO">
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="extra_content" property="extraContent"/>
        <result column="sort" property="sort"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        group_id
        ,
        group_name,
        extra_content,
        sort,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.groupId">
                AND group_id = #{condition.groupId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.groupName">
                AND group_name = #{condition.groupName}
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addGroup" parameterType="com.microservice.user.entity.dao.GroupInfoDAO">
        INSERT INTO group_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != groupId">
                group_id,
            </if>
            <if test="null != groupName">
                group_name,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != sort">
                sort,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != groupId">
                #{groupId},
            </if>
            <if test="null != groupName">
                #{groupName},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != sort">
                #{sort},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteGroup">
        DELETE
        FROM group_info
        <include refid="whereClause"/>
    </delete>

    <update id="updateGroup" parameterType="com.microservice.user.entity.dao.GroupInfoDAO">
        UPDATE group_info
        <set>
            <if test="null != value.groupId">group_id = #{value.groupId},</if>
            <if test="null != value.groupName">group_name = #{value.groupName},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.sort">sort = #{value.sort},</if>
            <if test="null != value.appChannelId">app_channel_id = #{value.appChannelId},</if>
            <if test="null != value.dataChannelId">data_channel_id = #{value.dataChannelId},</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <include refid="whereClause"/>
    </update>


    <select id="getGroup" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM group_info
        <include refid="whereClause"/>
        LIMIT 1
    </select>

    <select id="getGroupList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM group_info
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getGroupCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM group_info
        <include refid="whereClause"/>
    </select>

    <insert id="addGroupList">
        INSERT INTO group_info
        (
        group_id,
        group_name,
        extra_content,
        sort,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
        ) VALUES
        <foreach collection="dataList" item="data" index="index" separator=",">
            (
            #{data.groupId},
            #{data.groupName},
            #{data.extraContent},
            #{data.sort},
            #{data.appChannelId},
            #{data.dataChannelId},
            <if test="data.createTime == null">
                NOW(),
            </if>
            <if test="data.createTime != null">
                #{data.createTime},
            </if>
            <if test="data.updateTime == null">
                NOW()
            </if>
            <if test="data.updateTime != null">
                #{data.updateTime}
            </if>
            )
        </foreach>
    </insert>

</mapper>