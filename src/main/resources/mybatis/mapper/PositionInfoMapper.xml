<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.PositionInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.PositionInfoDAO">
        <result column="position_id" property="positionId"/>
        <result column="position_name" property="positionName"/>
        <result column="parent_id" property="parentId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        position_id
        ,
        position_name,
        parent_id,
        extra_content,
        sort,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.positionId">
                AND position_id = #{condition.positionId}
            </if>
            <if test="null != condition.positionName">
                AND position_name = #{condition.positionName}
            </if>
            <if test="null != condition.parentId">
                AND parent_id = #{condition.parentId}
            </if>
            <if test="null != condition.positionIdList">
                <foreach collection="condition.positionIdList" item="positionId" open="AND position_id IN (" close=")"
                         separator=",">
                    #{positionId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addPosition" parameterType="com.microservice.user.entity.dao.PositionInfoDAO">
        INSERT INTO position_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != positionId">
                position_id,
            </if>
            <if test="null != positionName">
                position_name,
            </if>
            <if test="null != parentId">
                parent_id,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != sort">
                sort,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != positionId">
                #{positionId},
            </if>
            <if test="null != positionName">
                #{positionName},
            </if>
            <if test="null != parentId">
                #{parentId},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != sort">
                #{sort},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deletePosition">
        DELETE
        FROM position_info
        <include refid="whereClause"/>
    </delete>

    <update id="updatePosition" parameterType="com.microservice.user.entity.dao.PositionInfoDAO">
        UPDATE position_info
        <set>
            <if test="null != value.positionId">position_id = #{value.positionId},</if>
            <if test="null != value.positionName">position_name = #{value.positionName},</if>
            <if test="null != value.parentId">parent_id = #{value.parentId},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.sort">sort = #{value.sort},</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <include refid="whereClause"/>
    </update>


    <select id="getPosition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM position_info
        <include refid="whereClause"/>
        LIMIT 1
    </select>

    <select id="getPositionList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM position_info
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getPositionCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM position_info
        <include refid="whereClause"/>
    </select>

</mapper>