<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.GroupPageRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.GroupPageRelationDAO">
        <result column="page_id" property="pageId"/>
        <result column="group_id" property="groupId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        page_id
        ,
        group_id,
        create_time
    </sql>

    <insert id="insert" parameterType="com.microservice.user.entity.dao.GroupPageRelationDAO">
        INSERT INTO group_page_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != pageId">
                page_id,
            </if>
            <if test="null != groupId">
                group_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != pageId">
                #{pageId},
            </if>
            <if test="null != groupId">
                #{groupId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
        </trim>
    </insert>

    <delete id="deleteGroupPageRelation">
        DELETE
        FROM group_page_relation
        <where>
            <if test="null != condition.groupId">
                AND group_id = #{condition.groupId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != condition.pageId">
                AND page_id = #{condition.pageId}
            </if>
            <if test="null != condition.pageIdList">
                <foreach collection="condition.pageIdList" item="pageId" open="AND page_id IN (" close=")"
                         separator=",">
                    #{pageId}
                </foreach>
            </if>
        </where>
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.GroupPageRelationDAO">
        UPDATE group_page_relation
        <set>
            <if test="null != pageId">page_id = #{pageId},</if>
            <if test="null != groupId">group_id = #{groupId},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM group_page_relation
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM group_page_relation
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM group_page_relation
    </select>

    <select id="getGPRList" resultType="com.microservice.user.entity.dao.GroupPageRelationDAO">
        SELECT
        gpr.* , igr.identity_id AS identityId
        FROM
        group_page_relation gpr
        INNER JOIN identity_group_relation igr ON gpr.group_id = igr.group_id
        <where>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND igr.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.identityId">
                AND igr.identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND igr.group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != condition.groupId">
                AND igr.group_id = #{condition.groupId}
            </if>
            <if test="null != condition.pageId">
                AND gpr.page_id = #{condition.pageId}
            </if>
        </where>
    </select>

    <insert id="addGroupPageRelationList">
        INSERT INTO group_page_relation
        (group_id, page_id)
        VALUES
        <foreach collection="dataList" item="data" index="index" separator=",">
            (#{data.groupId}, #{data.pageId})
        </foreach>
    </insert>

    <select id="getGroupPageRelationList" resultType="com.microservice.user.entity.dao.GroupPageRelationDAO">
        SELECT * FROM group_page_relation
        <where>
            <if test="null != condition.groupId">
                AND group_id = #{condition.groupId}
            </if>
            <if test="null != condition.pageId">
                AND page_id = #{condition.pageId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>