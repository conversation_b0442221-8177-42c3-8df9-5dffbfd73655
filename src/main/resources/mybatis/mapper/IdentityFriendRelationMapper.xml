<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.IdentityFriendRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.IdentityFriendRelationDAO">
        <result column="identity_id" property="identityId"/>
        <result column="target_identity_id" property="targetIdentityId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        identity_id
        ,
        target_identity_id,
        extra_content,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
    </sql>

    <insert id="insert" parameterType="com.microservice.user.entity.dao.IdentityFriendRelationDAO">
        INSERT INTO identity_friend_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                identity_id,
            </if>
            <if test="null != targetIdentityId">
                target_identity_id,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                #{identityId},
            </if>
            <if test="null != targetIdentityId">
                #{targetIdentityId},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId}
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteIdentityFriendRelation">
        DELETE
        FROM identity_friend_relation
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.targetIdentityId">
                AND target_identity_id = #{condition.targetIdentityId}
            </if>
            <if test="null != condition.targetIdentityIdList">
                <foreach collection="condition.targetIdentityIdList" item="targetIdentityId"
                         open="AND target_identity_id IN (" close=")"
                         separator=",">
                    #{targetIdentityId}
                </foreach>
            </if>
        </where>
    </delete>

    <update id="updateIdentityFriendRelation">
        UPDATE identity_friend_relation
        <set>
            <if test="null != value.identityId">identity_id = #{value.identityId},</if>
            <if test="null != value.targetIdentityId">target_identity_id = #{value.targetIdentityId},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.appChannelId">app_channel_id = #{value.appChannelId},</if>
            <if test="null != value.dataChannelId">data_channel_id = #{value.dataChannelId}</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.targetIdentityId">
                AND target_identity_id = #{condition.targetIdentityId}
            </if>
        </where>
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_friend_relation
        WHERE id = #{id}
    </select>

    <select id="getIdentityFriendRelation" resultType="com.microservice.user.entity.dao.IdentityFriendRelationDAO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_friend_relation
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.targetIdentityId">
                AND target_identity_id = #{condition.targetIdentityId}
            </if>
            <if test="null != condition.targetIdentityIdList">
                <foreach collection="condition.targetIdentityIdList" item="targetIdentityId"
                         open="AND target_identity_id IN (" close=")"
                         separator=",">
                    #{targetIdentityId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
        LIMIT 1
    </select>

    <select id="getIdentityFriendRelationList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_friend_relation
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.targetIdentityId">
                AND target_identity_id = #{condition.targetIdentityId}
            </if>
            <if test="null != condition.targetIdentityIdList">
                <foreach collection="condition.targetIdentityIdList" item="targetIdentityId"
                         open="AND target_identity_id IN (" close=")"
                         separator=",">
                    #{targetIdentityId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getIdentityFriendRelationCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM identity_friend_relation
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.targetIdentityId">
                AND target_identity_id = #{condition.targetIdentityId}
            </if>
            <if test="null != condition.targetIdentityIdList">
                <foreach collection="condition.targetIdentityIdList" item="targetIdentityId"
                         open="AND target_identity_id IN (" close=")"
                         separator=",">
                    #{targetIdentityId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="addIdentityFriendRelationList">
        INSERT INTO identity_friend_relation
        (identity_id, target_identity_id, extra_content, app_channel_id, data_channel_id)
        VALUES
        <foreach collection="dataList" item="data" index="index" separator=",">
            (#{data.identityId}, #{data.targetIdentityId}, #{data.extraContent}, #{data.appChannelId}, #{data.dataChannelId})
        </foreach>
    </insert>
</mapper>