<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.IdentityDepartmentRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.IdentityDepartmentRelationDAO">
        <result column="identity_id" property="identityId"/>
        <result column="department_id" property="departmentId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        identity_id
        ,
        department_id,
        extra_content,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.departmentId">
                AND department_id = #{condition.departmentId}
            </if>
            <if test="null != condition.departmentIdList">
                <foreach collection="condition.departmentIdList" item="departmentId" open="AND department_id IN ("
                         close=")"
                         separator=",">
                    #{departmentId}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="idrWhereClause">
        <where>
            <if test="null != condition.departmentId">
                AND idr.department_id = #{condition.departmentId}
            </if>
            <if test="null != condition.departmentIdList">
                <foreach collection="condition.departmentIdList" item="departmentId" open="AND idr.department_id IN ("
                         close=")"
                         separator=",">
                    #{departmentId}
                </foreach>
            </if>
            <if test="null != condition.identityId">
                AND idr.identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND idr.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND ii.extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.extraContentInCondition">
                <foreach collection="condition.extraContentInCondition.entrySet()" index="key" item="valList">
                    AND ii.extra_content ->> '$.${key}' IN
                    <foreach collection="valList" item="item" open="(" close=")"
                             separator=",">
                        #{item}
                    </foreach>
                </foreach>
            </if>
            <if test="null != condition.appChannelId">
                AND ii.app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND ii.data_channel_id = #{condition.dataChannelId}
            </if>
        </where>
    </sql>

    <insert id="insert" parameterType="com.microservice.user.entity.dao.IdentityDepartmentRelationDAO">
        INSERT INTO identity_department_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                identity_id,
            </if>
            <if test="null != departmentId">
                department_id,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                #{identityId},
            </if>
            <if test="null != departmentId">
                #{departmentId},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteIdentityDepartmentRelation">
        DELETE
        FROM identity_department_relation
        <include refid="whereClause"/>
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.IdentityDepartmentRelationDAO">
        UPDATE identity_department_relation
        <set>
            <if test="null != identityId">identity_id = #{identityId},</if>
            <if test="null != departmentId">department_id = #{departmentId},</if>
            <if test="null != extraContent">extra_content = #{extraContent},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_department_relation
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_department_relation
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM identity_department_relation
    </select>

    <select id="getIDRWithDepartmentList" resultType="com.microservice.user.entity.dao.IDRWithDepartmentDAO">
        SELECT idr.*,
        di.*,
        ii.user_id
        FROM identity_department_relation idr
        INNER JOIN identity_info ii ON idr.identity_id = ii.identity_id
        INNER JOIN department_info di ON idr.department_id = di.department_id
        <where>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND idr.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.departmentIdList">
                <foreach collection="condition.departmentIdList" item="departmentId" open="AND idr.department_id IN ("
                         close=")"
                         separator=",">
                    #{departmentId}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="addIdentityDepartmentRelationList">
        INSERT INTO identity_department_relation
        (identity_id, department_id)
        VALUES
        <foreach collection="identityDepartmentRelationList" item="identityDepartmentRelation" index="index"
                 separator=",">
            (#{identityDepartmentRelation.identityId}, #{identityDepartmentRelation.departmentId})
        </foreach>
    </insert>

    <select id="getIDRWithIdentityList" resultType="com.microservice.user.entity.dao.IDRWithIdentityDAO">
        SELECT ii.*, idr.department_id
        FROM identity_department_relation idr
        INNER JOIN identity_info ii ON idr.identity_id = ii.identity_id
        <include refid="idrWhereClause"/>
        ORDER BY ii.create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>


    <select id="getIDRWithIdentityCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM identity_department_relation idr
        INNER JOIN identity_info ii ON idr.identity_id = ii.identity_id
        <include refid="idrWhereClause"/>
    </select>

    <select id="getIDRWithIdentityDistinctCount" resultType="java.lang.Integer">
        SELECT COUNT(distinct ii.user_id)
        FROM identity_department_relation idr
        INNER JOIN identity_info ii ON idr.identity_id = ii.identity_id
        <include refid="idrWhereClause"/>
    </select>

    <select id="getIdentityDepartmentRelationList"
            resultType="com.microservice.user.entity.dao.IdentityDepartmentRelationDAO">
        SELECT * FROM
        identity_department_relation
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getIdentityDepartmentRelationCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        identity_department_relation
        <include refid="whereClause"/>
    </select>
    <select id="getIDRValidIdentityRelation" resultType="com.microservice.user.entity.dao.IdentityDepartmentRelationDAO" >
        SELECT
            idr.identity_id,
            idr.department_id,
            idr.extra_content,
            idr.create_time,
            idr.update_time
        FROM
            identity_department_relation idr,
            identity_info ii,
            company_department_relation cdr,
            identity_company_relation icr
        WHERE
            idr.department_id = #{departmentId}
          and ii.identity_id = idr.identity_id
          and cdr.department_id = idr.department_id
          and icr.company_id = cdr.company_id
          and icr.identity_id = idr.identity_id;
    </select>

    <select id="batchGetIdentityIdsByDepartments" resultType="com.microservice.user.entity.dao.IdentityDepartmentRelationDAO">
        SELECT department_id,
        identity_id,
        extra_content,
        create_time,
        update_time
        FROM identity_department_relation
        <where>
            <if test="departmentIds != null and departmentIds.size > 0">
                department_id IN
                <foreach collection="departmentIds" item="departmentId" open="(" separator="," close=")">
                    #{departmentId}
                </foreach>
            </if>
        </where>
    </select>


    <select id="getDepartmentFatherAndSon" resultType="com.microservice.user.entity.dto.ms.DepartmentFatherAndSonDTO">
        WITH RECURSIVE department_hierarchy AS (
        SELECT
        department_id,
        department_name,
        parent_id,
        0 as level
        FROM department_info
        WHERE department_id IN
        <foreach collection="departmentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        UNION ALL

        SELECT
        p.department_id,
        p.department_name,
        p.parent_id,
        h.level + 1 as level
        FROM department_info p
        INNER JOIN department_hierarchy h ON p.department_id = h.parent_id
        WHERE p.parent_id IS NOT NULL
        AND h.level &lt; 10 -- 小于10
        )
        SELECT DISTINCT
        department_id,
        department_name,
        parent_id,
        level
        FROM department_hierarchy
        ORDER BY level, department_id
    </select>


</mapper>