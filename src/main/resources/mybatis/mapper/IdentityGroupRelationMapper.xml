<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.IdentityGroupRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.IdentityGroupRelationDAO">
        <result column="identity_id" property="identityId"/>
        <result column="group_id" property="groupId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        identity_id
        ,
        group_id,
        create_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.groupId">
                AND group_id = #{condition.groupId}
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="insert" parameterType="com.microservice.user.entity.dao.IdentityGroupRelationDAO">
        INSERT INTO identity_group_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                identity_id,
            </if>
            <if test="null != groupId">
                group_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                #{identityId},
            </if>
            <if test="null != groupId">
                #{groupId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
        </trim>
    </insert>

    <delete id="delete">
        DELETE
        FROM identity_group_relation
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.IdentityGroupRelationDAO">
        UPDATE identity_group_relation
        <set>
            <if test="null != identityId">identity_id = #{identityId},</if>
            <if test="null != groupId">group_id = #{groupId},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_group_relation
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_group_relation
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM identity_group_relation
    </select>

    <select id="getIGRWithGroupList" resultType="com.microservice.user.entity.dao.IGRWithGroupDAO">
        SELECT
        gi.*, igr.identity_id AS identityId, igr.create_time AS igrCreateTime
        FROM identity_group_relation igr
        INNER JOIN group_info gi ON igr.group_id = gi.group_id
        <where>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND igr.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND igr.group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != condition.identityId">
                AND igr.identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.appChannelId">
                AND gi.app_channel_id = #{condition.appChannelId}
            </if>
            <if test="condition.groupId != null and condition.groupId != ''">
                AND igr.group_id = #{condition.groupId}
            </if>
        </where>
    </select>

    <insert id="addIdentityGroupRelationList">
        INSERT INTO identity_group_relation
        (identity_id, group_id)
        VALUES
        <foreach collection="identityGroupRelationList" item="identityGroupRelation" index="index" separator=",">
            (#{identityGroupRelation.identityId}, #{identityGroupRelation.groupId})
        </foreach>
    </insert>

    <delete id="deleteIdentityGroupRelation">
        <if test="null != condition">
            DELETE FROM identity_group_relation
            <include refid="whereClause"/>
        </if>
    </delete>

    <select id="getIGRCountGroupByGroupId" resultType="com.microservice.user.entity.dao.IdentityGroupCountDAO">
        SELECT igr.`group_id` AS groupId, COUNT(*) AS dataCount
        FROM `identity_group_relation` igr
        INNER JOIN identity_info ii ON igr.identity_id = ii.identity_id
        <where>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND igr.group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
        GROUP BY igr.`group_id`
    </select>

    <select id="getIGRWithIdentityList" resultType="com.microservice.user.entity.dao.IGRWithIdentityDAO">
        SELECT ii.*, igr.group_id AS groupId
        FROM identity_group_relation igr
        INNER JOIN identity_info ii ON igr.identity_id = ii.identity_id
        <where>
            <if test="null != condition.groupId">
                AND igr.group_id = #{condition.groupId}
            </if>
            <if test="null != condition.identityId">
                AND igr.identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND igr.identity_id IN ("
                         close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.groupIdList">
                <foreach collection="condition.groupIdList" item="groupId" open="AND igr.group_id IN (" close=")"
                         separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
        ORDER BY igr.create_time DESC
    </select>

    <select id="getIdentityGroupRelationList" resultType="com.microservice.user.entity.dao.IdentityGroupRelationDAO">
        SELECT * FROM identity_group_relation
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getIdentityGroupRelationCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM identity_group_relation
        <include refid="whereClause"/>
    </select>

    <delete id="deleteIGRByIdentityId">
        DELETE
        FROM identity_group_relation igr INNER JOIN identity_info ii
        ON igr.identity_id = ii.identity_id
        WHERE igr.identity_id = #{identityId}
          AND ii.app_channel_id = #{appChannelId}
    </delete>

    <select id="getGroupList" resultType="com.microservice.user.entity.dao.IGRWithGroupDAO">
        SELECT
            gi.group_id,
            gi.group_name,
            gi.extra_content,
            gi.sort,
            gi.app_channel_id,
            gi.data_channel_id,
            gi.create_time,
            gi.update_time,
            igr.identity_id AS identityId,
            igr.create_time AS igrCreateTime
        FROM
            identity_group_relation igr
            inner join group_info gi on igr.group_id = gi.group_id
            <if test="identityIdList == null or identityIdList.size == 0">
                left join identity_company_relation icr on icr.identity_id = igr.identity_id
            </if>
        <where>
            <if test="identityIdList == null or identityIdList.size == 0">
                AND icr.company_id = #{companyId}
            </if>
            <if test="identityIdList != null and identityIdList.size > 0">
                AND igr.identity_id IN
                <foreach collection="identityIdList" item="identityId" open="(" close=")" separator=",">
                    #{identityId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>