<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.IdentityCompanyRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.IdentityCompanyRelationDAO">
        <result column="identity_id" property="identityId"/>
        <result column="company_id" property="companyId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="getCompanyDepartmentPeopleNumMap" type="com.microservice.user.entity.dto.ms.CompanyDepartmentPeopleNumDTO">
        <result column="company_id" property="companyId" />
        <result column="department_id" property="departmentId" />
        <collection property="userIdList" ofType="string">
            <result column="user_id"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        identity_id
        ,
        company_id,
        extra_content,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.companyId">
                AND company_id = #{condition.companyId}
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="null != condition.companyIdList">
                <foreach collection="condition.companyIdList" item="companyId" open="AND company_id IN (" close=")"
                         separator=",">
                    #{companyId}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="icrWhereClause">
        <where>
            AND ii.identity_id IS NOT NULL
            <if test="null != condition.companyId">
                AND icr.company_id = #{condition.companyId}
            </if>
            <if test="null != condition.companyIdList">
                <if test="null != condition.companyIdList">
                    <foreach collection="condition.companyIdList" item="companyId" open="AND icr.company_id IN ("
                             close=")"
                             separator=",">
                        #{companyId}
                    </foreach>
                </if>
            </if>
            <if test="null != condition.identityId">
                AND icr.identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.identityIdList">
                <if test="null != condition.identityIdList">
                    <foreach collection="condition.identityIdList" item="identityId" open="AND icr.identity_id IN ("
                             close=")"
                             separator=",">
                        #{identityId}
                    </foreach>
                </if>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND ii.extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.extraContentInCondition">
                <foreach collection="condition.extraContentInCondition.entrySet()" index="key" item="valList">
                    AND ii.extra_content ->> '$.${key}' IN
                    <foreach collection="valList" item="item" open="(" close=")"
                             separator=",">
                        #{item}
                    </foreach>
                </foreach>
            </if>
            <if test="null != condition.appChannelId">
                AND ii.app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND ii.data_channel_id = #{condition.dataChannelId}
            </if>
        </where>
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.microservice.user.entity.dao.IdentityCompanyRelationDAO">
        INSERT INTO identity_company_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != identityId and '' != identityId">
                identity_id,
            </if>
            <if test="null != companyId and '' != companyId">
                company_id,
            </if>
            <if test="null != extraContent and '' != extraContent">
                extra_content,
            </if>
            <if test="null != createTime and '' != createTime">
                create_time,
            </if>
            <if test="null != updateTime and '' != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != identityId and '' != identityId">
                #{identityId},
            </if>
            <if test="null != companyId and '' != companyId">
                #{companyId},
            </if>
            <if test="null != extraContent and '' != extraContent">
                #{extraContent},
            </if>
            <if test="null != createTime and '' != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime and '' != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteIdentityCompanyRelation">
        DELETE
        FROM identity_company_relation
        <include refid="whereClause"/>
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.IdentityCompanyRelationDAO">
        UPDATE identity_company_relation
        <set>
            <if test="null != identityId and '' != identityId">identity_id = #{identityId},</if>
            <if test="null != companyId and '' != companyId">company_id = #{companyId},</if>
            <if test="null != extraContent and '' != extraContent">extra_content = #{extraContent},</if>
            <if test="null != createTime and '' != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime and '' != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_company_relation
        WHERE id = #{id}
    </select>

    <select id="getIdentityCompanyRelationList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_company_relation
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getIdentityCompanyRelationCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT ui.user_id) FROM identity_company_relation icr
        LEFT JOIN identity_info ii on ii.identity_id = icr.identity_id
        LEFT JOIN user_info ui ON ii.user_id = ui.user_id
        <include refid="icrWhereClause"/>
    </select>

    <insert id="addIdentityCompanyRelationList">
        INSERT INTO identity_company_relation
        (identity_id, company_id)
        VALUES
        <foreach collection="identityCompanyRelationList" item="identityCompanyRelation" index="index" separator=",">
            (#{identityCompanyRelation.identityId}, #{identityCompanyRelation.companyId})
        </foreach>
    </insert>

    <select id="getICRCountGroupByCompanyId" resultType="com.microservice.user.entity.dao.IdentityCompanyCountDAO">
        SELECT `company_id` AS companyId, COUNT(*) AS dataCount
        FROM `identity_company_relation`
        <where>
            <if test="null != condition.companyIdList">
                <foreach collection="condition.companyIdList" item="companyId" open="AND company_id IN (" close=")"
                         separator=",">
                    #{companyId}
                </foreach>
            </if>
        </where>
        GROUP BY `company_id`
    </select>

    <select id="getICRWithIdentityList" resultType="com.microservice.user.entity.dao.ICRWithIdentityDAO">
        SELECT ii.*, icr.company_id
        FROM identity_company_relation icr
        LEFT JOIN identity_info ii ON icr.identity_id = ii.identity_id
        <include refid="icrWhereClause"/>
        ORDER BY ii.create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getICRWithIdentityCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM identity_company_relation icr
        LEFT JOIN identity_info ii ON icr.identity_id = ii.identity_id
        <include refid="icrWhereClause"/>
    </select>

    <select id="getCompanyDepartmentPeopleNum" resultMap="getCompanyDepartmentPeopleNumMap" >
        SELECT
        icr.company_id,
        di.department_id,
        ui.user_id
        FROM
        identity_company_relation icr
        INNER JOIN identity_info ii on ii.identity_id = icr.identity_id
        INNER JOIN user_info ui on ui.user_id = ii.user_id
        left join identity_department_relation idr on icr.identity_id = idr.identity_id
        left join department_info di on idr.department_id = di.department_id
        <where>
            icr.company_id in
            <foreach collection="companyIdList" item="companyId" separator="," open="(" close=")">
                #{companyId}
            </foreach>
            and ui.user_id is not null
        </where>
    </select>
</mapper>