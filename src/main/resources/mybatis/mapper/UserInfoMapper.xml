<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.UserInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.UserInfoDAO">
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="password" property="password"/>
        <result column="extra_content" property="extraContent"/>
        <result column="sort" property="sort"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="identity_id" property="identityId"/>
        <result column="uccExtraContent" property="uccExtraContent"/>
    </resultMap>
    <resultMap id="getUserAndIdentityListResultMap" type="com.microservice.user.entity.dao.UserInfoWithIdentityDAO">
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="password" property="password"/>
        <result column="extra_content" property="extraContent"/>
        <result column="sort" property="sort"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <collection property="identityList" ofType="com.microservice.user.entity.dao.IdentityInfoWithCompanyInfoDAO">
            <result column="identity_id" property="identityId"/>
            <result column="identity_name" property="identityName"/>
            <result column="ii_user_id" property="userId"/>
            <result column="ii_extra_content" property="extraContent"/>
            <result column="ii_sort" property="sort"/>
            <result column="ii_app_channel_id" property="appChannelId"/>
            <result column="ii_data_channel_id" property="dataChannelId"/>
            <result column="ii_create_time" property="createTime"/>
            <result column="ii_update_time" property="updateTime"/>
            <result column="company_id" property="companyId"/>
            <collection property="departmentList" ofType="com.microservice.user.entity.dto.ms.DepartmentDTO">
                <result column="department_id" property="departmentId"/>
                <result column="department_name" property="departmentName"/>
            </collection>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        user_id
        ,
        user_name,
        password,
        extra_content,
        sort,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.userId">
                AND user_id = #{condition.userId}
            </if>
            <if test="null != condition.userName">
                AND user_name = #{condition.userName}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.userIdList">
                <foreach collection="condition.userIdList" item="userId" open="AND user_id IN (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="null != condition.userNameList">
                <foreach collection="condition.userNameList" item="userName" open="AND user_name IN (" close=")"
                         separator=",">
                    #{userName}
                </foreach>
            </if>
            <if test="null != condition.companyUserIdList">
                <foreach collection="condition.companyUserIdList" item="userId" open="AND user_id IN (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.startDateTime">
                <![CDATA[
                    AND create_time >= #{condition.startDateTime}
                ]]>
            </if>
            <if test="null != condition.endDateTime">
                <![CDATA[
                    AND create_time <= #{condition.endDateTime}
                ]]>
            </if>
        </where>
    </sql>

    <insert id="addUser" parameterType="com.microservice.user.entity.dao.UserInfoDAO">
        INSERT INTO user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != userId">
                user_id,
            </if>
            <if test="null != userName">
                user_name,
            </if>
            <if test="null != password">
                password,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != sort">
                sort,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != userId">
                #{userId},
            </if>
            <if test="null != userName">
                #{userName},
            </if>
            <if test="null != password">
                #{password},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != sort">
                #{sort},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteUser">
        DELETE
        FROM user_info
        <where>
            <if test="null != condition.userId">
                AND user_id = #{condition.userId}
            </if>
            <if test="null != condition.userName">
                AND user_name = #{condition.userName}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.userIdList">
                <foreach collection="condition.userIdList" item="userId" open="AND user_id IN (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </delete>

    <update id="updateUser" parameterType="com.microservice.user.entity.dao.UserInfoDAO">
        UPDATE user_info
        <set>
            <if test="null != value.userName">user_name = #{value.userName},</if>
            <if test="null != value.password">password = #{value.password},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.sort">sort = #{value.sort},</if>
            <if test="null != value.appChannelId">app_channel_id = #{value.appChannelId},</if>
            <if test="null != value.dataChannelId">data_channel_id = #{value.dataChannelId},</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <include refid="whereClause"/>
    </update>

    <select id="getUser" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_info
        <include refid="whereClause"/>
        LIMIT 1
    </select>

    <select id="getUserList" resultMap="BaseResultMap">
        <if test="null != condition.identityIdList">
            SELECT
            ui.*
            FROM
            identity_info ii
            LEFT JOIN user_info ui ON ii.user_id = ui.user_id
            <where>
                <if test="null != condition.userId">
                    AND ui.user_id = #{condition.userId}
                </if>
                <if test="null != condition.userName">
                    AND ui.user_name = #{condition.userName}
                </if>
                <if test="null != condition.appChannelId">
                    AND ui.app_channel_id = #{condition.appChannelId}
                </if>
                <if test="null != condition.dataChannelId">
                    AND ui.data_channel_id = #{condition.dataChannelId}
                </if>
                <if test="null != condition.identityIdList">
                    <foreach collection="condition.identityIdList" item="identityId" open="AND ii.identity_id IN ("
                             close=")"
                             separator=",">
                        #{identityId}
                    </foreach>
                </if>
                <if test="null != condition.userNameList">
                    <foreach collection="condition.userNameList" item="userName" open="AND ui.user_name IN (" close=")"
                             separator=",">
                        #{userName}
                    </foreach>
                </if>
                <if test="null != condition.userIdList">
                    <foreach collection="condition.userIdList" item="userId" open="AND ui.user_id IN (" close=")"
                             separator=",">
                        #{userId}
                    </foreach>
                </if>
                <if test="null != condition.companyUserIdList">
                    <foreach collection="condition.companyUserIdList" item="userId" open="AND ui.user_id IN (" close=")"
                             separator=",">
                        #{userId}
                    </foreach>
                </if>
                <if test="null != condition.extraContentCondition">
                    <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                        AND ui.extra_content ->> '$.${key}' = #{val}
                    </foreach>
                </if>
                <if test="null != condition.startDateTime">
                    <![CDATA[
                        AND ui.create_time >= #{condition.startDateTime}
                    ]]>
                </if>
                <if test="null != condition.endDateTime">
                    <![CDATA[
                        AND ui.create_time <= #{condition.endDateTime}
                    ]]>
                </if>
            </where>
            ORDER BY ui.`create_time` DESC
        </if>
        <if test="null == condition.identityIdList">
            <if test="condition.companyId != null and condition.companyId != ''">
                SELECT
                ui.*,
                ii.identity_id,
                ii.extra_content as uccExtraContent
                FROM user_info ui
                LEFT JOIN identity_info ii ON ii.user_id = ui.user_id
                LEFT JOIN identity_company_relation icr ON icr.identity_id = ii.identity_id
                WHERE icr.company_id = #{condition.companyId}
                ORDER BY ui.`create_time` DESC
            </if>
            <if test="condition.companyId == null or condition.companyId == ''">
                SELECT
                <include refid="Base_Column_List"/>
                FROM user_info
                <include refid="whereClause"/>
                ORDER BY `create_time` DESC
            </if>
        </if>
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getUserCount" resultType="java.lang.Integer">
        <if test="null != condition.identityIdList">
            SELECT
            COUNT(*)
            FROM
            identity_info ii
            LEFT JOIN user_info ui ON ii.user_id = ui.user_id
            <where>
                <if test="null != condition.userId">
                    AND ui.user_id = #{condition.userId}
                </if>
                <if test="null != condition.userName">
                    AND ui.user_name = #{condition.userName}
                </if>
                <if test="null != condition.appChannelId">
                    AND ui.app_channel_id = #{condition.appChannelId}
                </if>
                <if test="null != condition.dataChannelId">
                    AND ui.data_channel_id = #{condition.dataChannelId}
                </if>
                <if test="null != condition.identityIdList">
                    <foreach collection="condition.identityIdList" item="identityId" open="AND ii.identity_id IN ("
                             close=")"
                             separator=",">
                        #{identityId}
                    </foreach>
                </if>
                <if test="null != condition.userIdList">
                    <foreach collection="condition.userIdList" item="userId" open="AND ui.user_id IN (" close=")"
                             separator=",">
                        #{userId}
                    </foreach>
                </if>
                <if test="null != condition.userNameList">
                    <foreach collection="condition.userNameList" item="userName" open="AND ui.user_name IN (" close=")"
                             separator=",">
                        #{userName}
                    </foreach>
                </if>
                <if test="null != condition.extraContentCondition">
                    <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                        AND ui.extra_content ->> '$.${key}' = #{val}
                    </foreach>
                </if>
            </where>
        </if>
        <if test="null == condition.identityIdList">
            SELECT COUNT(*)
            FROM user_info
            <include refid="whereClause"/>
        </if>
    </select>

    <select id="getUserByIdentityId" resultType="com.microservice.user.entity.dao.UserInfoDAO">
        SELECT ui.*
        FROM identity_info ii
                 LEFT JOIN user_info ui ON ii.user_id = ui.user_id
        WHERE ii.identity_id = #{identityId} LIMIT 1
    </select>

    <select id="getIGERWithIdentityList" resultType="com.microservice.user.entity.dao.IGERWithIdentityDAO">
        SELECT ii.*,
        igr.group_id AS groupId,
        ger.event_id AS eventId
        FROM group_event_relation ger
        INNER JOIN identity_group_relation igr ON ger.group_id = igr.group_id
        INNER JOIN identity_info ii ON ii.identity_id = igr.identity_id
        INNER JOIN identity_company_relation icr ON ii.identity_id = icr.identity_id
        <where>
            <if test="null != eventId">
                AND ger.event_id = #{eventId}
            </if>
            <if test="null != companyId">
                AND icr.company_id = #{companyId}
            </if>
        </where>
        ORDER BY ii.create_time DESC
    </select>

    <select id="getUserAndIdentityCount" resultType="long">
        SELECT
        count( 1 )
        FROM
        (
        SELECT
        ui.user_id,
        ui.user_name,
        ui.`password`,
        ui.extra_content,
        ui.sort,
        ui.app_channel_id,
        ui.data_channel_id,
        ui.create_time,
        ui.update_time,
        ii.identity_id,
        ii.identity_name,
        ii.user_id ii_user_id,
        ii.extra_content ii_extra_content,
        ii.sort ii_sort,
        ii.app_channel_id ii_app_channel_id,
        ii.data_channel_id ii_data_channel_id,
        ii.create_time ii_create_time,
        ii.update_time ii_update_time,
        icr.company_id
        FROM
        identity_company_relation icr
        INNER JOIN identity_info ii ON icr.identity_id = ii.identity_id
        INNER JOIN user_info ui ON ii.user_id = ui.user_id
        <where>
            icr.company_id = #{companyId}
            <if test="departmentIdList != null and departmentIdList.size > 0">
                AND ii.identity_id in (select identity_id from identity_department_relation where department_id in
                    <foreach collection="departmentIdList" item="departmentId" separator="," open="(" close=")">
                        #{departmentId}
                    </foreach>
                )
            </if>
            <if test="userName != null and userName != ''">
                AND ui.user_name = #{userName}
            </if>
        </where>
        ) t
    </select>

    <select id="getUserAndIdentityList" resultMap="getUserAndIdentityListResultMap">
        SELECT
            t.*,
            di.department_id,
            di.department_name
        FROM
            (
                SELECT
                    ui.user_id,
                    ui.user_name,
                    ui.`password`,
                    ui.extra_content,
                    ui.sort,
                    ui.app_channel_id,
                    ui.data_channel_id,
                    ui.create_time,
                    ui.update_time,
                    ii.identity_id,
                    ii.identity_name,
                    ii.user_id ii_user_id,
                    ii.extra_content ii_extra_content,
                    ii.sort ii_sort,
                    ii.app_channel_id ii_app_channel_id,
                    ii.data_channel_id ii_data_channel_id,
                    ii.create_time ii_create_time,
                    ii.update_time ii_update_time,
                    icr.company_id
                FROM
                    identity_company_relation icr
                    INNER JOIN identity_info ii ON icr.identity_id = ii.identity_id
                    INNER JOIN user_info ui ON ii.user_id = ui.user_id
                <where>
                    icr.company_id = #{companyId}
                    <if test="departmentIdList != null and departmentIdList.size > 0">
                        AND ii.identity_id in (select identity_id from identity_department_relation where department_id in
                            <foreach collection="departmentIdList" item="departmentId" separator="," open="(" close=")">
                                #{departmentId}
                            </foreach>
                        )
                    </if>
                    <if test="userName != null and userName != ''">
                        AND ui.user_name = #{userName}
                    </if>
                </where>
                ORDER BY ui.create_time DESC, ii.create_time DESC
                <if test="limit > 0">
                    LIMIT ${start}, ${limit}
                </if>
            ) t
            LEFT JOIN identity_department_relation idr ON idr.identity_id = t.identity_id
            LEFT JOIN department_info di ON idr.department_id = di.department_id
    </select>
</mapper>