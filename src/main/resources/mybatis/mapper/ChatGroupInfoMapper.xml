<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.ChatGroupInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.ChatGroupInfoDAO">
        <result column="chat_group_id" property="chatGroupId"/>
        <result column="chat_group_name" property="chatGroupName"/>
        <result column="extra_content" property="extraContent"/>
        <result column="sort" property="sort"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        chat_group_id
        ,
        chat_group_name,
        extra_content,
        sort,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.chatGroupId">
                AND chat_group_id = #{condition.chatGroupId}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.chatGroupIdList">
                <foreach collection="condition.chatGroupIdList" item="chatGroupId" open="AND chat_group_id IN ("
                         close=")"
                         separator=",">
                    #{chatGroupId}
                </foreach>
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addChatGroup" parameterType="com.microservice.user.entity.dao.ChatGroupInfoDAO">
        INSERT INTO chat_group_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != chatGroupId">
                chat_group_id,
            </if>
            <if test="null != chatGroupName">
                chat_group_name,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != sort">
                sort,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != chatGroupId">
                #{chatGroupId},
            </if>
            <if test="null != chatGroupName">
                #{chatGroupName},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != sort">
                #{sort},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteChatGroup">
        DELETE
        FROM chat_group_info
        <include refid="whereClause"/>
    </delete>

    <update id="updateChatGroup" parameterType="com.microservice.user.entity.dao.ChatGroupInfoDAO">
        UPDATE chat_group_info
        <set>
            <if test="null != value.chatGroupId">chat_group_id = #{value.chatGroupId},</if>
            <if test="null != value.chatGroupName">chat_group_name = #{value.chatGroupName},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.sort">sort = #{value.sort},</if>
            <if test="null != value.appChannelId">app_channel_id = #{value.appChannelId},</if>
            <if test="null != value.dataChannelId">data_channel_id = #{value.dataChannelId},</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <include refid="whereClause"/>
    </update>


    <select id="getChatGroup" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_group_info
        <include refid="whereClause"/>
        LIMIT 1
    </select>

    <select id="getChatGroupList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_group_info
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getChatGroupCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_group_info
        <include refid="whereClause"/>
    </select>

    <select id="searchChatGroupByName" resultType="com.microservice.user.entity.dto.ms.SearchChatGroupDTO">
        SELECT cgi.chat_group_id,cgi.chat_group_name from chat_group_info cgi
        <where>
            <if test="null != groupIdList">
                <foreach collection="groupIdList" item="groupId" open="AND chat_group_id IN (" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != keyword">
                AND chat_group_name like CONCAT('%', #{keyword}, '%')
            </if>
         </where>
    </select>

    <resultMap id="SearchChatGroupMap" type="com.microservice.user.entity.dto.ms.SearchChatGroupDTO">
        <result column="chat_group_id" property="chatGroupId" jdbcType="VARCHAR"/>
        <result column="chat_group_name" property="chatGroupName" jdbcType="VARCHAR"/>
        <collection property="names" ofType="java.lang.String" javaType="list" column="name" >
                <constructor>
                    <arg column="name" jdbcType="VARCHAR"/>
                </constructor>
        </collection>
    </resultMap>

    <select id="searchChatGroupByUserRemarkName"
            resultMap="SearchChatGroupMap">
        SELECT
            cgi.chat_group_id,
            cgi.chat_group_name,
            JSON_UNQUOTE(JSON_EXTRACT(JSON_UNQUOTE(JSON_EXTRACT(icgr.extra_content,'$.customized')),'$.groupNickName')) name
        FROM
            chat_group_info cgi
                JOIN identity_chat_group_relation icgr ON icgr.chat_group_id = cgi.chat_group_id
        <where>
            <if test="null != groupIdList">
                <foreach collection="groupIdList" item="groupId" open="AND cgi.chat_group_id IN (" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="null != keyword">
                and JSON_UNQUOTE(JSON_EXTRACT( JSON_UNQUOTE( JSON_EXTRACT( icgr.extra_content, '$.customized' )), '$.groupNickName' )) LIKE CONCAT('%', #{keyword}, '%')
            </if>
        </where>
    </select>
    <select id="searchChatGroupByUserName" resultMap="SearchChatGroupMap">
        SELECT
            cgi.chat_group_id,
            cgi.chat_group_name,
            REPLACE (
                    CASE WHEN iden.extra_content -> '$.stageName' IS NULL OR iden.extra_content -> '$.stageName' = ''
                             THEN CASE WHEN iden.extra_content -> '$.realName' IS NULL OR iden.extra_content -> '$.realName' = ''
                                           THEN CASE WHEN iden.extra_content -> '$.markName' IS NULL OR iden.extra_content -> '$.markName' = ''
                                                         THEN CASE WHEN iden.extra_content -> '$.nickname' IS NULL OR iden.extra_content -> '$.nickname' = ''
                                                                       THEN iden.extra_content -> '$.userName'
                                                                   ELSE  iden.extra_content -> '$.nickname'  END
                                                     ELSE iden.extra_content -> '$.markName' END
                                       ELSE iden.extra_content -> '$.realName' END
                         ELSE iden.extra_content -> '$.stageName'
                        END,
                    '"',
                    ''
            ) name
        FROM
            chat_group_info cgi
                JOIN identity_chat_group_relation icgr ON icgr.chat_group_id = cgi.chat_group_id
                JOIN user_info iden ON iden.user_id = icgr.identity_id
        <where>
            <if test="null != groupIdList">
                <foreach collection="groupIdList" item="groupId" open="AND cgi.chat_group_id IN (" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
            HAVING name like CONCAT('%', #{keyword}, '%')
    </select>

</mapper>