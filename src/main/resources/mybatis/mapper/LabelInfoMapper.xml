<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.LabelInfoMapper">
  <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.LabelInfoDAO">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="label_name" jdbcType="VARCHAR" property="labelName" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="app_channel_id" jdbcType="VARCHAR" property="appChannelId" />
    <result column="data_channel_id" jdbcType="VARCHAR" property="dataChannelId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, label_name,parent_id, status,app_channel_id,data_channel_id,  create_time, update_time
  </sql>

  <select id="getExistLabelIds" parameterType="java.util.List" resultType="com.microservice.user.entity.dto.ms.label.LabelInfoDTO">
      SELECT id , label_name
      FROM label_info
      WHERE status = 1
      AND id IN
      <foreach collection="list" item="id" open="(" separator="," close=")">
          #{id,jdbcType=VARCHAR}
      </foreach>
  </select>


  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from label_info
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <resultMap id="getLabelListMap" type="com.microservice.user.entity.dto.ms.label.LabelInfoDTO">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="label_name" jdbcType="VARCHAR" property="labelName" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <collection property="childrenLabels" ofType="com.microservice.user.entity.dto.ms.label.LabelInfoDTO" column="id" select="selectChildrenByParentId">
    </collection>
  </resultMap>

  <resultMap id="ChildResultMap" type="com.microservice.user.entity.dto.ms.label.LabelInfoDTO">
    <result column="childrenId" jdbcType="VARCHAR" property="id" />
    <result column="childrenLabelName" jdbcType="VARCHAR" property="labelName" />
    <result column="childrenParentId" jdbcType="VARCHAR" property="parentId" />
    <result column="childrenStatus" jdbcType="BIT" property="status" />
    <result column="childrenCreateTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="childrenUpdateTime" jdbcType="TIMESTAMP" property="updateTime"/>
    <collection property="childrenLabels" ofType="com.microservice.user.entity.dto.ms.label.LabelInfoDTO" column="childrenId" select="selectChildrenByParentId">
    </collection>
  </resultMap>
  
  <select id="selectChildrenByParentId" resultMap="ChildResultMap">
        select      children.id childrenId,
                    children.label_name childrenLabelName,
                    children.parent_id childrenParentId,
                    children.status childrenStatus,
                    children.create_time childrenCreateTime,
                    children.update_time childrenUpdateTime  from label_info children
                                                             where children.parent_id = #{id} and children.status = 1
        order by children.create_time ASC
  </select>

  <select id="getLabelList" resultMap="getLabelListMap">
    select
        li.id,
        li.label_name ,
        li.parent_id ,
        li.status,
        li.create_time ,
        li.update_time
--     children.id childrenId,
--     children.label_name childrenLabelName,
--     children.parent_id childrenParentId,
--     children.status childrenStatus,
--     children.create_time childrenCreateTime,
--     children.update_time childrenUpdateTime
    from label_info li
    join company_label_relation clr on  clr.label_id = li.id
   --  left join label_info children on children.parent_id = li.id and children.status = 1
      <where>
        li.status = 1 and clr.company_id = #{request.companyId,jdbcType=VARCHAR}
        <if test="request.labelName != null and request.labelName != '' ">
          and li.label_name LIKE CONCAT('%', #{request.labelName}, '%')
        </if>
        <if test="request.parentId != null and request.parentId != '' ">
          and li.parent_id = #{request.parentId,jdbcType=VARCHAR}
        </if>
        <if test="request.parentId == null or request.parentId == '' ">
          and li.parent_id = ''
        </if>
      </where>
    order by li.create_time ASC
  </select>
  <select id="getLabelListByParentId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from label_info
        where status = 1

    <if test="labelId != null and labelId!='' ">
      and parent_id = #{labelId,jdbcType=VARCHAR}
    </if>
    <if test="labelIdList !=null">
      AND parent_id IN
      <foreach collection="labelIdList" item="item" open="(" separator="," close=")">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>

  </select>
  <select id="getLabelListByUser" resultType="com.microservice.user.entity.dto.ms.label.LabelInfoDTO">
    select
      li.id,lr.id relationId,
      li.label_name labelName,
      li.parent_id parentId,
      li.status,
      li.create_time createTime,
      li.update_time updateTime
    FROM
      label_relation lr
        JOIN label_info li on li.id = lr.label_id AND lr.status = 1
    WHERE
      lr.status = 1
      <if test="relationId != null and relationId != '' ">
        AND lr.relation_id IN
        <foreach collection="relationId" item="item" open="(" separator="," close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      AND lr.relation_type =#{relationType,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from label_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.microservice.user.entity.dao.LabelInfoDAO">
    insert into label_info (id, label_name, parent_id, status,app_channel_id ,data_channel_id,
       create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{labelName,jdbcType=VARCHAR},#{parentId,jdbcType=VARCHAR}, #{status,jdbcType=BIT}, #{appChannelId,jdbcType=VARCHAR}, #{dataChannelId,jdbcType=VARCHAR},
       #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.microservice.user.entity.dao.LabelInfoDAO" useGeneratedKeys="true" keyProperty="id">
    insert into label_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="labelName != null">
        label_name,
      </if>
        <if test="parentId != null">
        parent_id,
      </if>
      <if test="status != null">
        status,
      </if>

      <if test="dataChannelId != null">
        data_channel_id,
      </if>

      <if test="appChannelId != null">
        app_channel_id,
      </if>

      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="labelName != null">
        #{labelName,jdbcType=VARCHAR},
      </if>
        <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>

      <if test="dataChannelId != null">
        #{dataChannelId,jdbcType=VARCHAR},
      </if>

      <if test="appChannelId != null">
        #{appChannelId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.microservice.user.entity.dao.LabelInfoDAO">
    update label_info
    <set>
      <if test="labelName != null">
        label_name = #{labelName,jdbcType=VARCHAR},
      </if>
        <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="dataChannelId != null">
        data_channel_id = #{dataChannelId,jdbcType=VARCHAR},
      </if>
      <if test="appChannelId != null">
        app_channel_id = #{appChannelId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.microservice.user.entity.dao.LabelInfoDAO">
    update label_info
    set label_name = #{labelName,jdbcType=VARCHAR},
        parent_id = #{parentId,jdbcType=VARCHAR},
      status = #{status,jdbcType=BIT},
      app_channel_id = #{appChannelId,jdbcType=VARCHAR},
      data_channel_id = #{dataChannelId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>