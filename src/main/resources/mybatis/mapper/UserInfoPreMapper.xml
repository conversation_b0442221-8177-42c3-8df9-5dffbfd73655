<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.UserInfoPreMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.UserInfoPreDAO">
        <result column="pre_user_id" property="preUserId"/>
        <result column="user_name" property="userName"/>
        <result column="extra_content" property="extraContent"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="getCompanyDepartmentPeopleNumMap" type="com.microservice.user.entity.dto.ms.CompanyDepartmentPeopleNumDTO">
        <result column="companyId" property="companyId" />
        <result column="departmentId" property="departmentId" />
        <collection property="userIdList" ofType="string">
            <result column="pre_user_id"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        pre_user_id,
        user_name,
        extra_content,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.preUserId">
                AND pre_user_id = #{condition.preUserId}
            </if>
            <if test="null != condition.preUserIdList">
                <foreach collection="condition.preUserIdList" item="preUserId" open="AND pre_user_id IN (" close=")"
                         separator=",">
                    #{preUserId}
                </foreach>
            </if>
            <if test="null != condition.userName">
                AND user_name = #{condition.userName}
            </if>
            <if test="null != condition.userNameList">
                <foreach collection="condition.userNameList" item="userName" open="AND user_name IN (" close=")"
                         separator=",">
                    #{userName}
                </foreach>
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.extraContentInCondition">
                <foreach collection="condition.extraContentInCondition.entrySet()" index="key" item="valList">
                    AND extra_content ->> '$.${key}' IN
                    <foreach collection="valList" item="item" open="(" close=")"
                             separator=",">
                        #{item}
                    </foreach>
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addUserPre" parameterType="com.microservice.user.entity.dao.UserInfoPreDAO">
        INSERT INTO user_info_pre
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != preUserId">
                pre_user_id,
            </if>
            <if test="null != userName">
                user_name,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime ">
                create_time,
            </if>
            <if test="null != updateTime ">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != preUserId">
                #{preUserId},
            </if>
            <if test="null != userName">
                #{userName},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime ">
                #{createTime},
            </if>
            <if test="null != updateTime ">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteUserPre">
        DELETE
        FROM user_info_pre
        <include refid="whereClause"/>
    </delete>

    <update id="updateUserPre" parameterType="com.microservice.user.entity.dao.UserInfoPreDAO">
        UPDATE user_info_pre
        <set>
            <if test="null != userName">user_name = #{userName},</if>
            <if test="null != extraContent">extra_content = #{extraContent},</if>
            <if test="null != updateTime ">update_time = #{updateTime}</if>
        </set>
        WHERE pre_user_id = #{preUserId}
    </update>

    <select id="getUserPre" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_info_pre
        <include refid="whereClause"/>
        LIMIT 1
    </select>




    <select id="getUserPreList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_info_pre
        <include refid="whereClause"/>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getUserPreCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM user_info_pre
        <include refid="whereClause"/>
    </select>




    <select id="getCompanyDepartmentPeopleNum"
            resultMap="getCompanyDepartmentPeopleNumMap">
        SELECT
        tab.companyId,
        tab.departmentId,
        tab.pre_user_id
        FROM
        (
        SELECT
        uip.pre_user_id,
        uip.user_name,
        uip.extra_content,
        JSON_UNQUOTE(uip.extra_content -> '$.companyId') companyId,
        t.departmentId
        FROM
        user_info_pre uip
        LEFT JOIN json_table(CAST(uip.extra_content -> '$.departmentIdList' AS JSON) , '$[*]' columns (departmentId
        VARCHAR(36) path '$')) t on true
        <where>
            uip.extra_content -> '$.companyId' in
            <foreach collection="companyIdList" item="companyId" separator="," open="(" close=")">
                #{companyId}
            </foreach>
            AND uip.extra_content -> '$.status' = 1
        </where>
        ) tab
    </select>
    <select id="getValidUserList" resultType="com.microservice.user.entity.dao.UserInfoPreDAO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            user_info_pre
        WHERE
          JSON_SEARCH(extra_content -> '$.departmentIdList', 'one', #{departmentId}) IS NOT NULL
          AND extra_content -> '$.status' = 1
    </select>
    <update id="deleteDepartment">
        UPDATE user_info_pre
        SET extra_content = JSON_SET(
                extra_content,
                '$.departmentIdList',
                JSON_REMOVE(
                            extra_content -> '$.departmentIdList',
                            JSON_UNQUOTE(
                                    JSON_SEARCH( extra_content -> '$.departmentIdList', 'one', #{departmentId} ))))
        WHERE
            JSON_SEARCH( extra_content -> '$.departmentIdList', 'one', #{departmentId} ) IS NOT NULL
          AND extra_content -> '$.status' = 1;
    </update>
</mapper>