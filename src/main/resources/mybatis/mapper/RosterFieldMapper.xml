<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.RosterFieldMapper">
    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.RosterFieldDAO">
        <id column="id" jdbcType="CHAR" property="id" />
        <result column="roster_rule_team_id" jdbcType="CHAR" property="rosterRuleTeamId" />
        <result column="roster_rule_group_id" jdbcType="CHAR" property="rosterRuleGroupId" />
        <result column="field" jdbcType="VARCHAR" property="field" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="type" jdbcType="TINYINT" property="type" />
        <result column="sort" jdbcType="INTEGER" property="type" />
        <result column="is_non_null" jdbcType="TINYINT" property="isNonNull" />
        <result column="length" jdbcType="INTEGER" property="length" />
        <result column="is_show" jdbcType="TINYINT" property="isShow" />
        <result column="is_System" jdbcType="INTEGER" property="isSystem"/>
        <result column="company_id" jdbcType="CHAR" property="companyId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="app_channel_id" jdbcType="CHAR" property="appChannelId" />
        <result column="data_channel_id" jdbcType="CHAR" property="dataChannelId" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `roster_rule_team_id`,
        `roster_rule_group_id`,
        `field`,
        `name`,
        `type`,
        `sort`,
        `is_non_null`,
        `length`,
        `is_show`,
        `is_system`,
        `company_id`,
        `create_time`,
        `update_time`,
        `app_channel_id`,
        `data_channel_id`,
        `is_delete`
    </sql>

    <sql id="whereClause">
        <where>
            <if test="condition.isDelete != null">
                and is_delete = #{condition.isDelete}
            </if>
            <if test="condition.companyId != null">
                and company_id = #{condition.companyId,jdbcType=CHAR}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="condition.isShow != null ">
                and is_show = #{condition.isShow}
            </if>
            <if test="condition.isSystem != null ">
                and is_systm = #{condition.isSystem}
            </if>
            <if test="condition.name != null and condition.name != ''">
                and name = #{condition.name}
            </if>
            <if test="condition.startDateTime != null and condition.startDateTime != ''">
                and create_time <![CDATA[ >= ]]> #{condition.startDateTime}
            </if>
            <if test="condition.endDateTime != null and condition.endDateTime != ''">
                and create_time <![CDATA[ <= ]]> #{condition.endDateTime}
            </if>
            <if test="condition.primaryKeyIds != null">
                <foreach collection="condition.primaryKeyIds" item="primaryKeyId" open="AND id IN ("
                         close=")"
                         separator=",">
                    #{primaryKeyId}
                </foreach>
            </if>
            <if test="condition.rosterTeamIds != null">
                <foreach collection="condition.rosterTeamIds" item="rosterTeamId" open="AND roster_rule_team_id IN ("
                         close=")"
                         separator=",">
                    #{rosterTeamId}
                </foreach>
            </if>
            <if test="condition.rosterFields != null">
                <foreach collection="condition.rosterFields" item="rosterField" open="AND field IN ("
                         close=")"
                         separator=",">
                    #{rosterField}
                </foreach>
            </if>
            <if test="condition.rosterGroupIds != null">
                <foreach collection="condition.rosterGroupIds" item="rosterGroupId" open="AND roster_rule_group_id IN ("
                         close=")"
                         separator=",">
                    #{rosterGroupId}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from roster_field
        where id = #{id}
    </select>

    <select id="selectRosterFieldList" resultType="com.microservice.user.entity.dao.RosterFieldDAO"
            parameterType="com.microservice.user.entity.condition.GetRosterConfigCondition">
        select
        <include refid="Base_Column_List" />
        from roster_field
        <include refid="whereClause"/>
        ORDER BY sort
        <if test="limit > 0">
            limit #{start},#{limit}
        </if>
    </select>

    <select id="countRosterField" resultType="java.lang.Integer"
            parameterType="com.microservice.user.entity.condition.GetRosterConfigCondition">
        select count(*)
        from roster_field
        <include refid="whereClause"/>
    </select>

    <select id="getSortMax" resultType="java.lang.Integer">
        select max(sort) from roster_field
        <include refid="whereClause"/>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from roster_field
        where id = #{id}
    </delete>

    <insert id="insertRosterField" parameterType="com.microservice.user.entity.dao.RosterFieldDAO" useGeneratedKeys="true"
            keyProperty="id">
        insert into roster_field
            (id,
             roster_rule_team_id,
             roster_rule_group_id,
             field,
             name,
             type,
             sort,
             is_non_null,
             length,
             is_show,
             is_system,
             company_id ,
             create_time,
             update_time,
             app_channel_id,
             data_channel_id,
             is_delete)
        values
            (#{id},
             #{rosterRuleTeamId},
             #{rosterRuleGroupId},
             #{field,jdbcType=VARCHAR},
             #{name,jdbcType=VARCHAR},
             #{type,jdbcType=TINYINT},
             #{sort,jdbcType=INTEGER},
             #{isNonNull,jdbcType=TINYINT},
             #{length,jdbcType=INTEGER},
             #{isShow,jdbcType=TINYINT},
             #{isSystem},
             #{companyId,jdbcType=CHAR},
             #{createTime,jdbcType=TIMESTAMP},
             #{updateTime,jdbcType=TIMESTAMP},
             #{appChannelId,jdbcType=CHAR},
             #{dataChannelId,jdbcType=CHAR},
             #{isDelete,jdbcType=TINYINT})
    </insert>

    <update id="updateRosterField" parameterType="com.microservice.user.entity.dao.RosterFieldDAO">
        update roster_field
        <set>
            <if test="rosterRuleTeamId != null">
                roster_rule_team_id = #{rosterRuleTeamId},
            </if>
            <if test="rosterRuleGroupId != null">
                roster_rule_group_id = #{rosterRuleGroupId},
            </if>
            <if test="field != null and field != ''">
                field = #{field,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=TINYINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="isNonNull != null">
                is_non_null = #{isNonNull,jdbcType=TINYINT},
            </if>
            <if test="length != null">
                `length` = #{length,jdbcType=INTEGER},
            </if>
            <if test="isShow != null">
                is_show = #{isShow,jdbcType=TINYINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>