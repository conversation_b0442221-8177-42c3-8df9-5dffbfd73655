<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.IdentityRosterMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.IdentityRosterDAO">
        <result column="user_name" property="userName"/>
        <result column="identity_id" property="identityId"/>
        <result column="extra_content" property="extraContent"/>
        <result column="join_time" property="joinTime"/>
        <result column="employee_status" property="employeeStatus"/>
        <result column="employee_type" property="employeeType"/>
        <result column="seniority" property="seniority"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="post" property="post"/>
        <result column="job_number" property="jobNumber"/>
        <result column="id_number" property="idNumber"/>
        <result column="id_number_type" property="idNumberType"/>
        <result column="id_photo_front" property="idPhotoFront"/>
        <result column="id_photo_side" property="idPhotoSide"/>
        <result column="birth_date" property="birthDate"/>
        <result column="birth_type" property="birthType"/>
        <result column="extend" property="extend"/>
        <result column="company_id" property="companyId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="select">
        X.`user_name`,
        T.`identity_id`,
        T.`extra_content`,
        T.`extra_content` ->> '$.joinTime' AS join_time,
        T.`extra_content` ->> '$.employeeStatus' AS employee_status,
        T.`extra_content` ->> '$.employeeType' AS employee_type,
        T.`extra_content` ->> '$.seniority' AS `seniority`,
        Y.`dept_id`,
        Y.`dept_name`,
        Z.`post`,
        Z.`job_number`,
        Z.`id_number`,
        Z.`id_number_type`,
        Z.`id_photo_front`,
        Z.`id_photo_side`,
        Z.`birth_date`,
        Z.`birth_type`,
        Z.`extend`,
        Z.`company_id`,
        Z.`create_time`,
        Z.`update_time`
    </sql>

    <select id="findAllByRosterDAOCount" resultType="long"
            parameterType="com.microservice.user.entity.condition.GetRosterCondition">
        SELECT
        COUNT(*)
        FROM
        `identity_info` T
        LEFT JOIN `roster` Z ON Z.`identity_id` = T.`identity_id`
        LEFT JOIN (
        SELECT
        A.`identity_id`,
        GROUP_CONCAT( B.`department_id` ORDER BY B.`department_id` SEPARATOR ',' ) AS `dept_id`,
        GROUP_CONCAT( B.`department_name` ORDER BY B.`department_name` SEPARATOR ',' ) AS `dept_name`
        FROM
        `identity_department_relation` A
        LEFT JOIN `department_info` B ON A.`department_id` = B.`department_id`
        GROUP BY
        A.`identity_id`
        ) Y ON T.`identity_id` = Y.`identity_id`
        LEFT JOIN `user_info` X ON X.`user_id` = T.`user_id`
        INNER JOIN `identity_company_relation` C ON T.`identity_id` = C.`identity_id`
        <where>
            <if test="identityIdList != null">AND T.`identity_id` IN
                <foreach collection="identityIdList" item="identityId" open="(" close=")" separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="entryTimeStart != null and entryTimeStart != ''">AND T.`extra_content` ->> '$.joinTime' &gt;=#{entryTimeStart}</if>
            <if test="entryTimeEnd != null and entryTimeEnd != ''">AND T.`extra_content` ->> '$.joinTime' &lt;= #{entryTimeEnd}</if>
            <if test="appChannelId != null and appChannelId != ''">AND T.`app_channel_id` = #{appChannelId}</if>
            <if test="dataChannelId != null and dataChannelId != ''">AND T.`data_channel_id` = #{dataChannelId}</if>
            <if test="deptId != null and deptId != ''">AND Y.`dept_id` LIKE concat('%',#{deptId},'%')</if>
            <if test="deptName != null and deptName != ''">AND Y.`dept_name` LIKE concat('%',#{deptName},'%')</if>
            <if test="idNumber != null and idNumber != ''">AND Z.`id_number` LIKE concat('%',#{idNumber},'%')</if>
            <if test="userName != null and userName != ''">AND X.`user_name` LIKE concat('%',#{userName},'%')</if>
            <if test="companyId != null and companyId != ''">AND C.`company_id` = #{companyId}</if>
            <if test="idNumberType != null and idNumberType != ''">AND Z.`id_number_type` = #{idNumberType}</if>


            <if test="employeeStatus != null and employeeStatus != '' and employeeStatus == 1">AND (T.`extra_content` ->> '$.employeeStatus' = #{employeeStatus} OR T.`extra_content` ->> '$.employeeStatus' IS NULL)</if>
            <if test="employeeStatus != null and employeeStatus != '' and employeeStatus != 1">AND T.`extra_content` ->> '$.employeeStatus' = #{employeeStatus}</if>
            <if test="employeeType != null and employeeType != '' and employeeType == 1">AND (T.`extra_content` ->> '$.employeeType' =#{employeeType} OR T.`extra_content` ->> '$.employeeType' IS NULL)</if>
            <if test="employeeType != null and employeeType != '' and employeeType != 1">AND T.`extra_content` ->> '$.employeeType' =#{employeeType}</if>
        </where>
    </select>

    <select id="findAllByRosterDAO" parameterType="com.microservice.user.entity.condition.GetRosterCondition"
            resultMap="BaseResultMap">
        SELECT
        <include refid="select"/>
        FROM
        `identity_info` T
        LEFT JOIN `roster` Z ON Z.`identity_id` = T.`identity_id`
        LEFT JOIN (
        SELECT
        A.`identity_id`,
        GROUP_CONCAT( B.`department_id` ORDER BY B.`department_id` SEPARATOR ',' ) AS `dept_id`,
        GROUP_CONCAT( B.`department_name` ORDER BY B.`department_name` SEPARATOR ',' ) AS `dept_name`
        FROM
        `identity_department_relation` A
        LEFT JOIN `department_info` B ON A.`department_id` = B.`department_id`
        GROUP BY
        A.`identity_id`
        ) Y ON T.`identity_id` = Y.`identity_id`
        LEFT JOIN `user_info` X ON X.`user_id` = T.`user_id`
        INNER JOIN `identity_company_relation` C ON T.`identity_id` = C.`identity_id`
        <where>
            <if test="identityIdList != null">AND T.`identity_id` IN
                <foreach collection="identityIdList" item="identityId" open="(" close=")" separator=",">
                    #{identityId}
                </foreach>
            </if>
            <if test="entryTimeStart != null and entryTimeStart != ''">AND T.`extra_content` ->> '$.joinTime' &gt;=#{entryTimeStart}</if>
            <if test="entryTimeEnd != null and entryTimeEnd != ''">AND T.`extra_content` ->> '$.joinTime' &lt;= #{entryTimeEnd}</if>
            <if test="appChannelId != null and appChannelId != ''">AND T.`app_channel_id` = #{appChannelId}</if>
            <if test="dataChannelId != null and dataChannelId != ''">AND T.`data_channel_id` = #{dataChannelId}</if>
            <if test="deptId != null and deptId != ''">AND Y.`dept_id` LIKE concat('%',#{deptId},'%')</if>
            <if test="deptName != null and deptName != ''">AND Y.`dept_name` LIKE concat('%',#{deptName},'%')</if>
            <if test="idNumber != null and idNumber != ''">AND Z.`id_number` LIKE concat('%',#{idNumber},'%')</if>
            <if test="userName != null and userName != ''">AND X.`user_name` LIKE concat('%',#{userName},'%')</if>
            <if test="companyId != null and companyId != ''">AND C.`company_id` = #{companyId}</if>
            <if test="idNumberType != null and idNumberType != ''">AND Z.`id_number_type` = #{idNumberType}</if>


            <if test="employeeStatus != null and employeeStatus != '' and employeeStatus == 1">AND (T.`extra_content` ->> '$.employeeStatus' = #{employeeStatus} OR T.`extra_content` ->> '$.employeeStatus' IS NULL)</if>
            <if test="employeeStatus != null and employeeStatus != '' and employeeStatus != 1">AND T.`extra_content` ->> '$.employeeStatus' = #{employeeStatus}</if>
            <if test="employeeType != null and employeeType != '' and employeeType == 1">AND (T.`extra_content` ->> '$.employeeType' =#{employeeType} OR T.`extra_content` ->> '$.employeeType' IS NULL)</if>
            <if test="employeeType != null and employeeType != '' and employeeType != 1">AND T.`extra_content` ->> '$.employeeType' =#{employeeType}</if>
        </where>
        ORDER BY `join_time` DESC
    </select>

    <select id="findByIdentityId" parameterType="string" resultMap="BaseResultMap">
        SELECT
        <include refid="select"/>
        FROM
        `identity_info` T
        LEFT JOIN `roster` Z ON Z.`identity_id` = T.`identity_id`
        LEFT JOIN (
        SELECT
        A.`identity_id`,
        GROUP_CONCAT( B.`department_id` ORDER BY B.`department_id` SEPARATOR ',' ) AS `dept_id`,
        GROUP_CONCAT( B.`department_name` ORDER BY B.`department_name` SEPARATOR ',' ) AS `dept_name`
        FROM
        `identity_department_relation` A
        LEFT JOIN `department_info` B ON A.`department_id` = B.`department_id`
        LEFT JOIN `identity_company_relation` C ON A.`identity_id` = C.`identity_id`
        GROUP BY
        A.`identity_id`
        ) Y ON T.`identity_id` = Y.`identity_id`
        LEFT JOIN `user_info` X ON X.`user_id` = T.`user_id`
        <where>
            <if test="identityId != null and identityId != ''">AND T.`identity_id` = #{identityId}</if>
        </where>
    </select>

    <select id="getRosterByIdentityId" resultType="com.microservice.user.entity.dao.RosterDAO" parameterType="string">
        SELECT `identity_id`,
               `user_name`,
               `post`,
               `job_number`,
               `id_number`,
               `id_number_type`,
               `id_photo_front`,
               `id_photo_side`,
               `birth_date`,
               `birth_type`,
               `extend`,
               `company_id`,
               `create_time`,
               `update_time`,
               `app_channel_id`,
               `data_channel_id`
        FROM `roster`
        WHERE `identity_id` = #{identityId}
    </select>

    <insert id="addRosterDAO" parameterType="com.microservice.user.entity.dao.RosterDAO">
        INSERT INTO `roster`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="identityId != null">`identity_id`,</if>
            <if test="userName != null">`user_name`,</if>
            <if test="post != null">`post`,</if>
            <if test="jobNumber != null">`job_number`,</if>
            <if test="idNumber != null">`id_number`,</if>
            <if test="idNumberType != null">`id_number_type`,</if>
            <if test="idPhotoFront != null">`id_photo_front`,</if>
            <if test="idPhotoSide != null">`id_photo_side`,</if>
            <if test="birthDate != null">`birth_date`,</if>
            <if test="birthType != null">`birth_type`,</if>
            <if test="extend != null">`extend`,</if>
            <if test="companyId != null">`company_id`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
            <if test="appChannelId != null">`app_channel_id`,</if>
            <if test="dataChannelId != null">`data_channel_id`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="identityId != null">#{identityId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="post != null">#{post},</if>
            <if test="jobNumber != null">#{jobNumber},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="idNumberType != null">#{idNumberType},</if>
            <if test="idPhotoFront != null">#{idPhotoFront},</if>
            <if test="idPhotoSide != null">#{idPhotoSide},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="birthType != null">#{birthType},</if>
            <if test="extend != null">#{extend},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="appChannelId != null">#{appChannelId},</if>
            <if test="dataChannelId != null">#{dataChannelId},</if>
        </trim>
    </insert>

    <update id="updateRosterDAO" parameterType="com.microservice.user.entity.dao.IdentityRosterDAO">
        UPDATE `roster`
        <set>
            <if test="userName != null">`user_name` = #{userName},</if>
            <if test="post != null">`post` = #{post},</if>
            <if test="jobNumber != null">`job_number` = #{jobNumber},</if>
            <if test="idNumber != null">`id_number` = #{idNumber},</if>
            <if test="idNumberType != null">`id_number_type` = #{idNumberType},</if>
            <if test="idPhotoFront != null">`id_photo_front` = #{idPhotoFront},</if>
            <if test="idPhotoSide != null">`id_photo_side` = #{idPhotoSide},</if>
            <if test="birthDate != null">`birth_date` = #{birthDate},</if>
            <if test="birthType != null">`birth_type` = #{birthType},</if>
            <if test="extend != null">`extend` = #{extend},</if>
            <if test="companyId != null">`company_id` = #{companyId},</if>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
            <if test="appChannelId != null">`app_channel_id` = #{appChannelId},</if>
            <if test="dataChannelId != null">`data_channel_id` = #{dataChannelId},</if>
        </set>
        WHERE `identity_id` = #{identityId}
    </update>

    <delete id="deleteRosterDAO">
        DELETE
        FROM `roster`
        WHERE `identity_id` = #{identityId}
    </delete>

</mapper>