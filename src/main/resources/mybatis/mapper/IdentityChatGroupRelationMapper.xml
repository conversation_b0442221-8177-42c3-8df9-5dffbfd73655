<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.IdentityChatGroupRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.IdentityChatGroupRelationDAO" >
        <result column="identity_id" property="identityId" />
        <result column="chat_group_id" property="chatGroupId" />
        <result column="extra_content" property="extraContent" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        identity_id
        ,
        chat_group_id,
        extra_content,
        create_time,
        update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.microservice.user.entity.dao.IdentityChatGroupRelationDAO">
        INSERT INTO identity_chat_group_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                identity_id,
            </if>
            <if test="null != chatGroupId">
                chat_group_id,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != identityId">
                #{identityId},
            </if>
            <if test="null != chatGroupId">
                #{chatGroupId},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteIdentityChatGroupRelation" >
        DELETE FROM identity_chat_group_relation
        <where>
            <if test="null != condition.chatGroupId">
                AND chat_group_id = #{condition.chatGroupId}
            </if>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
            <if test="null != condition.chatGroupIdList">
                <foreach collection="condition.chatGroupIdList" item="chatGroupId" open="AND chat_group_id IN (" close=")"
                         separator=",">
                    #{chatGroupId}
                </foreach>
            </if>
            <if test="null != condition.identityIdList">
                <foreach collection="condition.identityIdList" item="identityId" open="AND identity_id IN (" close=")"
                         separator=",">
                    #{identityId}
                </foreach>
            </if>
        </where>
    </delete>

    <update id="updateIdentityChatGroupRelation">
        UPDATE identity_chat_group_relation
        <set>
            <if test="null != value.identityId">identity_id = #{value.identityId},</if>
            <if test="null != value.chatGroupId">chat_group_id = #{value.chatGroupId},</if>
            <if test="null != value.extraContent">extra_content = #{value.extraContent},</if>
            <if test="null != value.createTime">create_time = #{value.createTime},</if>
            <if test="null != value.updateTime">update_time = #{value.updateTime}</if>
        </set>
        <where>
            <if test="null != condition.chatGroupId">
                AND chat_group_id = #{condition.chatGroupId}
            </if>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
        </where>
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM identity_chat_group_relation
        WHERE id = #{id}
    </select>

    <select id="getIdentityChatGroupRelationList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM identity_chat_group_relation
        <where>
            <if test="null != condition.chatGroupId">
                AND chat_group_id = #{condition.chatGroupId}
            </if>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
        </where>
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM identity_chat_group_relation
    </select>

    <insert id="addIdentityChatGroupRelationList">
        INSERT INTO identity_chat_group_relation
        (identity_id, chat_group_id, extra_content)
        VALUES
        <foreach collection="dataList" item="identityChatGroupRelation" index="index"
                 separator=",">
            (#{identityChatGroupRelation.identityId}, #{identityChatGroupRelation.chatGroupId}, #{identityChatGroupRelation.extraContent})
        </foreach>
    </insert>

    <select id="getIdentityChatGroupRelationCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM identity_chat_group_relation
        <where>
            <if test="null != condition.chatGroupId">
                AND chat_group_id = #{condition.chatGroupId}
            </if>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
        </where>
    </select>

    <select id="getIdentityChatGroupRelation" resultType="com.microservice.user.entity.dao.IdentityChatGroupRelationDAO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM identity_chat_group_relation
        <where>
            <if test="null != condition.chatGroupId">
                AND chat_group_id = #{condition.chatGroupId}
            </if>
            <if test="null != condition.identityId">
                AND identity_id = #{condition.identityId}
            </if>
        </where>
        LIMIT 1
    </select>
</mapper>