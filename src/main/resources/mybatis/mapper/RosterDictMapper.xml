<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.RosterDictMapper">
  <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.RosterDictDAO">
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="roster_rule_team_id" jdbcType="CHAR" property="rosterRuleTeamId" />
    <result column="roster_rule_group_id" jdbcType="CHAR" property="rosterRuleGroupId" />
    <result column="roster_rule_field_id" jdbcType="VARCHAR" property="rosterRuleFieldId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="app_channel_id" jdbcType="CHAR" property="appChannelId" />
    <result column="data_channel_id" jdbcType="CHAR" property="dataChannelId" />
  </resultMap>

  <sql id="Base_Column_List">
    id, roster_rule_team_id, roster_rule_group_id, roster_rule_field_id, name, value, 
    company_id, app_channel_id, data_channel_id
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from roster_dict
    where id = #{id}
  </select>

  <select id="selectRosterDictList" resultType="com.microservice.user.entity.dao.RosterDictDAO"
            parameterType="com.microservice.user.entity.condition.GetRosterConfigCondition">
    select
    <include refid="Base_Column_List" />
    from roster_dict
    <where>
      <if test="condition.companyId != null">
        and company_id = #{condition.companyId,jdbcType=CHAR}
      </if>
      <if test="null != condition.appChannelId">
        AND app_channel_id = #{condition.appChannelId}
      </if>
      <if test="null != condition.dataChannelId">
        AND data_channel_id = #{condition.dataChannelId}
      </if>
      <if test="condition.rosterFields != null">
        <foreach collection="condition.rosterFields" item="rosterField" open="AND roster_rule_field_id IN ("
                 close=")"
                 separator=",">
          #{rosterField}
        </foreach>
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from roster_dict
    where id = #{id}
  </delete>

  <delete id="deleteByField">
    delete from roster_dict
    where roster_rule_field_id = #{field}
    and app_channel_id = #{appChannelId}
    and data_channel_id = #{dataChannelId}
    and company_id = #{companyId}
  </delete>

  <insert id="insertDic" parameterType="com.microservice.user.entity.dao.RosterDictDAO" useGeneratedKeys="true" keyProperty="id">
    insert into roster_dict
        (id,
         roster_rule_team_id,
         roster_rule_group_id,
         roster_rule_field_id,
         name,
         value,
         company_id,
         app_channel_id,
         data_channel_id
      )
    values
        (#{id},
         #{rosterRuleTeamId},
         #{rosterRuleGroupId},
         #{rosterRuleFieldId},
         #{name,jdbcType=VARCHAR},
         #{value,jdbcType=VARCHAR},
         #{companyId,jdbcType=CHAR},
         #{appChannelId,jdbcType=CHAR},
         #{dataChannelId,jdbcType=CHAR}
      )
  </insert>

  <insert id="insertSelective" parameterType="com.microservice.user.entity.dao.RosterDictDAO">
    insert into roster_dict
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="rosterRuleTeamId != null">
        roster_rule_team_id,
      </if>
      <if test="rosterRuleGroupId != null">
        roster_rule_group_id,
      </if>
      <if test="rosterRuleFieldId != null">
        roster_rule_field_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="value != null">
        value,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="appChannelId != null">
        app_channel_id,
      </if>
      <if test="dataChannelId != null">
        data_channel_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="rosterRuleTeamId != null">
        #{rosterRuleTeamId},
      </if>
      <if test="rosterRuleGroupId != null">
        #{rosterRuleGroupId},
      </if>
      <if test="rosterRuleFieldId != null">
        #{rosterRuleFieldId},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="appChannelId != null">
        #{appChannelId,jdbcType=CHAR},
      </if>
      <if test="dataChannelId != null">
        #{dataChannelId,jdbcType=CHAR},
      </if>
    </trim>
  </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.microservice.user.entity.dao.RosterDictDAO">
    update roster_dict
    <set>
      <if test="rosterRuleTeamId != null">
        roster_rule_team_id = #{rosterRuleTeamId},
      </if>
      <if test="rosterRuleGroupId != null">
        roster_rule_group_id = #{rosterRuleGroupId},
      </if>
      <if test="rosterRuleFieldId != null">
        roster_rule_field_id = #{rosterRuleFieldId},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="appChannelId != null">
        app_channel_id = #{appChannelId,jdbcType=CHAR},
      </if>
      <if test="dataChannelId != null">
        data_channel_id = #{dataChannelId,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id}
  </update>

</mapper>