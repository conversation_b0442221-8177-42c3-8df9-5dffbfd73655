<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.CompanyDepartmentRelationMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.CompanyDepartmentRelationDAO">
        <result column="company_id" property="companyId"/>
        <result column="department_id" property="departmentId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        company_id
        ,
        department_id,
        create_time
    </sql>

    <sql id="getCDRWhereClause">
        <where>
            <if test="null != condition.companyId">
                AND cdr.company_id = #{condition.companyId}
            </if>
            <if test="null != condition.departmentId">
                AND cdr.department_id = #{condition.departmentId}
            </if>
            <if test="null != condition.companyIdList">
                <foreach collection="condition.companyIdList" item="companyId" open="AND cdr.company_id IN ("
                         close=")"
                         separator=",">
                    #{companyId}
                </foreach>
            </if>
            <if test="null != condition.departmentIdList">
                <foreach collection="condition.departmentIdList" item="departmentId" open="AND cdr.department_id IN ("
                         close=")"
                         separator=",">
                    #{departmentId}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.companyId">
                AND company_id = #{condition.companyId}
            </if>
            <if test="null != condition.departmentId">
                AND department_id = #{condition.departmentId}
            </if>
            <if test="null != condition.companyIdList">
                <foreach collection="condition.companyIdList" item="companyId" open="AND company_id IN ("
                         close=")"
                         separator=",">
                    #{companyId}
                </foreach>
            </if>
            <if test="null != condition.departmentIdList">
                <foreach collection="condition.departmentIdList" item="departmentId" open="AND department_id IN ("
                         close=")"
                         separator=",">
                    #{departmentId}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addCompanyDepartmentRelation"
            parameterType="com.microservice.user.entity.dao.CompanyDepartmentRelationDAO">
        INSERT INTO company_department_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != companyId">
                company_id,
            </if>
            <if test="null != departmentId">
                department_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != companyId">
                #{companyId},
            </if>
            <if test="null != departmentId">
                #{departmentId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
        </trim>
    </insert>

    <delete id="delete">
        DELETE
        FROM company_department_relation
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.microservice.user.entity.dao.CompanyDepartmentRelationDAO">
        UPDATE company_department_relation
        <set>
            <if test="null != companyId">company_id = #{companyId},</if>
            <if test="null != departmentId">department_id = #{departmentId},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM company_department_relation
        WHERE id = #{id}
    </select>

    <select id="getCompanyDepartmentRelationList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM company_department_relation
        <include refid="whereClause"/>
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM company_department_relation
        <include refid="whereClause"/>
    </select>

    <delete id="deleteCDRByCompanyId">
        DELETE FROM company_department_relation
        <include refid="whereClause"/>
    </delete>

    <select id="getCDRWithDepartmentList" resultType="com.microservice.user.entity.dao.CDRWithDepartmentDAO">
        SELECT cdr.*, di.*
        FROM company_department_relation cdr
        INNER JOIN department_info di ON cdr.department_id = di.department_id
        <include refid="getCDRWhereClause"/>
        ORDER BY di.sort ASC, di.create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getCDRWithDepartmentCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM company_department_relation cdr
        INNER JOIN department_info di ON cdr.department_id = di.department_id
        <include refid="getCDRWhereClause"/>
    </select>

    <delete id="deleteCompanyDepartmentRelation">
        DELETE FROM `company_department_relation`
        <include refid="whereClause"/>
    </delete>

    <select id="queryByCompanyId" resultType="com.microservice.user.entity.dto.ms.DepartmentDTO">
        SELECT
            di.department_id,
            di.department_name,
            di.parent_id
        FROM
            company_department_relation cdr
            INNER JOIN department_info di ON cdr.department_id = di.department_id
        where cdr.company_id = #{companyId};
    </select>
</mapper>