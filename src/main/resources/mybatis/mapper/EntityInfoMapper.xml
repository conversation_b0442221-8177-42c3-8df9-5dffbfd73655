<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.EntityInfoMapper">

    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.EntityInfoDAO">
        <result column="entity_id" property="entityId"/>
        <result column="entity_name" property="entityName"/>
        <result column="type" property="type"/>
        <result column="extra_content" property="extraContent"/>
        <result column="sort" property="sort"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        entity_id
        ,
        entity_name,
        type,
        extra_content,
        sort,
        app_channel_id,
        data_channel_id,
        create_time,
        update_time
    </sql>

    <sql id="whereClause">
        <where>
            <if test="null != condition.entityId">
                AND entity_id = #{condition.entityId}
            </if>
            <if test="null != condition.entityIdList">
                <foreach collection="condition.entityIdList" item="entityId" open="AND entity_id IN (" close=")"
                         separator=",">
                    #{entityId}
                </foreach>
            </if>
            <if test="null != condition.type">
                AND type = #{condition.type}
            </if>
            <if test="null != condition.appChannelId">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.extraContentCondition">
                <foreach collection="condition.extraContentCondition.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="addEntity" parameterType="com.microservice.user.entity.dao.EntityInfoDAO">
        INSERT INTO entity_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != entityId">
                entity_id,
            </if>
            <if test="null != entityName">
                entity_name,
            </if>
            <if test="null != type">
                type,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != sort">
                sort,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != entityId">
                #{entityId},
            </if>
            <if test="null != entityName">
                #{entityName},
            </if>
            <if test="null != type">
                #{type},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != sort">
                #{sort},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="deleteEntity">
        DELETE
        FROM entity_info
        <include refid="whereClause"/>
    </delete>

    <update id="updateEntity" parameterType="com.microservice.user.entity.dao.EntityInfoDAO">
        UPDATE entity_info
        <set>
            <if test="null != entityId">entity_id = #{entityId},</if>
            <if test="null != entityName">entity_name = #{entityName},</if>
            <if test="null != type">type = #{type},</if>
            <if test="null != extraContent">extra_content = #{extraContent},</if>
            <if test="null != sort">sort = #{sort},</if>
            <if test="null != appChannelId">app_channel_id = #{appChannelId},</if>
            <if test="null != dataChannelId">data_channel_id = #{dataChannelId},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        <include refid="whereClause"/>
    </update>


    <select id="getEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM entity_info
        <include refid="whereClause"/>
        LIMIT 1
    </select>

    <select id="getEntityList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM entity_info
        <include refid="whereClause"/>
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getEntityCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM entity_info
        <include refid="whereClause"/>
    </select>

</mapper>