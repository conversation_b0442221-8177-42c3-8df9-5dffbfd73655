<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.microservice.user.mapper.LabelRelationMapper">
    <resultMap id="BaseResultMap" type="com.microservice.user.entity.dao.LabelRelationDAO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="label_id" jdbcType="INTEGER" property="labelId"/>
        <result column="relation_id" jdbcType="VARCHAR" property="relationId"/>
        <result column="relation_type" jdbcType="BIT" property="relationType"/>
        <result column="status" jdbcType="BIT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="extra_content" jdbcType="LONGVARCHAR" property="extraContent"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , label_id, relation_id, relation_type, status, create_time, update_time,extra_content
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from label_relation
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByLabelId" resultType="com.microservice.user.entity.dto.ms.label.LabelMemberDTO">
        SELECT temp.*
        FROM (SELECT
                  lr.id,
                  lr.label_id labelId,
                  lr.relation_id relationId,
                  lr.relation_type relationType,
                  ci.company_name name ,pci.company_name parentName
              FROM
                  label_relation lr
                      JOIN company_info ci ON ci.company_id = lr.relation_id  AND lr.relation_type = 3
                      LEFT JOIN company_info pci ON pci.company_id = ci.parent_id
              WHERE
                lr.STATUS = 1
                <if test="labelIdList != null and labelIdList.size>0">
                  AND lr.label_id IN
                  <foreach collection="labelIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                  </foreach>
                </if>
                <if test="labelId!=null and labelId!=''">
                    and lr.label_id = #{labelId,jdbcType=VARCHAR}
                </if>
                  UNION
        SELECT lr.id,
                     lr.label_id         labelId,lr.relation_id relationId,lr.relation_type relationType,
                     dr.department_name  NAME,
                     pdr.department_name parentName
              FROM label_relation lr
                       JOIN department_info dr ON dr.department_id = lr.relation_id
                  AND lr.relation_type = 1
                       LEFT JOIN department_info pdr ON pdr.department_id = dr.parent_id
              WHERE
        lr.STATUS = 1
        <if test="labelIdList != null and labelIdList.size>0">
            AND lr.label_id IN
            <foreach collection="labelIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="labelId!=null and labelId!=''">
            and lr.label_id = #{labelId,jdbcType=VARCHAR}
        </if>
              UNION
              SELECT id,
                     labelId, relationId,relationType,
                     uname,
                     GROUP_CONCAT(parentName)
              FROM (SELECT lr.id,
                           lr.label_id                                         labelId,lr.relation_id relationId,lr.relation_type relationType,
        REPLACE (
        CASE WHEN iden.extra_content -> '$.stageName' IS NULL OR iden.extra_content -> '$.stageName' = '' THEN
        CASE WHEN ui.extra_content -> '$.realName' IS NULL OR ui.extra_content -> '$.realName' = '' THEN
        ui.extra_content -> '$.nickname'
        ELSE ui.extra_content -> '$.realName'  END
        ELSE iden.extra_content -> '$.stageName' END,
        '"',
        ''
        ) uname,
                           dr.department_name                                  parentName
                    FROM label_relation lr
                             JOIN identity_info iden ON iden.identity_id = lr.relation_id
                        AND lr.relation_type = 2
                             JOIN user_info ui ON ui.user_id = iden.user_id
                             LEFT JOIN identity_department_relation idr ON idr.identity_id = iden.identity_id
                             LEFT JOIN department_info dr ON dr.department_id = idr.department_id
                    WHERE
                        lr.STATUS = 1 and iden.extra_content ->'$.employeeStatus' = 1
                        <if test="labelIdList != null and labelIdList.size>0">
                            AND lr.label_id IN
                            <foreach collection="labelIdList" item="item" index="index" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </if>
                        <if test="labelId!=null and labelId!=''">
                            and lr.label_id = #{labelId,jdbcType=VARCHAR}
                        </if>
                       ) temp_tab
              GROUP BY temp_tab.uname,
                       id) temp WHERE 1=1
        <if test="condition != null">
            AND temp.name LIKE CONCAT('%', #{condition}, '%')
        </if>
        order by temp.id desc
    </select>
    <select id="selectByLabelIdAndRelationId"
            resultType="com.microservice.user.entity.dao.LabelRelationDAO">
        select
        <include refid="Base_Column_List"/>
        from label_relation
        where label_id = #{labelId,jdbcType=VARCHAR} and relation_id =#{relationId,jdbcType=VARCHAR} and status =1 and relation_type = #{relationType,jdbcType=INTEGER}
    </select>

    <select id="selectRelationIdsByLabelIds" resultType="map">
        SELECT
        label_id as labelId,
        relation_id as relationId
        FROM label_relation
        WHERE
        label_id IN
        <foreach item="id" collection="labelIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND relation_type = #{relationType}
        AND status = 1
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from label_relation
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.microservice.user.entity.dao.LabelRelationDAO">
        insert into label_relation (id, label_id, relation_id,
                                    relation_type, status, create_time,
                                    update_time, extra_content)
        values (#{id,jdbcType=INTEGER}, #{labelId,jdbcType=INTEGER}, #{relationId,jdbcType=VARCHAR},
                #{relationType,jdbcType=BIT}, #{status,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{extraContent,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.microservice.user.entity.dao.LabelRelationDAO" useGeneratedKeys="true" keyProperty="id">
        insert into label_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="labelId != null">
                label_id,
            </if>
            <if test="relationId != null">
                relation_id,
            </if>
            <if test="relationType != null">
                relation_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="extraContent != null">
                extra_content,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="labelId != null">
                #{labelId,jdbcType=INTEGER},
            </if>
            <if test="relationId != null">
                #{relationId,jdbcType=VARCHAR},
            </if>
            <if test="relationType != null">
                #{relationType,jdbcType=BIT},
            </if>
            <if test="status != null">
                #{status,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="extraContent != null">
                #{extraContent,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO label_relation (id, label_id, relation_id,
        relation_type, status, create_time,
        update_time, extra_content)
        VALUES
        <foreach collection="labelRelationDAOS" item="labelRelationDAO" separator=",">
            (#{labelRelationDAO.id}, #{labelRelationDAO.labelId}, #{labelRelationDAO.relationId},
            #{labelRelationDAO.relationType}, #{labelRelationDAO.status}
            , #{labelRelationDAO.createTime}, #{labelRelationDAO.updateTime}, #{labelRelationDAO.extraContent})
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.microservice.user.entity.dao.LabelRelationDAO">
        update label_relation
        <set>
            <if test="labelId != null">
                label_id = #{labelId,jdbcType=INTEGER},
            </if>
            <if test="relationId != null">
                relation_id = #{relationId,jdbcType=VARCHAR},
            </if>
            <if test="relationType != null">
                relation_type = #{relationType,jdbcType=BIT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="extraContent != null">
                extra_content = #{extraContent,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.microservice.user.entity.dao.LabelRelationDAO">
        update label_relation
        set label_id      = #{labelId,jdbcType=INTEGER},
            relation_id   = #{relationId,jdbcType=VARCHAR},
            relation_type = #{relationType,jdbcType=BIT},
            status        = #{status,jdbcType=BIT},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            extra_content = #{extraContent,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.microservice.user.entity.dao.LabelRelationDAO">
        update label_relation
        set label_id      = #{labelId,jdbcType=INTEGER},
            relation_id   = #{relationId,jdbcType=VARCHAR},
            relation_type = #{relationType,jdbcType=BIT},
            status        = #{status,jdbcType=BIT},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateStatusByLabelId">
        update label_relation
        set status = #{status,jdbcType=BIT}
        where label_id = #{labelId,jdbcType=VARCHAR}
    </update>
</mapper>