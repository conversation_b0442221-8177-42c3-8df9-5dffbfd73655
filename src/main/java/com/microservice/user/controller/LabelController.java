package com.microservice.user.controller;

import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.label.LabelDataListDTO;
import com.microservice.user.entity.dto.ms.label.LabelExistDTO;
import com.microservice.user.entity.dto.ms.label.LabelInfoDTO;
import com.microservice.user.entity.dto.ms.label.LabelMemberDTO;
import com.microservice.user.entity.request.ms.label.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.LabelService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/label")
public class LabelController {

    private final LabelService labelService;

    @PostMapping("/addLabel")
    public MsResponse<LabelInfoDTO> addLabel(@RequestBody @Valid AddLabelRequest request) {
        LabelInfoDTO label = labelService.addLabel(request);
        return MsResponse.success(label);
    }


    @PostMapping("/updateLabel")
    public MsResponse<LabelInfoDTO> updateLabel(@RequestBody @Valid UpdateLabelRequest request) {
        LabelInfoDTO label = labelService.updateLabel(request);
        return MsResponse.success(label);
    }


    @PostMapping("/getLabelList")
    public MsResponse<DataListDTO<LabelInfoDTO>> getLabelList(@RequestBody @Valid LabelQueryRequest request) {
        DataListDTO<LabelInfoDTO> labelList = labelService.getLabelList(request);
        return MsResponse.success(labelList);
    }

    @PostMapping("/addLabelMembers")
    public MsResponse addLabelMembers(@RequestBody @Valid AddLabelMembersRequest request) {
        labelService.addLabelMembers(request);
        return MsResponse.success();
    }

    @PostMapping("/getLabelMembers")
    public MsResponse<LabelDataListDTO<LabelMemberDTO>> getLabelMembers(@RequestBody @Valid GetLabelMembersRequest request) {
        LabelDataListDTO<LabelMemberDTO> labelMembers = labelService.getLabelMembers(request);
        return MsResponse.success(labelMembers);
    }

    @PostMapping("/updateLabelMember")
    public MsResponse updateLabelMember(@RequestBody @Valid UpdateLabelMemberRequest request) {
        labelService.updateLabelMember(request);
        return MsResponse.success();
    }

    @PostMapping("/setLabelManageScope")
    public MsResponse setLabelManageScope(@RequestBody @Valid SetLabelManageScopeRequest request) {
        labelService.setLabelManageScope(request);
        return MsResponse.success();
    }


    /**
     * 获取标签审核人id
     * @param request
     * @return
     */
    @PostMapping("/getLabelAuditorId")
    public MsResponse getLabelAuditorId(@RequestBody @Valid GetLabelAuditorIdRequest request) {
        List<String> userIds = labelService.getLabelAuditorId(request);
        return MsResponse.success(userIds);
    }

    /**
     * 获取用户标签
     * @param request
     * @return
     */
    @PostMapping("/getLabelListByUser")
    public MsResponse<DataListDTO<LabelInfoDTO>> getLabelListByUser(@RequestBody @Valid QueryUserLabelRequest request) {
        DataListDTO<LabelInfoDTO> labelList = labelService.getLabelListByUser(request);
        return MsResponse.success(labelList);
    }


    @PostMapping("/addLabelMembersByIdentity")
    public MsResponse addLabelMembersByIdentity(@RequestBody @Valid AddLabelMembersRequest request) {
        labelService.addLabelMembersByIdentity(request);
        return MsResponse.success();
    }



}
