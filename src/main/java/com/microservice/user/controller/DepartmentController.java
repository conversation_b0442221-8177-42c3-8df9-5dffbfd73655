package com.microservice.user.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.DepartmentConstant;
import com.microservice.user.entity.condition.GetCompanyDepartmentCondition;
import com.microservice.user.entity.condition.GetDepartmentCondition;
import com.microservice.user.entity.condition.GetIdentityDepartmentCondition;
import com.microservice.user.entity.dao.CDRWithDepartmentDAO;
import com.microservice.user.entity.dao.CompanyDepartmentRelationDAO;
import com.microservice.user.entity.dao.DepartmentInfoDAO;
import com.microservice.user.entity.dao.IDRWithIdentityDAO;
import com.microservice.user.entity.dto.ms.*;
import com.microservice.user.entity.request.ms.department.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.CompanyDepartmentService;
import com.microservice.user.service.CompanyService;
import com.microservice.user.service.DepartmentService;
import com.microservice.user.service.IdentityDepartmentService;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/department")
public class DepartmentController {

    private List<String> deptIdList = null;
    private final Lock lock = new ReentrantLock();

    @Autowired
    private CompanyDepartmentService companyDepartmentServic;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private CompanyDepartmentService companyDepartmentService;
    @Autowired
    private IdentityDepartmentService identityDepartmentService;

    @RequestMapping("/getDepartmentList")
    public MsResponse<DataListDTO<DepartmentDTO>> getDepartmentList(@RequestBody GetDepartmentListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        //构造查询条件
        Map<String, Object> conditionMap = new HashMap<>();

        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), DepartmentConstant.DEPARTMENT_MAIN_COLUMN);
        //构造查询条件
        GetDepartmentCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetDepartmentCondition.class);
        //开始查询
        List<DepartmentDTO> departmentDTOList = departmentService.getDepartmentList(condition, (page - 1) * limit, limit);
        //查询部门和公司关系
        List<CompanyDepartmentRelationDAO> cdrList = companyDepartmentService.getCDRList
                (GetCompanyDepartmentCondition.builder()
                        .departmentIdList(departmentDTOList.stream().map(DepartmentDTO::getDepartmentId)
                                .collect(Collectors.toList()))
                        .build());
        Map<String, String> departmentId2CompanyIdMap = cdrList.stream().collect(Collectors
                .toMap(CompanyDepartmentRelationDAO::getDepartmentId, CompanyDepartmentRelationDAO::getCompanyId, (dao1, dao2) -> dao1));
        // 查询部门下的身份ID列表
        Map<String, List<String>> deptIdentityMap = new HashMap<>();
        if (Objects.equals(request.getIsGetUser(), 1)) {
            List<String> departmentIds = departmentDTOList.stream()
                    .map(DepartmentDTO::getDepartmentId)
                    .collect(Collectors.toList());
            deptIdentityMap = identityDepartmentService.batchGetIdentityIdsByDepartmentList(departmentIds);
        } else {
            deptIdentityMap = new HashMap<>();
        }

        // 查询部门及其父部门的id集合
        List<String> departmentIdSet;
        if (Objects.equals(request.getIsGetFatherAndMeIds(), 1)) {
            departmentIdSet = new ArrayList<>();
            List<String> departmentIds = departmentDTOList.stream()
                    .map(DepartmentDTO::getDepartmentId)
                    .collect(Collectors.toList());
            List<DepartmentFatherAndSonDTO> departmentFatherAndSon = identityDepartmentService.getDepartmentFatherAndSon(departmentIds);
            departmentFatherAndSon.stream().map(DepartmentFatherAndSonDTO::getDepartmentId).forEach(departmentIdSet::add);
        } else {
            departmentIdSet = new ArrayList<>();
        }


        Map<String, List<String>> finalDeptIdentityMap = deptIdentityMap;

        return MsResponse.success(
                DataListDTO.<DepartmentDTO>builder()
                        .dataCount(departmentService.getDepartmentCount(condition))
                        .dataList(departmentDTOList.stream()
                                .peek(departmentDTO -> {
                                    departmentDTO.setCompanyId(departmentId2CompanyIdMap.getOrDefault(departmentDTO.getDepartmentId(), ""));
                                    departmentDTO.setIdentityIds(finalDeptIdentityMap.getOrDefault(departmentDTO.getDepartmentId(), Collections.emptyList()));
                                    // 加入部门及其父部门的id集合
                                    departmentDTO.setAllDepartmentIds(departmentIdSet);
                                })
                                .collect(Collectors.toList()))
                        .build()
        );
    }

    @RequestMapping("/getDepartmentCount")
    public MsResponse<Map<String, Integer>> getDepartmentCount(@RequestBody GetDepartmentListRequest request) {
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), DepartmentConstant.DEPARTMENT_MAIN_COLUMN);
        //构造查询条件
        GetDepartmentCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetDepartmentCondition.class);
        //开始查询
        Integer dataCount = departmentService.getDepartmentCount(condition);
        Map<String, Integer> resultMap = new HashMap<>();
        resultMap.put("dataCount", dataCount);
        return MsResponse.success(resultMap);
    }


    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addDepartment")
    public MsResponse<DepartmentDTO> addDepartment(@Valid @RequestBody AddDepartmentRequest request) {
        //添加部门
        DepartmentInfoDAO departmentInfoDAO = departmentService.addDepartment(request.getDepartmentName(), request.getParentId(), request.getExtraContent(), request.getSort());
        //添加公司部门关系
        if (!StringUtil.isEmpty(request.getCompanyId())) {
            companyDepartmentService.addCompanyDepartmentRelation(request.getCompanyId(), departmentInfoDAO.getDepartmentId());
        }
        return MsResponse.success(DepartmentDTO.create(departmentInfoDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateDepartment")
    public MsResponse<DepartmentDTO> updateDepartment(@Valid @RequestBody UpdateDepartmentRequest request) {
        DepartmentInfoDAO departmentInfoDAO = departmentService.getDepartment(request.getDepartmentId());
        if (departmentInfoDAO == null) {
            return MsResponse.fail("50002", "部门不存在");
        }
        DepartmentInfoDAO updateData = DepartmentInfoDAO.builder()
                .departmentId(request.getDepartmentId())
                .build();
        if (request.getDepartmentName() != null) {
            updateData.setDepartmentName(request.getDepartmentName());
        }
        if (request.getParentId() != null) {
            updateData.setParentId(request.getParentId());
        }
        if (request.getExtraContent() != null) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        if (request.getSort() != null) {
            updateData.setSort(request.getSort());
        }
        departmentService.updateDepartment(updateData);
        BeanUtils.copyProperties(updateData, departmentInfoDAO);
        return MsResponse.success(DepartmentDTO.create(departmentInfoDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteDepartment")
    public MsResponse<DepartmentDTO> deleteDepartment(@Valid @RequestBody DeleteDepartmentRequest request) {
        return MsResponse.success(departmentService.deleteDepartment(request.getDepartmentId(), request.getIsForce()));
    }

    @RequestMapping("/getDepartmentUserList")
    public MsResponse<DataListDTO<IdentityDTO>> getDepartmentUserList(@RequestBody GetDepartmentUserListRequest request) {
        if (StringUtil.isEmpty(request.getDepartmentId())
                && (request.getDepartmentIdList() == null || request.getDepartmentIdList().isEmpty())) {
            return MsResponse.fail("50001", "部门id错误 ");
        }
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        Set<String> departmentIdSet = new HashSet<>();
        if (request.getDepartmentIdList() != null) {
            departmentIdSet.addAll(request.getDepartmentIdList());
        }
        if (!StringUtil.isEmpty(request.getDepartmentId())) {
            departmentIdSet.add(request.getDepartmentId());
        }
        if (request.getIsGetChildren() > 0) {
            GetCompanyDepartmentCondition condition = new GetCompanyDepartmentCondition();
            Iterator<String> iterator = departmentIdSet.iterator();
            condition.setDepartmentId(iterator.next());
            List<CDRWithDepartmentDAO> cdrWithDepartmentList = companyDepartmentServic.getCDRWithDepartmentList(condition, 0, 0);
            CDRWithDepartmentDAO cdrWithDepartmentDAO = cdrWithDepartmentList.get(0);
            String companyId = cdrWithDepartmentDAO.getCompanyId();

            List<DepartmentDTO> departmentList = departmentService.getDepartmentList(GetDepartmentCondition.builder()
                    .companyIdList(Collections.singletonList(companyId))
                    .build());

            List<DeptTreeDTO> deptTreeDTOList = departmentList.stream().map(DeptTreeDTO::create).collect(Collectors.toList());

            Map<String, DeptTreeDTO> deptTreeDTOMap = new HashMap<>();
            for (DeptTreeDTO deptTreeDTO : deptTreeDTOList) {
                String departmentId = deptTreeDTO.getDepartmentId();
                deptTreeDTOMap.put(departmentId, deptTreeDTO);
            }

            List<DeptTreeDTO> treeDTOList = new ArrayList<>();

            for (DeptTreeDTO deptTreeDTO : deptTreeDTOList) {
                String parentId = deptTreeDTO.getParentId();
                if (StrUtil.isEmpty(parentId)) {
                    treeDTOList.add(deptTreeDTO);
                } else {
                    DeptTreeDTO parentDeptTreeDTO = deptTreeDTOMap.get(parentId);
                    parentDeptTreeDTO.addDeptTreeDTO(deptTreeDTO);
                }
            }

            List<String> deptIdList = getDeptIdList(treeDTOList, departmentIdSet);

            departmentIdSet.addAll(deptIdList);

        }

        //是否获取子部门的用户
//        if (!StringUtil.isEmpty(request.getDepartmentId()) && request.getIsGetChildren() > 0) {
//            departmentIdSet.addAll(departmentService.getDepartmentList(GetDepartmentCondition.builder().parentId(request.getDepartmentId()).build())
//                    .stream()
//                    .map(DepartmentDTO::getDepartmentId)
//                    .collect(Collectors.toList()));
//        }
        departmentIdSet = departmentIdSet.stream().filter(departmentId -> !StringUtil.isEmpty(departmentId)).collect(Collectors.toSet());
        int dataCount = identityDepartmentService.getIDRCount(GetIdentityDepartmentCondition.builder().departmentIdList(new ArrayList<>(departmentIdSet)).extraContentCondition(request.getCondition()).build());
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<IdentityDTO>builder().build());
        }
        List<IdentityDTO> identityDTOList = new ArrayList<>();
        if (request.getIsSimple() > 0) {
            List<IDRWithIdentityDAO> idrWithIdentityDAOList = identityDepartmentService.getIDRWithIdentityList(
                    GetIdentityDepartmentCondition.builder().departmentIdList(new ArrayList<>(departmentIdSet)).extraContentCondition(request.getCondition()).build()
            );
            identityDTOList = idrWithIdentityDAOList.stream().map(IdentityDTO::create).collect(Collectors.toList());
        } else {
            identityDTOList = identityDepartmentService.getIDRList(GetIdentityDepartmentCondition.builder().departmentIdList(new ArrayList<>(departmentIdSet)).extraContentCondition(request.getCondition()).build(), (page - 1) * limit, limit)
                    .stream()
                    .map(idr -> IdentityDTO.builder()
                            .identityId(idr.getIdentityId())
                            .build())
                    .collect(Collectors.toList());
        }
        return MsResponse.success(
                DataListDTO.<IdentityDTO>builder()
                        .dataList(identityDTOList)
                        .dataCount(dataCount)
                        .build()
        );
    }

    /**
     * 递归获取部门下
     *
     * @param treeDTOList
     * @return
     */
    private List<String> getDeptIdList(List<DeptTreeDTO> treeDTOList, Set<String> deptIds) {
        lock.lock();
        try {
            deptIdList = new ArrayList<>();
            recursion(treeDTOList, deptIds);
            List<String> returnDeptIdList = new ArrayList<>(deptIdList);
            deptIdList = null;
            return returnDeptIdList.stream().distinct().collect(Collectors.toList());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 递归
     *
     * @param treeDTOList
     */
    private void recursion(List<DeptTreeDTO> treeDTOList, Set<String> deptIdList) {
        if (Objects.isNull(treeDTOList)) {
            return;
        }
        for (DeptTreeDTO deptTreeDTO : treeDTOList) {
            String departmentId = deptTreeDTO.getDepartmentId();
            if (deptIdList.contains(departmentId)) {
                addList(deptTreeDTO);
            } else {
                recursion(deptTreeDTO.getChildren(), deptIdList);
            }
        }
    }

    /**
     * 将值都保存到集合中
     */
    private void addList(DeptTreeDTO deptTreeDTO) {
        if (Objects.isNull(deptTreeDTO)) {
            return;
        }
        List<DeptTreeDTO> children = deptTreeDTO.getChildren();
        if (Objects.isNull(children)) {
            return;
        }
        for (DeptTreeDTO child : children) {
            deptIdList.add(child.getDepartmentId());
            addList(child);
        }
    }


}
