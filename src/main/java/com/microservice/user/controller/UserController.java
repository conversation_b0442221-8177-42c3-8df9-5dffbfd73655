package com.microservice.user.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.TokenConstant;
import com.microservice.user.constant.UserConstant;
import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.*;
import com.microservice.user.entity.request.ms.group.GetGroupUserListRequest;
import com.microservice.user.entity.request.ms.user.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.*;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;
    @Autowired
    private GroupService groupService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private IdentityGroupService identityGroupService;
    @Autowired
    private IdentityPositionService identityPositionService;
    @Autowired
    private IdentityDepartmentService identityDepartmentService;
    @Autowired
    private IdentityCompanyService identityCompanyService;

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addUser")
    public MsResponse<UserDTO> addUser(@Valid @RequestBody AddUserRequest request) {
        String appChannelId = Common.getAppChannelId(request);
        String dataChannelId = Common.getDataChannelId(request);
        //判断用户是否已存在
        UserInfoDAO userInfoDAO = userService.getUser(GetUserCondition.builder().userName(request.getUserName()).dataChannelId(dataChannelId).build());
        if (userInfoDAO != null) {
            return MsResponse.fail("50004", "用户已存在");
        }
        //初始化用户
        UserDTO userDTO = userService.initUser(request.getUserName(), request.getPassword(), request.getExtraContent(), appChannelId, dataChannelId);
        //关联用户组
        if (request.getGroupIdList() != null && !request.getGroupIdList().isEmpty()) {
            identityGroupService.addIdentityGroupRelation(userDTO.getUserId(), request.getGroupIdList(), appChannelId);
        }
        return MsResponse.success(userService.createUserEntity(appChannelId, userDTO.getUserId()));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateUser")
    public MsResponse<UserDTO> updateUser(@Valid @RequestBody UpdateUserRequest request) {
        String dataChannelId = Common.getDataChannelId(request);
        //判断用户是否存在
        UserInfoDAO userInfoDAO = userService.getUserByIdentityId(request.getIdentityId());
        if (userInfoDAO == null) {
            return MsResponse.fail("50002", "用户不存在");
        }
        //如果修改了用户名 || 如果修改了用户的渠道号
        if ((request.getUserName() != null && !request.getUserName().equals(userInfoDAO.getUserName())) || (request.getDataChannelId() != null && !request.getDataChannelId().equals(userInfoDAO.getDataChannelId()))) {
            //检查目标dataChannelId中是否已有此用户
            UserInfoDAO checkUserInfo = userService.getUser(GetUserCondition.builder().userName(request.getUserName()).dataChannelId(dataChannelId).build());
            if (checkUserInfo != null) {
                return MsResponse.fail("50003", "目标渠道已存在此用户");
            }
        }
        if (request.getGroupIdList() != null && !request.getGroupIdList().isEmpty()) {
            //判断用户组是否在渠道内
            List<GroupDTO> groupDTOList = groupService.getGroupList(GetGroupCondition.builder().groupIdList(request.getGroupIdList()).build());
            for (GroupDTO groupDTO : groupDTOList) {
                if (!groupDTO.getDataChannelId().equals(dataChannelId)) {
                    return MsResponse.fail("50004", "用户组不在渠道内");
                }
            }
        }
        //修改逻辑开始>>>
        UserInfoDAO updateData = UserInfoDAO.builder().userId(userInfoDAO.getUserId()).build();
        if (request.getPassword() != null) {
            if (StringUtil.isEmpty(request.getPassword())) {
                return MsResponse.fail("50005", "密码不能为空");
            }
            String password = request.getPassword();
            if (password.length() != 32) {
                password = StringUtil.md5(password);
            }
            updateData.setPassword(StringUtil.myMd5(password));
        }
        if (request.getUserName() != null) {
            updateData.setUserName(request.getUserName());
        }
        if (request.getExtraContent() != null) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        if (!StringUtil.isEmpty(request.getAppChannelId())) {
            updateData.setAppChannelId(request.getAppChannelId());
        }
        if (!StringUtil.isEmpty(request.getDataChannelId())) {
            updateData.setDataChannelId(request.getDataChannelId());
        }
        userService.updateUser(updateData);
        //更新用户组
        if (request.getGroupIdList() != null) {
            boolean isLogout = identityGroupService.updateIdentityGroupRelation(request.getIdentityId(), request.getGroupIdList(), Common.getAppChannelId(request));
            if (isLogout) {
                //退出登录
                tokenService.logout(GetTokenCondition.builder().userId(userInfoDAO.getUserId()).build(), TokenConstant.tokenStatus.CHANGE_EVENT.getCode().toString());
            }
        }
        //生成用户信息
        UserDTO userDTO = userService.createUserEntity(Common.getAppChannelId(request), userInfoDAO.getUserId());
        //更新用户缓存
        tokenService.updateTokenData(Common.getAppChannelId(request), userDTO.getUserId());
        //清除用户缓存
        userService.clearUserCache(userInfoDAO.getUserId());
        //清除身份缓存
        identityService.clearIdentityCache(userDTO.getIdentityId());
        return MsResponse.success(userDTO);
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteUser")
    public MsResponse<UserDTO> deleteUser(@Valid @RequestBody DeleteUserRequest request) {
        String dataChannelId = Common.getDataChannelId(request);
        UserInfoDAO userInfoDAO = userService.getUserByIdentityId(request.getIdentityId());
        if (userInfoDAO == null) {
            return MsResponse.fail("50002", "用户不存在");
        }
        if (!userInfoDAO.getDataChannelId().equals(dataChannelId)) {
            return MsResponse.fail("50003", "用户渠道错误");
        }
        UserDTO userDTO = userService.createUserEntity(Common.getAppChannelId(request), userInfoDAO.getUserId());
        List<String> identityIdList = Optional.ofNullable(userDTO.getIdentityList()).map(identityList -> identityList.stream().map(IdentityDTO::getIdentityId).collect(Collectors.toList())).orElse(new ArrayList<>());
        //删除身份关联信息
        if (request.getIsForce() > 0) {
            identityService.deleteIdentity(identityIdList);
        } else {
            identityService.deleteIdentity(request.getIdentityId());
        }
        //删除用户
        userService.deleteUser(userInfoDAO.getUserId());
        //清除用户缓存
        userService.clearUserCache(userInfoDAO.getUserId());
        //清除身份缓存
        if (request.getIsForce() > 0) {
            identityService.clearIdentityCache(identityIdList);
        } else {
            identityService.clearIdentityCache(userDTO.getIdentityId());
        }
        return MsResponse.success(userDTO);
    }

    @RequestMapping("/getUserList")
    public MsResponse<DataListDTO<UserDTO>> getUserList(@RequestBody GetUserListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        String dataChannelId = Common.getDataChannelId(request);
        Map<String, Object> conditionMap = new HashMap<>() {{
            put("dataChannelId", dataChannelId);
        }};
        //isShowAll大于0时忽略渠道号
        if (request.getIsShowAll() > 0) {
            conditionMap.remove("dataChannelId");
        }
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), UserConstant.USER_MAIN_COLUMN);
        //构造查询条件
        GetUserCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetUserCondition.class);
        if (!StringUtil.isEmpty(condition.getIdentityId())) {
            if (condition.getIdentityIdList() == null) {
                condition.setIdentityIdList(new LinkedList<>());
            }
            condition.getIdentityIdList().add(condition.getIdentityId());
        }
        if (!StringUtil.isEmpty(condition.getCompanyId())) {
            List<ICRWithIdentityDAO> icrWithIdentityList = identityCompanyService.getICRWithIdentityList(GetIdentityCompanyCondition.builder().companyId(condition.getCompanyId()).build(), 0, 0);
            List<String> companyUserIdList = CollectionUtils.isEmpty(icrWithIdentityList) ? List.of("not_exists") : icrWithIdentityList.stream().map(ICRWithIdentityDAO::getUserId).distinct().collect(Collectors.toList());
            condition.setCompanyUserIdList(companyUserIdList);
        }
        //开始查询
        List<UserInfoDAO> userInfoDAOList = userService.getUserList(condition, (page - 1) * limit, limit);
        List<UserDTO> userDTOList;
        if (request.getIsSimple() > 0) {
            userDTOList = userInfoDAOList.stream().map(UserDTO::create).collect(Collectors.toList());
            //简版的身份信息
            userService.createSimpleUserEntity(userDTOList);
        } else {
            userDTOList = userService.getUserEntityFromCache(Common.getAppChannelId(request), userInfoDAOList.stream().map(UserInfoDAO::getUserId).collect(Collectors.toList()));
        }
        if (request.getUserKeyFilter() != null) {
            userDTOList = userService.doUserKeyFilter(userDTOList, request.getUserKeyFilter());
        }
        return MsResponse.success(DataListDTO.<UserDTO>builder().dataCount(userService.getUserCount(condition)).dataList(userDTOList).build());
    }

    @RequestMapping("/getEventUserList")
    public MsResponse<List<UserDTO>> getEventUserList(@Valid @RequestBody GetEventUserListRequest request) {
        if (StringUtil.isEmpty(request.getEventId())) {
            return MsResponse.fail("50001", "事件id错误");
        }
        List<IGERWithIdentityDAO> igerWithIdentityDAOList = userService.getEventUserList(request.getEventId(), request.getCompanyId());
        List<UserDTO> userDTOList = new ArrayList<>();
        if (request.getIsSimple() > 0) {
            Map<String, IGERWithIdentityDAO> map = igerWithIdentityDAOList.stream().collect(Collectors.toMap(IGERWithIdentityDAO::getUserId, igerWithIdentityDAO -> igerWithIdentityDAO, (igerWithIdentityDAO1, igerWithIdentityDAO2) -> igerWithIdentityDAO1));
            userDTOList = userService.createUserSimpleEntity(igerWithIdentityDAOList.stream().map(IGERWithIdentityDAO::getUserId).collect(Collectors.toList()));
            userDTOList = userDTOList.stream().peek(userDTO -> {
                IGERWithIdentityDAO igerWithIdentityDAO = map.get(userDTO.getUserId());
                if (igerWithIdentityDAO != null) {
                    userDTO.setIdentityId(igerWithIdentityDAO.getIdentityId());
                }
            }).collect(Collectors.toList());
        } else {
            userDTOList = userService.getUserEntityFromCache(Common.getAppChannelId(request), igerWithIdentityDAOList.stream().map(IGERWithIdentityDAO::getUserId).collect(Collectors.toList()));
        }
        return MsResponse.success(userDTOList);
    }

    @RequestMapping("/getCompanyUserList")
    public MsResponse<DataListDTO<UserDTO>> getCompanyUserList(@RequestBody GetCompanyUserListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        List<String> userIdList = new ArrayList<>();
        int dataCount = 0;
        if (!StringUtil.isEmpty(request.getPositionId())) {
            userIdList = identityPositionService.getIPRWithIdentityList(GetIdentityPositionCondition.builder().positionId(request.getPositionId()).build(), (page - 1) * limit, limit).stream().map(IPRWithIdentityDAO::getUserId).collect(Collectors.toList());
            dataCount = identityPositionService.getIPRCount(GetIdentityPositionCondition.builder().positionId(request.getPositionId()).build());
        } else if (!StringUtil.isEmpty(request.getDepartmentId()) || CollectionUtil.isNotEmpty(request.getDepartmentIdList())) {
            userIdList = identityDepartmentService.getIDRWithIdentityList(GetIdentityDepartmentCondition.builder().departmentId(request.getDepartmentId()).departmentIdList(request.getDepartmentIdList()).build(), (page - 1) * limit, limit).stream().map(IDRWithIdentityDAO::getUserId).distinct().collect(Collectors.toList());
            dataCount = identityDepartmentService.getIDRDistinctCount(GetIdentityDepartmentCondition.builder().departmentId(request.getDepartmentId()).departmentIdList(request.getDepartmentIdList()).build());
        } else if (!StringUtil.isEmpty(request.getCompanyId()) || CollectionUtil.isNotEmpty(request.getCompanyIdList())) {
            userIdList = identityCompanyService.getICRWithIdentityList(GetIdentityCompanyCondition.builder().companyId(request.getCompanyId()).companyIdList(request.getCompanyIdList()).build(), (page - 1) * limit, limit).stream().map(ICRWithIdentityDAO::getUserId).distinct().collect(Collectors.toList());
            dataCount = identityCompanyService.getICRCountByCompanyId(GetIdentityCompanyCondition.builder().companyId(request.getCompanyId()).companyIdList(request.getCompanyIdList()).build());
        }
        if (request.getIsSimple() > 0) {
            return MsResponse.success(DataListDTO.<UserDTO>builder().dataCount(dataCount).dataList(userService.createUserSimpleEntity(userIdList, request.getCompanyId())).build());
        }
        List<UserDTO> userDTOList = userService.getUserEntityFromCache(Common.getAppChannelId(request), userIdList);
        if (request.getUserKeyFilter() != null) {
            userDTOList = userService.doUserKeyFilter(userDTOList, request.getUserKeyFilter());
        }
        return MsResponse.success(DataListDTO.<UserDTO>builder().dataList(userDTOList).dataCount(dataCount).build());
    }

    @RequestMapping("/getCompanyUserCount")
    public MsResponse<Map<String,Object>> getCompanyUserCount(@RequestBody GetCompanyUserListRequest request) {

        int dataCount = 0;
        if (!StringUtil.isEmpty(request.getPositionId())) {
            dataCount = identityPositionService.getIPRCount(GetIdentityPositionCondition.builder().positionId(request.getPositionId()).build());
        } else if (!StringUtil.isEmpty(request.getDepartmentId())) {
            dataCount = identityDepartmentService.getIDRCount(GetIdentityDepartmentCondition.builder().departmentId(request.getDepartmentId()).build());
        } else if (!StringUtil.isEmpty(request.getCompanyId())) {
            dataCount = identityCompanyService.getICRCountByCompanyId(request.getCompanyId());
        }
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("dataCount",dataCount);
        return MsResponse.success(resultMap);
    }

    @RequestMapping("/getGroupUserList")
    public MsResponse<DataListDTO<UserDTO>> getGroupUserList(@Valid @RequestBody GetGroupUserListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        int dataCount = identityGroupService.getIGRCount(GetIdentityGroupCondition.builder().groupId(request.getGroupId()).identityIdList(request.getIdentityIdList()).build());
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<UserDTO>builder().build());
        }
        List<IGRWithIdentityDAO> igrList = identityGroupService.getIGRWithIdentityList(GetIdentityGroupCondition.builder().groupId(request.getGroupId()).identityIdList(request.getIdentityIdList()).build(), (page - 1) * limit, limit);
        List<UserDTO> userDTOList = new ArrayList<>();
        if (request.getIsSimple() > 0) {
            userDTOList = userService.createUserSimpleEntity(igrList.stream().map(IGRWithIdentityDAO::getUserId).collect(Collectors.toList()));
        } else {
            userDTOList = userService.getUserEntityFromCache(Common.getAppChannelId(request), igrList.stream().map(IGRWithIdentityDAO::getUserId).collect(Collectors.toList()));
        }
        return MsResponse.success(DataListDTO.<UserDTO>builder().dataCount(dataCount).dataList(userDTOList).build());
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addUserPre")
    public MsResponse<UserInfoPreDTO> addUserPre(@Valid @RequestBody AddUserPreRequest request) {
        UserInfoPreDAO userInfoPreDAO = userService.addUserPre(request.getUserName(), request.getExtraContent(), Common.getAppChannelId(request), Common.getDataChannelId(request));
        return MsResponse.success(new UserInfoPreDTO(userInfoPreDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateUserPre")
    public MsResponse<UserInfoPreDTO> updateUserPre(@Valid @RequestBody UpdateUserPreRequest request) {
        UserInfoPreDAO userInfoPreDAO = userService.updateUserPre(request.getPreUserId(), request.getUserName(), request.getExtraContent());
        return MsResponse.success(new UserInfoPreDTO(userInfoPreDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteUserPre")
    public MsResponse deleteUserPre(@Valid @RequestBody DeleteUserPreRequest request) {
        userService.deleteUserPre(request.getPreUserId());
        return MsResponse.success();
    }

    @RequestMapping("/getUserPreList")
    public MsResponse<DataListDTO<UserInfoPreDTO>> getUserPreList(@RequestBody GetUserInfoPreListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        String dataChannelId = Common.getDataChannelId(request);
        Map<String, Object> conditionMap = new HashMap<>() {{
            put("dataChannelId", dataChannelId);
        }};
        //isShowAll大于0时忽略渠道号
        if (request.getIsShowAll() > 0) {
            conditionMap.remove("dataChannelId");
        }
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), UserConstant.USER_PRE_MAIN_COLUMN);
        //构造查询条件
        GetUserPreCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetUserPreCondition.class);

        return MsResponse.success(DataListDTO.<UserInfoPreDTO>builder()
                .dataCount(userService.getUserPreCount(condition))
                .dataList(userService.getUserPreList(condition, (page - 1) * limit, limit)
                        .stream().map(UserInfoPreDTO::new)
                        .collect(Collectors.toList())).build());
    }


    @RequestMapping("/getUserPreListCount")
    public MsResponse getUserPreListTwo(@RequestBody GetUserInfoPreListRequest request) {
        String dataChannelId = Common.getDataChannelId(request);
        Map<String, Object> conditionMap = new HashMap<>() {{
            put("dataChannelId", dataChannelId);
        }};
        //isShowAll大于0时忽略渠道号
        if (request.getIsShowAll() > 0) {
            conditionMap.remove("dataChannelId");
        }
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), UserConstant.USER_PRE_MAIN_COLUMN);
        //构造查询条件
        GetUserPreCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetUserPreCondition.class);
        Map<String,Object> resultCount = new HashMap<>();
        resultCount.put("dataCount",userService.getUserPreCount(condition));
        return MsResponse.success(resultCount);
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateUserPreList")
    public MsResponse<Map<String, Object>> updateUserPreList(@RequestBody UpdateUserPreListRequest updateUserPreListRequest) {
        if (updateUserPreListRequest.getPreUserList() == null || updateUserPreListRequest.getPreUserList().isEmpty()) {
            return MsResponse.fail("50001", "请求参数错误");
        }
        List<UserInfoPreDTO> userInfoPreDTOList = new ArrayList<>();
        for (UpdateUserPreRequest request : updateUserPreListRequest.getPreUserList()) {
            UserInfoPreDAO userInfoPreDAO = userService.updateUserPre(request.getPreUserId(), request.getUserName(), request.getExtraContent());
            userInfoPreDTOList.add(new UserInfoPreDTO(userInfoPreDAO));
        }
        return MsResponse.success(new HashMap<>() {{
            put("preUserIds", userInfoPreDTOList.stream().map(UserInfoPreDTO::getPreUserId).collect(Collectors.joining(",")));
            put("preUserList", userInfoPreDTOList);
        }});
    }

    @PostMapping("/getSimpleCompanyUserList")
    public MsResponse getSimpleCompanyUserList(@RequestBody @Valid GetSimpleCompanyUserListRequest request) {
        if (Objects.isNull(request.getPage()) || request.getPage() < 1) {
            request.setPage(1);
        }
        if (Objects.isNull(request.getLimit()) || request.getLimit() < 0) {
            request.setLimit(0);
        }
        return userService.getSimpleCompanyUserList(request);
    }
}
