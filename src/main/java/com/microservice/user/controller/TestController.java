package com.microservice.user.controller;

import com.microservice.user.constant.SystemConstant;
import com.microservice.user.constant.TokenConstant;
import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.IdentityDTO;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import com.microservice.user.entity.request.ms.test.ClearCacheRequest;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.*;
import com.microservice.user.util.RedisUtil;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private TokenService tokenService;
    @Autowired
    private UserService userService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private IdentityCompanyService identityCompanyService;
    @Autowired
    private IdentityDepartmentService identityDepartmentService;
    @Autowired
    private IdentityPositionService identityPositionService;
    @Autowired
    private IdentityGroupService identityGroupService;
    @Autowired
    private IdentityEntityService identityEntityService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private CompanyDepartmentService companyDepartmentService;
    @Autowired
    private CompanyPositionService companyPositionService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private PositionService positionService;
    @Autowired
    private DepartmentPositionService departmentPositionService;
    @Autowired
    private GroupService groupService;
    @Autowired
    private GroupEventService groupEventService;
    @Autowired
    private LogService logService;

    @PostMapping("/hello")
    public String hello() {
        return "MicroService-Invoice: hello world";
    }

    @PostMapping("/clearLoginStatus")
    public MsResponse<List<String>> clearLoginStatus(@RequestBody MsBaseRequest request) {
        GetTokenCondition condition = GetTokenCondition.builder()
                .tokenStatus("")
                .appChannelId(request.getAppChannelId())
                .dataChannelId(request.getDataChannelId())
                .build();
        List<TokenInfoDAO> tokenInfoDAOList = tokenService.getTokenList(condition);
        if (tokenInfoDAOList.isEmpty()) {
            return MsResponse.success(new ArrayList<>());
        }
        tokenService.updateToken(String.valueOf(TokenConstant.tokenStatus.LOGOUT.getCode()), condition);
        List<String> keyList = tokenInfoDAOList.stream().map(dao -> SystemConstant.REDIS_KEY_USER_TOKEN + dao.getToken()).collect(Collectors.toList());
        RedisUtil.del(keyList);
        return MsResponse.success(keyList);
    }

    @PostMapping("/clearCache")
    public MsResponse<Set<String>> clearCache(@Valid @RequestBody ClearCacheRequest request) {
        Set<String> keySet = RedisUtil.keys(request.getKeyPrefix() + "*");
        if (!keySet.isEmpty()) {
            RedisUtil.del(new ArrayList<>(keySet));
        }
        return MsResponse.success(keySet);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/clearData")
    public MsResponse<Map<String, Integer>> clearData(@Valid @RequestBody MsBaseRequest request) {
        String appChannelId = request.getAppChannelId();
        String dataChannelId = request.getDataChannelId();
        if (StringUtil.isEmpty(appChannelId) && StringUtil.isEmpty(dataChannelId)) {
            return MsResponse.fail("60000", "渠道号错误");
        }
        //清理数据结果
        Map<String, Integer> clearResult = new HashMap<>() {{
            put("user_info", 0);
            put("user_info_pre", 0);
            put("identity_company_relation", 0);
            put("identity_department_relation", 0);
            put("identity_position_relation", 0);
            put("identity_group_relation", 0);
            put("identity_entity_relation", 0);
            put("identity_info", 0);
            put("token_info", 0);
            put("department_info", 0);
            put("company_department_relation", 0);
            put("company_position_relation", 0);
            put("department_position_relation", 0);
            put("position_info", 0);
            put("company_info", 0);
            put("group_info", 0);
            put("group_event_relation", 0);
            put("log_info", 0);
        }};
        //删除渠道下所有用户
        int deleteUserCount = userService.deleteUser(GetUserCondition.builder()
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build());
        clearResult.put("user_info", deleteUserCount);
        //删除所有预注册用户
        int deleteUserPreCount = userService.deleteUserPre(GetUserPreCondition.builder()
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build());
        clearResult.put("user_info_pre", deleteUserPreCount);
        //获取所有身份
        List<IdentityDTO> identityDTOList = identityService.getIdentityList(GetIdentityCondition.builder()
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build());
        if (!identityDTOList.isEmpty()) {
            List<String> identityIdList = identityDTOList.stream().map(IdentityDTO::getIdentityId).collect(Collectors.toList());
            //删除身份和公司关系
            int deleteICRCount = identityCompanyService.deleteICR(GetIdentityCompanyCondition.builder().identityIdList(identityIdList).build());
            clearResult.put("identity_company_relation", deleteICRCount);
            //删除身份和部门关系
            int deleteIDRCount = identityDepartmentService.deleteIDR(GetIdentityDepartmentCondition.builder().identityIdList(identityIdList).build());
            clearResult.put("identity_department_relation", deleteIDRCount);
            //删除身份和职位关系
            int deleteIPRCount = identityPositionService.deleteIPR(GetIdentityPositionCondition.builder().identityIdList(identityIdList).build());
            clearResult.put("identity_position_relation", deleteIPRCount);
            //删除身份和用户组关系
            int deleteIGRCount = identityGroupService.deleteIGR(GetIdentityGroupCondition.builder().identityIdList(identityIdList).build());
            clearResult.put("identity_group_relation", deleteIGRCount);
            //删除用户和标签关系
            int deleteIERCount = identityEntityService.deleteIER(GetIdentityEntityCondition.builder().identityIdList(identityIdList).build());
            clearResult.put("identity_entity_relation", deleteIERCount);
            //删除身份
            int deleteIdentityCount = identityService.deleteIdentity(GetIdentityCondition.builder().identityIdList(identityIdList).build());
            clearResult.put("identity_info", deleteIdentityCount);
        }
        //获取所有公司
        List<CompanyInfoDAO> companyInfoDAOList = companyService.getCompanyList(GetCompanyCondition.builder()
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build());
        if (!companyInfoDAOList.isEmpty()) {
            List<String> companyIdList = companyInfoDAOList.stream().map(CompanyInfoDAO::getCompanyId).collect(Collectors.toList());
            //获取公司和部门的关系
            List<CompanyDepartmentRelationDAO> companyDepartmentRelationDAOList = companyDepartmentService.getCDRList(GetCompanyDepartmentCondition.builder()
                    .companyIdList(companyIdList)
                    .build());
            if (!companyDepartmentRelationDAOList.isEmpty()) {
                List<String> departmentIdList = companyDepartmentRelationDAOList.stream().map(CompanyDepartmentRelationDAO::getDepartmentId).collect(Collectors.toList());
                //删除部门
                int deleteDepartmentCount = departmentService.deleteDepartment(GetDepartmentCondition.builder()
                        .departmentIdList(departmentIdList)
                        .build());
                clearResult.put("department_info", deleteDepartmentCount);
                //删除公司和部门的关系
                int deleteCDRCount = companyDepartmentService.deleteCDR(GetCompanyDepartmentCondition.builder()
                        .companyIdList(companyIdList)
                        .build());
                clearResult.put("company_department_relation", deleteCDRCount);
                //获取部门和职位关系
                List<DepartmentPositionRelationDAO> departmentPositionRelationDAOList = departmentPositionService.getDPRList(GetDepartmentPositionCondition.builder()
                        .departmentIdList(departmentIdList)
                        .build());
                if (!departmentPositionRelationDAOList.isEmpty()) {
                    //删除职位
                    int deletePositionCount = positionService.deletePosition(GetPositionCondition.builder()
                            .positionIdList(departmentPositionRelationDAOList.stream().map(DepartmentPositionRelationDAO::getPositionId).collect(Collectors.toList()))
                            .build());
                    clearResult.put("position_info", deletePositionCount);
                    //删除部门和职位关系
                    int deleteDPRCount = departmentPositionService.deleteDPR(GetDepartmentPositionCondition.builder()
                            .departmentIdList(departmentIdList)
                            .build());
                    clearResult.put("department_position_relation", deleteDPRCount);
                }
            }
            //获取公司和职位的关系
            List<CompanyPositionRelationDAO> companyPositionRelationDAOList = companyPositionService.getCPRList(GetCompanyPositionCondition.builder()
                    .companyIdList(companyIdList)
                    .build());
            if (!companyPositionRelationDAOList.isEmpty()) {
                //删除职位
                int deletePositionCount = positionService.deletePosition(GetPositionCondition.builder()
                        .positionIdList(companyPositionRelationDAOList.stream().map(CompanyPositionRelationDAO::getPositionId).collect(Collectors.toList()))
                        .build());
                clearResult.put("position_info", clearResult.getOrDefault("position_info", 0) + deletePositionCount);
                //删除公司和职位的关系
                int deleteCPRCount = companyPositionService.deleteCPR(GetCompanyPositionCondition.builder()
                        .companyIdList(companyIdList)
                        .build());
                clearResult.put("company_position_relation", deleteCPRCount);
            }
            //删除公司
            int deleteCompanyCount = companyService.deleteCompany(GetCompanyCondition.builder()
                    .appChannelId(appChannelId)
                    .dataChannelId(dataChannelId)
                    .build());
            clearResult.put("company_info", deleteCompanyCount);
        }
        //获取所有用户组
        List<GroupInfoDAO> groupInfoDAOList = groupService.getGroupList(GetGroupCondition.builder()
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build(), 0, 0);
        if (!groupInfoDAOList.isEmpty()) {
            List<String> groupIdList = groupInfoDAOList.stream().map(GroupInfoDAO::getGroupId).collect(Collectors.toList());
            //删除所有用户组
            int deleteGroupCount = groupService.deleteGroup(GetGroupCondition.builder()
                    .groupIdList(groupIdList)
                    .build());
            clearResult.put("group_info", deleteGroupCount);
            //删除用户组和事件关系
            int deleteGERCount = groupEventService.deleteGER(GetGroupEventCondition.builder()
                    .groupIdList(groupIdList)
                    .build());
            clearResult.put("group_event_relation", deleteGERCount);
        }
        //删除token
        int deleteTokenCount = tokenService.deleteToken(GetTokenCondition.builder()
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build());
        clearResult.put("token_info", deleteTokenCount);
        //获取所有操作日志
        int deleteLogCount = logService.deleteLog(GetLogCondition.builder()
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build());
        clearResult.put("log_info", deleteLogCount);
        return MsResponse.success(clearResult);
    }
}
