package com.microservice.user.controller;

import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.CompanyConstant;
import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.*;
import com.microservice.user.entity.request.ms.company.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.*;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/company")
public class CompanyController {

    @Autowired
    private CompanyService companyService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private IdentityCompanyService identityCompanyService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private PositionService positionService;
    @Autowired
    private IdentityDepartmentService identityDepartmentService;
    @Autowired
    private IdentityPositionService identityPositionService;
    @Autowired
    private DepartmentPositionService departmentPositionService;

    @RequestMapping("/getCompanyList")
    public MsResponse<DataListDTO<CompanyDTO>> getCompanyList(@RequestBody GetCompanyListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        String dataChannelId = Common.getDataChannelId(request);
        Map<String, Object> conditionMap = new HashMap<>() {{
            put("dataChannelId", dataChannelId);
        }};
        //isShowAll大于0时忽略渠道号
        if (request.getIsShowAll() > 0) {
            conditionMap.remove("dataChannelId");
        }
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), CompanyConstant.COMPANY_MAIN_COLUMN);
        //构造查询条件
        GetCompanyCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetCompanyCondition.class);
        condition.setApplySql(Common.buildQuerySql(request.getQueryCondition()));
        //开始查询
        int dataCount = companyService.getCompanyCount(condition);
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<CompanyDTO>builder().build());
        }
        List<CompanyInfoDAO> companyInfoDAOList = companyService.getCompanyList(condition, request.getColumnList(), (page - 1) * limit, limit);
        List<CompanyDTO> companyDTOList;
        if (request.getIsSimple() > 0) {
            companyDTOList = companyInfoDAOList.stream().map(CompanyDTO::create).collect(Collectors.toList());
        } else {
            Map<String, Integer> company2ICRCountMap = identityCompanyService.getICRCountGroupByCompanyId(
                    GetIdentityCompanyCondition.builder()
                            .companyIdList(
                                    companyInfoDAOList.stream()
                                            .map(CompanyInfoDAO::getCompanyId)
                                            .collect(Collectors.toList())
                            )
                            .build()
            ).stream().collect(Collectors.toMap(IdentityCompanyCountDAO::getCompanyId, IdentityCompanyCountDAO::getDataCount, (dao1, dao2) -> dao2));
            companyDTOList = companyInfoDAOList.stream().map(companyInfoDAO -> {
                CompanyDTO companyDTO = CompanyDTO.create(companyInfoDAO);
                companyDTO.setUserCount(company2ICRCountMap.getOrDefault(companyDTO.getCompanyId(), 0));
                return companyDTO;
            }).collect(Collectors.toList());
        }
        if (request.getCompanyKeyFilter() != null) {
            companyDTOList = companyService.doCompanyKeyFilter(companyDTOList, request.getCompanyKeyFilter());
        }
        return MsResponse.success(
                DataListDTO.<CompanyDTO>builder()
                        .dataCount(dataCount)
                        .dataList(companyDTOList)
                        .build()
        );
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addCompany")
    public MsResponse<CompanyDTO> addCompany(@Valid @RequestBody AddCompanyRequest request) {
        String appChannelId = Common.getAppChannelId(request);
        String dataChannelId = Common.getDataChannelId(request);
        CompanyInfoDAO companyInfoDAO = companyService.addCompany(request.getCompanyName(), request.getExtraContent(), appChannelId, dataChannelId);
        return MsResponse.success(CompanyDTO.create(companyInfoDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateCompany")
    public MsResponse<CompanyDTO> updateCompany(@Valid @RequestBody UpdateCompanyRequest request) {
        CompanyInfoDAO companyInfoDAO = companyService.getCompany(request.getCompanyId());
        if (companyInfoDAO == null) {
            return MsResponse.fail("50002", "公司不存在");
        }
        CompanyInfoDAO updateData = CompanyInfoDAO.builder()
                .companyId(request.getCompanyId())
                .build();
        updateData.setDataChannelId(Common.getDataChannelId(request));
        if (request.getCompanyName() != null) {
            updateData.setCompanyName(request.getCompanyName());
        }
        if (request.getParentId() != null) {
            updateData.setParentId(request.getParentId());
        }
        if (request.getExtraContent() != null) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        if (!StringUtil.isEmpty(request.getAppChannelId())) {
            updateData.setAppChannelId(request.getAppChannelId());
        }
        if (!StringUtil.isEmpty(request.getDataChannelId())) {
            updateData.setDataChannelId(request.getDataChannelId());
        }
        companyService.updateCompany(updateData);
        BeanUtils.copyProperties(updateData, companyInfoDAO);
        //清除缓存
        companyService.clearCompanyCache(Common.getAppChannelId(request), companyInfoDAO.getCompanyId());
        return MsResponse.success(CompanyDTO.create(companyInfoDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteCompany")
    public MsResponse<CompanyDTO> deleteCompany(@Valid @RequestBody DeleteCompanyRequest request) {
        CompanyInfoDAO companyInfoDAO = companyService.getCompany(request.getCompanyId());
        if (companyInfoDAO == null) {
            return MsResponse.fail("50002", "公司不存在");
        }
        CompanyDTO companyDTO = CompanyDTO.create(companyInfoDAO);
        companyService.deleteCompany(request.getCompanyId(), request.getIsForce());
        //清除缓存
        companyService.clearCompanyCache(Common.getAppChannelId(request), companyInfoDAO.getCompanyId());
        return MsResponse.success(companyDTO);
    }

    @RequestMapping("/getCompanyUserList")
    public MsResponse<DataListDTO<IdentityDTO>> getCompanyUserList(@RequestBody GetCompanyUserListRequest request) {
        if (StringUtil.isEmpty(request.getCompanyId())) {
            return MsResponse.fail("50001", "公司id错误");
        }
        int dataCount = identityCompanyService.getICRCountByCompanyId(request.getCompanyId());
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<IdentityDTO>builder().build());
        }
        List<IdentityDTO> identityDTOList = new ArrayList<>();
        if (request.getIsSimple() > 0) {
            List<ICRWithIdentityDAO> icrWithIdentityList = identityCompanyService.getICRWithIdentityList(GetIdentityCompanyCondition.builder()
                    .companyId(request.getCompanyId())
                    .build());
            identityDTOList = icrWithIdentityList.stream().map(IdentityDTO::create).collect(Collectors.toList());
        } else {
            identityDTOList = identityService.createIdentityEntity(
                    Common.getAppChannelId(request),
                    identityCompanyService.getICRListByCompanyId(request.getCompanyId())
                            .stream()
                            .map(IdentityCompanyRelationDAO::getIdentityId)
                            .collect(Collectors.toList())
            );
        }
        return MsResponse.success(
                DataListDTO.<IdentityDTO>builder()
                        .dataCount(dataCount)
                        .dataList(identityDTOList)
                        .build()
        );
    }

    @RequestMapping("/getCompanyTree")
    public MsResponse<TreeItemDTO> getCompanyTree(@RequestBody GetCompanyTreeRequest request) {
        if (StringUtil.isEmpty(request.getCompanyId())) {
            return MsResponse.fail("50001", "参数错误");
        }
        CompanyInfoDAO companyInfoDAO = companyService.getCompany(request.getCompanyId());
        if (companyInfoDAO == null) {
            return MsResponse.fail("50002", "公司不存在");
        }
        CompanyDTO companyDTO = CompanyDTO.create(companyInfoDAO);
        //获取所有子公司
        List<CompanyDTO> subCompanyList = companyService.getCompanyList(GetCompanyCondition.builder()
                        .parentId(request.getCompanyId())
                        .build())
                .stream()
                .map(CompanyDTO::create)
                .collect(Collectors.toList());
        //构造可能用到的companyId列表
        Set<String> companyIdSet = new HashSet<String>() {{
            add(request.getCompanyId());
            addAll(subCompanyList.stream().map(CompanyDTO::getCompanyId).collect(Collectors.toList()));
        }};
        List<String> companyIdList = new ArrayList<>(companyIdSet);
        //部门列表（部门和公司关系）
        List<DepartmentDTO> departmentList = departmentService.getDepartmentList(GetDepartmentCondition.builder()
                .companyIdList(companyIdList)
                .build());
        //职位列表（职位和公司关系）
        List<PositionDTO> positionList = positionService.getPositionList(GetPositionCondition.builder()
                .companyIdList(companyIdList)
                .build());
        //职位和部门关系
        List<DepartmentPositionRelationDAO> dprList = departmentPositionService.getDPRList(GetDepartmentPositionCondition.builder()
                .positionIdList(positionList.stream().map(PositionDTO::getPositionId).collect(Collectors.toList()))
                .build());
        Map<String, String> positionId2DepartmentIdMap = dprList.stream().collect(Collectors.toMap(DepartmentPositionRelationDAO::getPositionId, DepartmentPositionRelationDAO::getDepartmentId, (dao1, dao2) -> dao2));
        //职位关联部门
        positionList = positionList.stream().peek(positionDTO -> positionDTO.setDepartmentId(positionId2DepartmentIdMap.getOrDefault(positionDTO.getPositionId(), ""))).collect(Collectors.toList());
        //组织架构和身份的关系
        List<ICRWithIdentityDTO> companyIdentityList = new ArrayList<>();
        List<IDRWithIdentityDTO> departmentIdentityList = new ArrayList<>();
        List<IPRWithIdentityDTO> positionIdentityList = new ArrayList<>();
        if (request.getIsGetUser() > 0) {
            companyIdentityList = identityCompanyService.getICRWithIdentityList(GetIdentityCompanyCondition.builder()
                    .companyIdList(companyIdList)
                    .build()).stream().map(ICRWithIdentityDTO::create).collect(Collectors.toList());
            departmentIdentityList = identityDepartmentService.getIDRWithIdentityList(GetIdentityDepartmentCondition.builder()
                    .departmentIdList(departmentList.stream().map(DepartmentDTO::getDepartmentId).collect(Collectors.toList()))
                    .build()).stream().map(IDRWithIdentityDTO::create).collect(Collectors.toList());
            positionIdentityList = identityPositionService.getIPRWithIdentityList(GetIdentityPositionCondition.builder()
                    .positionIdList(positionList.stream().map(PositionDTO::getPositionId).collect(Collectors.toList()))
                    .build()).stream().map(IPRWithIdentityDTO::create).collect(Collectors.toList());
        }
        //生成部门树形结构
        return MsResponse.success(
                companyService.createCompanyTree(companyDTO, subCompanyList, departmentList, positionList, companyIdentityList, departmentIdentityList, positionIdentityList)
        );
    }

    @RequestMapping("/batchGetCompanyTree")
    public MsResponse<List<TreeItemDTO>> batchGetCompanyTree(@RequestBody BatchGetCompanyTreeRequest request) {
        if (request.getCompanyIds() == null || request.getCompanyIds().isEmpty()) {
            return MsResponse.fail("50001", "参数错误");
        }
        List<CompanyInfoDAO> companyInfoDAOList = companyService.getCompanyListByIds(request.getCompanyIds());

        if (companyInfoDAOList.size() != request.getCompanyIds().size()) {
            return MsResponse.fail("50002", "部分公司不存在");
        }
        // 获取所有子公司
        List<CompanyDTO> allSubCompanyList = companyService.getAllDescendantCompanies(GetCompanyCondition.builder()
                        .parentIdList(request.getCompanyIds())
                        .build())
                .stream()
                .map(CompanyDTO::create)
                .collect(Collectors.toList());
        // 构造所有公司ID列表
        Set<String> allCompanyIdSet = new HashSet<>(request.getCompanyIds());
        allCompanyIdSet.addAll(allSubCompanyList.stream().map(CompanyDTO::getCompanyId).collect(Collectors.toList()));
        List<String> allCompanyIdList = new ArrayList<>(allCompanyIdSet);

        // 批量获取部门和职位
        List<DepartmentDTO> allDepartmentList = departmentService.getDepartmentList(GetDepartmentCondition.builder()
                .companyIdList(allCompanyIdList)
                .build());

        List<PositionDTO> allPositionList = positionService.getPositionList(GetPositionCondition.builder()
                .companyIdList(allCompanyIdList)
                .build());

        //获取职位和部门关系
        List<DepartmentPositionRelationDAO> allDprList = departmentPositionService.getDPRList(GetDepartmentPositionCondition.builder()
                .positionIdList(allPositionList.stream().map(PositionDTO::getPositionId).collect(Collectors.toList()))
                .build());

        Map<String, String> positionId2DepartmentIdMap = allDprList.stream()
                .collect(Collectors.toMap(DepartmentPositionRelationDAO::getPositionId, DepartmentPositionRelationDAO::getDepartmentId, (dao1, dao2) -> dao2));

        allPositionList = allPositionList.stream()
                .peek(positionDTO -> positionDTO.setDepartmentId(positionId2DepartmentIdMap.getOrDefault(positionDTO.getPositionId(), "")))
                .collect(Collectors.toList());

        Map<String, List<ICRWithIdentityDTO>> companyIdentityMap = new HashMap<>();
        Map<String, List<IDRWithIdentityDTO>> departmentIdentityMap = new HashMap<>();
        Map<String, List<IPRWithIdentityDTO>> positionIdentityMap = new HashMap<>();

        if (request.getIsGetUser() > 0) {
            companyIdentityMap = identityCompanyService.getICRWithIdentityList(
                            GetIdentityCompanyCondition.builder()
                                    .companyIdList(allCompanyIdList)
                                    .build())
                    .stream()
                    .map(ICRWithIdentityDTO::create)
                    .collect(Collectors.groupingBy(ICRWithIdentityDTO::getCompanyId));

            departmentIdentityMap = identityDepartmentService.getIDRWithIdentityList(
                            GetIdentityDepartmentCondition.builder()
                                    .departmentIdList(allDepartmentList.stream()
                                            .map(DepartmentDTO::getDepartmentId)
                                            .collect(Collectors.toList()))
                                    .build())
                    .stream()
                    .map(IDRWithIdentityDTO::create)
                    .collect(Collectors.groupingBy(IDRWithIdentityDTO::getDepartmentId));

            positionIdentityMap = identityPositionService.getIPRWithIdentityList(
                            GetIdentityPositionCondition.builder()
                                    .positionIdList(allPositionList.stream()
                                            .map(PositionDTO::getPositionId)
                                            .collect(Collectors.toList()))
                                    .build())
                    .stream()
                    .map(IPRWithIdentityDTO::create)
                    .collect(Collectors.groupingBy(IPRWithIdentityDTO::getPositionId));
        }

        Map<String, List<CompanyDTO>> subCompanyMap = allSubCompanyList.stream()
                .collect(Collectors.groupingBy(CompanyDTO::getParentId));

        Map<String, List<DepartmentDTO>> departmentMap = allDepartmentList.stream()
                .collect(Collectors.groupingBy(DepartmentDTO::getCompanyId));

        Map<String, List<PositionDTO>> positionMap = allPositionList.stream()
                .collect(Collectors.groupingBy(PositionDTO::getCompanyId));


        List<TreeItemDTO> treeList = new ArrayList<>();
        for (String companyId : request.getCompanyIds()) {
            CompanyInfoDAO companyInfoDAO = companyInfoDAOList.stream()
                    .filter(dao -> dao.getCompanyId().equals(companyId))
                    .findFirst()
                    .orElse(null);
            if (companyInfoDAO == null) {
                continue; // 跳过不存在的公司
            }
            CompanyDTO companyDTO = CompanyDTO.create(companyInfoDAO);
            List<CompanyDTO> subCompanyList = subCompanyMap.getOrDefault(companyId, new ArrayList<>());
            List<DepartmentDTO> departmentList = departmentMap.getOrDefault(companyId, new ArrayList<>());
            List<PositionDTO> positionList = positionMap.getOrDefault(companyId, new ArrayList<>());
            List<ICRWithIdentityDTO> companyIdentityList = companyIdentityMap.getOrDefault(companyId, new ArrayList<>());
            List<IDRWithIdentityDTO> departmentIdentityList = departmentIdentityMap.entrySet().stream()
                    .filter(entry -> departmentList.stream().anyMatch(dept -> dept.getDepartmentId().equals(entry.getKey())))
                    .flatMap(entry -> entry.getValue().stream())
                    .collect(Collectors.toList());
            List<IPRWithIdentityDTO> positionIdentityList = positionIdentityMap.entrySet().stream()
                    .filter(entry -> positionList.stream().anyMatch(pos -> pos.getPositionId().equals(entry.getKey())))
                    .flatMap(entry -> entry.getValue().stream())
                    .collect(Collectors.toList());
            TreeItemDTO treeItem = companyService.createCompanyTree(companyDTO, subCompanyList, departmentList, positionList,
                    companyIdentityList, departmentIdentityList, positionIdentityList);
            treeList.add(treeItem);
        }

        return MsResponse.success(treeList);
    }


    /**
     * 获取公司树，带人数
     *
     * @param request
     * @return com.microservice.user.entity.response.ms.MsResponse<com.microservice.user.entity.dto.ms.TreeItemDTO>
     * <AUTHOR>
     * @date 2024/12/12 16:00
     **/
    @RequestMapping("/getCompanyTreeWithPeopleNum")
    public MsResponse<TreeItemV2DTO> getCompanyTreeWithPeopleNum(@RequestBody @Valid GetCompanyTreeWithPeopleNumRequest request) {
        return companyService.getCompanyTreeWithPeopleNum(request);
    }

    /**
     * 获取指定公司组织架构下用户信息 &身份信息
     *
     * @param request
     * @return
     */
    @RequestMapping("/getCompanyUserTree")
    public MsResponse<TreeItemDTO> getCompanyUserTree(@RequestBody GetCompanyTreeRequest request) {
        if (StringUtil.isEmpty(request.getCompanyId())) {
            return MsResponse.fail("50001", "参数错误");
        }
        CompanyInfoDAO companyInfoDAO = companyService.getCompany(request.getCompanyId());
        if (companyInfoDAO == null) {
            return MsResponse.fail("50002", "公司不存在");
        }
        CompanyDTO companyDTO = CompanyDTO.create(companyInfoDAO);
        //获取所有子公司
        List<CompanyDTO> subCompanyList = companyService.getCompanyList(GetCompanyCondition.builder()
                        .parentId(request.getCompanyId())
                        .build())
                .stream()
                .map(CompanyDTO::create)
                .collect(Collectors.toList());
        //构造可能用到的companyId列表
        Set<String> companyIdSet = new HashSet<String>() {{
            add(request.getCompanyId());
            addAll(subCompanyList.stream().map(CompanyDTO::getCompanyId).collect(Collectors.toList()));
        }};
        List<String> companyIdList = new ArrayList<>(companyIdSet);
        //部门列表（部门和公司关系）
        List<DepartmentDTO> departmentList = departmentService.getDepartmentList(GetDepartmentCondition.builder()
                .companyIdList(companyIdList)
                .build());
        //职位列表（职位和公司关系）
        List<PositionDTO> positionList = new ArrayList<>();
        //组织架构和身份的关系
        List<ICRWithIdentityDTO> companyIdentityList = new ArrayList<>();
        List<IDRWithIdentityDTO> departmentIdentityList = new ArrayList<>();
        List<IPRWithIdentityDTO> positionIdentityList = new ArrayList<>();
        if (request.getIsGetUser() > 0) {
            companyIdentityList = identityCompanyService.getICRWithIdentityList(GetIdentityCompanyCondition.builder()
                    .companyIdList(companyIdList)
                    .build()).stream().map(ICRWithIdentityDTO::create).collect(Collectors.toList());
            departmentIdentityList = identityDepartmentService.getIDRWithIdentityList(GetIdentityDepartmentCondition.builder()
                    .departmentIdList(departmentList.stream().map(DepartmentDTO::getDepartmentId).collect(Collectors.toList()))
                    .build()).stream().map(IDRWithIdentityDTO::create).collect(Collectors.toList());
        }
        //生成部门树形结构
        TreeItemDTO treeItemDTO = companyService.createCompanyTree(
                companyDTO,
                subCompanyList,
                departmentList,
                positionList,
                companyIdentityList,
                departmentIdentityList,
                positionIdentityList
        );
        if (Objects.nonNull(treeItemDTO.getExtraContent())) {
            treeItemDTO.setExtraContent(null);
        }
        return MsResponse.success(treeItemDTO);
    }
}
