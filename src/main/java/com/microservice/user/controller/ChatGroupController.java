package com.microservice.user.controller;

import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.ChatGroupConstant;
import com.microservice.user.entity.condition.GetChatGroupCondition;
import com.microservice.user.entity.condition.GetIdentityChatGroupCondition;
import com.microservice.user.entity.condition.GetIdentityCondition;
import com.microservice.user.entity.dao.ChatGroupInfoDAO;
import com.microservice.user.entity.dao.IdentityChatGroupRelationDAO;
import com.microservice.user.entity.dto.ms.*;
import com.microservice.user.entity.request.ms.chatGroup.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.ChatGroupService;
import com.microservice.user.service.IdentityChatGroupService;
import com.microservice.user.service.IdentityService;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/chatGroup")
public class ChatGroupController {

    @Autowired
    private IdentityService identityService;
    @Autowired
    private ChatGroupService chatGroupService;
    @Autowired
    private IdentityChatGroupService identityChatGroupService;

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addChatGroup")
    public MsResponse<ChatGroupDTO> addChatGroup(@Valid @RequestBody AddChatGroupRequest request) {
        List<IdentityDTO> identityDTOList = identityService.getIdentityList(GetIdentityCondition.builder()
                .identityIdList(request.getIdentityIdList())
                .dataChannelId(Common.getDataChannelId(request))
                .build());
        if (identityDTOList.isEmpty()) {
            return MsResponse.fail("50004", "群成员错误");
        }
        ChatGroupInfoDAO chatGroupInfoDAO = chatGroupService.addChatGroup(request.getChatGroupName(), request.getExtraContent(), Common.getAppChannelId(request), Common.getDataChannelId(request), identityDTOList.stream().map(IdentityDTO::getIdentityId).collect(Collectors.toList()));
        return MsResponse.success(chatGroupService.createChatGroupEntity(Common.getAppChannelId(request), chatGroupInfoDAO.getChatGroupId()));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateChatGroup")
    public MsResponse<ChatGroupDTO> updateChatGroup(@Valid @RequestBody UpdateChatGroupRequest request) {
        ChatGroupInfoDAO chatGroupInfoDAO = chatGroupService.getChatGroup(request.getChatGroupId());
        if (chatGroupInfoDAO == null) {
            return MsResponse.fail("50002", "群聊不存在");
        }
        ChatGroupInfoDAO updateData = ChatGroupInfoDAO.builder()
                .chatGroupId(request.getChatGroupId())
                .build();
        if (request.getChatGroupName() != null) {
            updateData.setChatGroupName(request.getChatGroupName());
        }
        if (request.getExtraContent() != null) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        chatGroupService.updateChatGroup(updateData, request.getIdentityIdList());
        return MsResponse.success(chatGroupService.createChatGroupEntity(Common.getAppChannelId(request), request.getChatGroupId()));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteChatGroup")
    public MsResponse<ChatGroupDTO> deleteChatGroup(@Valid @RequestBody DeleteChatGroupRequest request) {
        ChatGroupInfoDAO chatGroupInfoDAO = chatGroupService.getChatGroup(request.getChatGroupId());
        if (chatGroupInfoDAO == null) {
            return MsResponse.fail("50002", "群聊不存在");
        }
        ChatGroupDTO chatGroupDTO = chatGroupService.createChatGroupEntity(Common.getAppChannelId(request), request.getChatGroupId());
        chatGroupService.deleteChatGroup(request.getChatGroupId());
        return MsResponse.success(chatGroupDTO);
    }

    @RequestMapping("getChatGroupList")
    public MsResponse<DataListDTO<ChatGroupDTO>> getChatGroupList(@RequestBody GetChatGroupListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), ChatGroupConstant.CHAT_GROUP_MAIN_COLUMN);
        //构造查询条件
        GetChatGroupCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetChatGroupCondition.class);
        //开始查询
        int dataCount = chatGroupService.getChatGroupCount(condition);
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<ChatGroupDTO>builder().build());
        }
        return MsResponse.success(
                DataListDTO.<ChatGroupDTO>builder()
                        .dataCount(dataCount)
                        .dataList(chatGroupService.getChatGroupList(condition, (page - 1) * limit, limit)
                                .stream()
                                .map(ChatGroupDTO::create)
                                .collect(Collectors.toList()))
                        .build()
        );
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addChatGroupIdentity")
    public MsResponse<Map<String, Object>> addChatGroupIdentity(@Valid @RequestBody AddChatGroupIdentityRequest request) {
        ChatGroupInfoDAO chatGroupInfoDAO = chatGroupService.getChatGroup(request.getChatGroupId());
        if (chatGroupInfoDAO == null) {
            return MsResponse.fail("50003", "群聊不存在");
        }
        List<IdentityChatGroupRelationDAO> icgrList = chatGroupService.addChatGroupIdentity(request.getChatGroupId(), request.getIdentityIdList(), request.getExtraContent());
        return MsResponse.success(new HashMap<>() {{
            put("identityList", icgrList.stream().map(IdentityChatGroupRelationDTO::create).collect(Collectors.toList()));
            put("chatGroupId", chatGroupInfoDAO.getChatGroupId());
            put("chatGroupName", chatGroupInfoDAO.getChatGroupName());
        }});
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateChatGroupIdentity")
    public MsResponse<IdentityChatGroupRelationDTO> updateChatGroupIdentity(@Valid @RequestBody UpdateChatGroupIdentityRequest request) {
        ChatGroupInfoDAO chatGroupInfoDAO = chatGroupService.getChatGroup(request.getChatGroupId());
        if (chatGroupInfoDAO == null) {
            return MsResponse.fail("50003", "群聊不存在");
        }
        IdentityChatGroupRelationDAO identityChatGroupRelationDAO = identityChatGroupService.getICGR(
                GetIdentityChatGroupCondition.builder()
                        .chatGroupId(request.getChatGroupId())
                        .identityId(request.getIdentityId())
                        .build());
        if (identityChatGroupRelationDAO == null) {
            return MsResponse.fail("50004", "群成员不存在");
        }
        IdentityChatGroupRelationDAO updateData = IdentityChatGroupRelationDAO.builder()
                .identityId(request.getIdentityId())
                .chatGroupId(request.getChatGroupId())
                .build();
        if (request.getExtraContent() != null) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        identityChatGroupService.updateICGR(updateData);
        BeanUtils.copyProperties(updateData, identityChatGroupRelationDAO);
        return MsResponse.success(IdentityChatGroupRelationDTO.create(identityChatGroupRelationDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteChatGroupIdentity")
    public MsResponse<Map<String, Object>> deleteChatGroupIdentity(@Valid @RequestBody DeleteChatGroupIdentityRequest request) {
        List<IdentityChatGroupRelationDAO> icgrList = identityChatGroupService.getICGRList(
                GetIdentityChatGroupCondition.builder()
                        .chatGroupId(request.getChatGroupId())
                        .identityIdList(request.getIdentityIdList())
                        .build());
        if (icgrList.isEmpty()) {
            return MsResponse.fail("50004", "群成员不存在");
        }
        identityChatGroupService.deleteICGR(GetIdentityChatGroupCondition.builder()
                .identityIdList(request.getIdentityIdList())
                .chatGroupId(request.getChatGroupId())
                .build());
        return MsResponse.success(
                new HashMap<>() {{
                    put("icgrList", icgrList.stream().map(IdentityChatGroupRelationDTO::create).collect(Collectors.toList()));
                    put("chatGroupId", request.getChatGroupId());
                    put("identityId", icgrList.stream().map(IdentityChatGroupRelationDAO::getIdentityId).collect(Collectors.toList()));
                }}
        );
    }

    @RequestMapping("/getChatGroupIdentityList")
    public MsResponse<DataListDTO<IdentityChatGroupRelationDTO>> getChatGroupIdentityList(@RequestBody GetChatGroupIdentityListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), ChatGroupConstant.CHAT_GROUP_IDENTITY_MAIN_COLUMN);
        //构造查询条件
        GetIdentityChatGroupCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetIdentityChatGroupCondition.class);
        //开始查询
        int dataCount = identityChatGroupService.getICGRCount(condition);
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<IdentityChatGroupRelationDTO>builder().build());
        }
        List<IdentityChatGroupRelationDAO> icgrList = identityChatGroupService.getICGRList(condition, (page - 1) * limit, limit);
        Map<String, IdentityDTO> identityId2IdentityMap = identityService.createIdentityEntity(
                Common.getAppChannelId(request),
                icgrList.stream().map(IdentityChatGroupRelationDAO::getIdentityId).collect(Collectors.toList())
        ).stream().collect(Collectors.toMap(IdentityDTO::getIdentityId, identityDTO -> identityDTO, (dao1, dao2) -> dao2));
        return MsResponse.success(
                DataListDTO.<IdentityChatGroupRelationDTO>builder()
                        .dataCount(dataCount)
                        .dataList(icgrList
                                .stream()
                                .map(dao -> {
                                    IdentityChatGroupRelationDTO dto = IdentityChatGroupRelationDTO.create(dao);
                                    dto.setIdentity(identityId2IdentityMap.get(dao.getIdentityId()));
                                    return dto;
                                })
                                .collect(Collectors.toList()))
                        .build()
        );
    }


    @RequestMapping("/searchChatGroup")
    public MsResponse<DataListDTO<SearchChatGroupDTO>> searchChatGroup(@RequestBody SearchChatGroupRequest request) {
       List<SearchChatGroupDTO> searchChatGroupDTOS =   chatGroupService.searchChatGroup(request);

       return MsResponse.success(DataListDTO.create(searchChatGroupDTOS));
    }
}
