package com.microservice.user.controller;

import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.EntityConstant;
import com.microservice.user.entity.condition.GetEntityCondition;
import com.microservice.user.entity.condition.GetEntityRelationCondition;
import com.microservice.user.entity.dao.EntityInfoDAO;
import com.microservice.user.entity.dao.EntityRelationDAO;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.EntityDTO;
import com.microservice.user.entity.dto.ms.EntityRelationDTO;
import com.microservice.user.entity.request.ms.entity.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.EntityRelationService;
import com.microservice.user.service.EntityService;
import com.microservice.user.service.IdentityService;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/entity")
public class EntityController {

    @Autowired
    private EntityService entityService;
    @Autowired
    private EntityRelationService entityRelationService;
    @Autowired
    private IdentityService identityService;

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addEntity")
    public MsResponse<EntityDTO> addEntity(@Valid @RequestBody AddEntityRequest request) {
        String appChannelId = Common.getAppChannelId(request);
        String dataChannelId = Common.getDataChannelId(request);
        EntityInfoDAO entityInfoDAO = entityService.addEntity(request.getEntityName(), request.getType(), request.getExtraContent(), request.getSort(), appChannelId, dataChannelId);
        return MsResponse.success(EntityDTO.create(entityInfoDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateEntity")
    public MsResponse<EntityDTO> updateEntity(@Valid @RequestBody UpdateEntityRequest request) {
        EntityInfoDAO entityInfoDAO = entityService.getEntity(request.getEntityId());
        if (entityInfoDAO == null) {
            return MsResponse.fail("50002", "标签不存在");
        }
        EntityInfoDAO updateData = EntityInfoDAO.builder().entityId(request.getEntityId()).build();
        entityService.updateEntity(updateData);
        BeanUtils.copyProperties(updateData, entityInfoDAO);
        return MsResponse.success(EntityDTO.create(entityInfoDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteEntity")
    public MsResponse<EntityDTO> deleteEntity(@Valid @RequestBody DeleteEntityRequest request) {
        EntityInfoDAO entityInfoDAO = entityService.getEntity(request.getEntityId());
        if (entityInfoDAO == null) {
            return MsResponse.fail("50002", "标签不存在");
        }
        entityService.deleteEntity(request.getEntityId());
        return MsResponse.success(EntityDTO.create(entityInfoDAO));
    }

    @RequestMapping("/getEntityList")
    public MsResponse<DataListDTO<EntityDTO>> getEntityList(@RequestBody GetEntityListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        String dataChannelId = Common.getDataChannelId(request);
        Map<String, Object> conditionMap = new HashMap<>() {{
            put("dataChannelId", dataChannelId);
        }};
        //isShowAll大于0时忽略渠道号
        if (request.getIsShowAll() > 0) {
            conditionMap.remove("dataChannelId");
        }
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), EntityConstant.ENTITY_MAIN_COLUMN);
        //构造查询条件
        GetEntityCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetEntityCondition.class);
        //开始查询
        return MsResponse.success(
                DataListDTO.<EntityDTO>builder()
                        .dataList(entityService.getEntityList(condition, (page - 1) * limit, limit)
                                .stream()
                                .map(EntityDTO::create)
                                .collect(Collectors.toList()))
                        .dataCount(entityService.getEntityCount(condition))
                        .build()
        );
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addEntityRelation")
    public MsResponse<Map<String, Object>> addEntityRelation(@Valid @RequestBody AddEntityRelationRequest request) {
        EntityInfoDAO entityInfoDAO = entityService.getEntity(request.getEntityId());
        if (entityInfoDAO == null) {
            return MsResponse.fail("50003", "标签不存在");
        }
        List<String> targetEntityIdList = entityService.getEntityList(
                GetEntityCondition.builder()
                        .entityIdList(request.getTargetEntityIdList())
                        .build()
        ).stream().map(EntityInfoDAO::getEntityId).collect(Collectors.toList());
        if (targetEntityIdList.isEmpty()) {
            return MsResponse.fail("50004", "目标标签不存在");
        }
        List<EntityRelationDTO> entityRelationDTOList = entityRelationService.addEntityRelationList(
                request.getEntityId(),
                targetEntityIdList
        );
        return MsResponse.success(new HashMap<>() {{
            put("entityId", entityInfoDAO.getEntityId());
            put("targetEntityId", targetEntityIdList);
            put("entityRelationList", entityRelationDTOList);
        }});
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateEntityRelation")
    public MsResponse<EntityRelationDTO> updateEntityRelation(@RequestBody @Valid UpdateEntityRelationRequest request) {
        EntityInfoDAO entityInfoDAO = entityService.getEntity(request.getEntityId());
        if (entityInfoDAO == null) {
            return MsResponse.fail("50001", "标签不存在");
        }
        EntityInfoDAO targetEntityInfoDAO = entityService.getEntity(request.getTargetEntityId());
        if (targetEntityInfoDAO == null) {
            return MsResponse.fail("50002", "目标标签不存在");
        }
        EntityRelationDAO entityRelationDAO = entityRelationService.getEntityRelation(
                GetEntityRelationCondition.builder()
                        .entityId(request.getEntityId())
                        .targetEntityId(request.getTargetEntityId())
                        .build()
        );
        if (entityRelationDAO == null) {
            return MsResponse.fail("50003", "标签关系不存在");
        }
        EntityRelationDAO updateData = EntityRelationDAO.builder()
                .entityId(entityInfoDAO.getEntityId())
                .targetEntityId(targetEntityInfoDAO.getEntityId())
                .build();
        if (request.getExtraContent() != null && !request.getExtraContent().isEmpty()) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        entityRelationService.updateEntityRelation(
                updateData,
                GetEntityRelationCondition.builder().build()
        );
        BeanUtils.copyProperties(updateData, entityRelationDAO);
        return MsResponse.success(EntityRelationDTO.create(entityRelationDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteEntityRelation")
    public MsResponse<Map<String, Object>> deleteEntityRelation(@RequestBody DeleteEntityRelationRequest request) {
        //构造entityIdList
        List<String> entityIdList = Optional.ofNullable(request.getEntityIdList()).orElse(new ArrayList<>());
        if (StringUtil.isEmpty(request.getEntityId())) {
            entityIdList.add(request.getEntityId());
        }
        //构造targetEntityIdList
        List<String> targetEntityIdList = Optional.ofNullable(request.getTargetEntityIdList()).orElse(new ArrayList<>());
        if (StringUtil.isEmpty(request.getTargetEntityId())) {
            targetEntityIdList.add(request.getTargetEntityId());
        }
        //删除数据
        entityRelationService.deleteEntityRelation(GetEntityRelationCondition.builder()
                .entityIdList(entityIdList)
                .targetEntityIdList(targetEntityIdList)
                .build());
        return MsResponse.success(new HashMap<>() {{
            put("entityId", entityIdList);
            put("targetEntityId", targetEntityIdList);
        }});
    }

    @RequestMapping("/getEntityRelationList")
    public MsResponse<DataListDTO<EntityRelationDTO>> getEntityRelationList(@RequestBody GetEntityRelationListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        String dataChannelId = Common.getDataChannelId(request);
        GetEntityRelationCondition condition = GetEntityRelationCondition.builder()
                .dataChannelId(dataChannelId)
                .build();
        //isShowAll大于0时忽略渠道号
        if (request.getIsShowAll() > 0) {
            condition = GetEntityRelationCondition.builder().build();
        }
        if (request.getCondition() != null && !request.getCondition().isEmpty()) {
            Map<String, Object> entityCondition = new HashMap<>();
            Map<String, Object> extraContentCondition = new HashMap<>();
            List<String> entityMainColumnList = Arrays.asList(EntityConstant.ENTITY_RELATION_MAIN_COLUMN);
            for (Map.Entry<String, Object> entry : request.getCondition().entrySet()) {
                if (entityMainColumnList.contains(entry.getKey())) {
                    entityCondition.put(entry.getKey(), entry.getValue());
                } else {
                    extraContentCondition.put(entry.getKey(), entry.getValue());
                }
            }
            condition = StringUtil.jsonDecode(StringUtil.jsonEncode(entityCondition), GetEntityRelationCondition.class);
            condition.setExtraContentCondition(extraContentCondition);
        }
        return MsResponse.success(
                DataListDTO.<EntityRelationDTO>builder()
                        .dataList(entityRelationService.getERWithEntityList(condition, (page - 1) * limit, limit)
                                .stream()
                                .map(EntityRelationDTO::create)
                                .collect(Collectors.toList()))
                        .dataCount(entityRelationService.getERWithEntityCount(condition))
                        .build()
        );
    }
}
