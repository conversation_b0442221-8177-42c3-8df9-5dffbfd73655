package com.microservice.user.controller;

import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.FriendConstant;
import com.microservice.user.entity.condition.GetIdentityCondition;
import com.microservice.user.entity.condition.GetIdentityFriendCondition;
import com.microservice.user.entity.dao.IdentityFriendRelationDAO;
import com.microservice.user.entity.dao.IdentityInfoDAO;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.IdentityDTO;
import com.microservice.user.entity.dto.ms.IdentityFriendRelationDTO;
import com.microservice.user.entity.request.ms.friend.AddFriendRequest;
import com.microservice.user.entity.request.ms.friend.DeleteFriendRequest;
import com.microservice.user.entity.request.ms.friend.GetFriendListRequest;
import com.microservice.user.entity.request.ms.friend.UpdateFriendRequest;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.IdentityFriendService;
import com.microservice.user.service.IdentityService;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/friend")
public class FriendController {

    @Autowired
    private IdentityService identityService;
    @Autowired
    private IdentityFriendService identityFriendService;

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addFriend")
    public MsResponse<Map<String, Object>> addFriend(@Valid @RequestBody AddFriendRequest request) {
        String appChannelId = Common.getAppChannelId(request);
        String dataChannelId = Common.getDataChannelId(request);
        //验证当前身份
        IdentityInfoDAO identityInfoDAO = identityService.getIdentity(
                GetIdentityCondition.builder()
                        .identityId(request.getIdentityId())
                        .dataChannelId(dataChannelId)
                        .build()
        );
        if (identityInfoDAO == null) {
            return MsResponse.fail("50005", "用户身份不存在");
        }
        //验证目标用户
        List<IdentityDTO> targetIdentityList = identityService.getIdentityList(
                GetIdentityCondition.builder()
                        .identityIdList(request.getTargetIdentityIdList())
                        .dataChannelId(dataChannelId)
                        .build()
        );
        if (targetIdentityList.isEmpty()) {
            return MsResponse.fail("50006", "目标用户身份不存在");
        }
        //添加好友
        List<IdentityFriendRelationDAO> ifrList = identityFriendService.addIdentityFriend(
                request.getIdentityId(),
                request.getTargetIdentityIdList(),
                request.getExtraContent(),
                appChannelId,
                dataChannelId
        );
        List<IdentityFriendRelationDTO> friendRelationDTOList = ifrList.stream().map(IdentityFriendRelationDTO::create).collect(Collectors.toList());
        return MsResponse.success(new HashMap<>() {{
            put("friendList", friendRelationDTOList);
            put("identityId", request.getIdentityId());
            put("targetIdentityId", friendRelationDTOList.stream().map(IdentityFriendRelationDTO::getTargetIdentityId).collect(Collectors.toList()));
        }});
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateFriend")
    public MsResponse<IdentityFriendRelationDTO> updateFriend(@Valid @RequestBody UpdateFriendRequest request) {
        IdentityFriendRelationDAO identityFriendRelationDAO = identityFriendService.getIdentityFriendRelation(request.getIdentityId(), request.getTargetIdentityId());
        if (identityFriendRelationDAO == null) {
            return MsResponse.fail("50003", "好友关系不存在");
        }
        IdentityFriendRelationDAO updateData = IdentityFriendRelationDAO.builder()
                .identityId(request.getIdentityId())
                .targetIdentityId(request.getTargetIdentityId())
                .build();
        if (request.getExtraContent() != null) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        identityFriendService.updateIdentityFriendRelation(updateData);
        BeanUtils.copyProperties(updateData, identityFriendRelationDAO);
        return MsResponse.success(IdentityFriendRelationDTO.create(identityFriendRelationDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteFriend")
    public MsResponse<Map<String, Object>> deleteFriend(@Valid @RequestBody DeleteFriendRequest request) {
        //验证目标用户
        List<IdentityFriendRelationDAO> ifrDAOList = identityFriendService.getIdentityFriendRelationList(GetIdentityFriendCondition.builder()
                .identityId(request.getIdentityId())
                .targetIdentityIdList(request.getTargetIdentityIdList())
                .build());
        if (ifrDAOList.isEmpty()) {
            return MsResponse.fail("50003", "好友关系不存在");
        }
        for (IdentityFriendRelationDAO ifr : ifrDAOList) {
            identityFriendService.deleteIdentityFriendRelation(ifr.getIdentityId(), ifr.getTargetIdentityId());
            if (request.getIsDeleteEachOther() > 0) {
                identityFriendService.deleteIdentityFriendRelation(ifr.getTargetIdentityId(), ifr.getIdentityId());
            }
        }
        return MsResponse.success(new HashMap<>() {{
            put("identityId", request.getIdentityId());
            put("targetIdentityId", ifrDAOList.stream().map(IdentityFriendRelationDAO::getTargetIdentityId).collect(Collectors.toList()));
        }});
    }

    @RequestMapping("/getFriendList")
    public MsResponse<DataListDTO<IdentityFriendRelationDTO>> getFriendList(@RequestBody GetFriendListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        String dataChannelId = Common.getDataChannelId(request);
        Map<String, Object> conditionMap = new HashMap<>() {{
            put("dataChannelId", dataChannelId);
        }};
        //isShowAll大于0时忽略渠道号
        if (request.getIsShowAll() > 0) {
            conditionMap.remove("dataChannelId");
        }
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), FriendConstant.FRIEND_MAIN_COLUMN);
        //构造查询条件
        GetIdentityFriendCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetIdentityFriendCondition.class);
        //开始查询
        int dataCount = identityFriendService.getIdentityFriendRelationCount(condition);
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<IdentityFriendRelationDTO>builder().build());
        }
        List<IdentityFriendRelationDAO> ifrList = identityFriendService.getIdentityFriendRelationList(condition, (page - 1) * limit, limit);
        //好友用户实体
        Map<String, IdentityDTO> identityId2IdentityMap = identityService.createIdentityEntity(
                Common.getAppChannelId(request),
                ifrList.stream()
                        .map(IdentityFriendRelationDAO::getTargetIdentityId)
                        .collect(Collectors.toList())
        ).stream().collect(Collectors.toMap(IdentityDTO::getIdentityId, identityDTO -> identityDTO, (dto1, dto2) -> dto2));
        return MsResponse.success(
                DataListDTO.<IdentityFriendRelationDTO>builder()
                        .dataCount(dataCount)
                        .dataList(ifrList.stream().map(ifr -> {
                            IdentityFriendRelationDTO dto = IdentityFriendRelationDTO.create(ifr);
                            dto.setTargetIdentity(identityId2IdentityMap.get(dto.getTargetIdentityId()));
                            return dto;
                        }).collect(Collectors.toList()))
                        .build()
        );
    }
}
