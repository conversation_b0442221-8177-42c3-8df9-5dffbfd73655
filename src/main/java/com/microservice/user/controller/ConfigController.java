package com.microservice.user.controller;

import com.microservice.user.config.ApplicationConfig;
import com.microservice.user.entity.request.ms.config.MsUpdateConfigRequest;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.util.ConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/config")
public class ConfigController {

    @Autowired
    private ApplicationConfig applicationConfig;

    @RequestMapping("/updateConfig")
    public MsResponse<Map<String, Object>> updateConfig(@RequestBody(required = false) MsUpdateConfigRequest request) {
        ConfigUtil.updateConfig(
                applicationConfig.getMsConfigDomain(),
                applicationConfig.getAppChannelId(),
                applicationConfig.getDataChannelId()
        );
        return MsResponse.success(new HashMap<String, Object>() {{
            put("system", ConfigUtil.getSystemConfig());
        }});
    }

    @RequestMapping("/getSystemConfig")
    public MsResponse getSystemConfig() {
        return MsResponse.success(ConfigUtil.getSystemConfig());
    }

}
