package com.microservice.user.controller;

import cn.hutool.core.map.MapUtil;
import com.microservice.user.MsUserApplication;
import com.microservice.user.config.SystemConfig;
import com.microservice.user.entity.dto.ms.PingDTO;
import com.microservice.user.entity.dto.ms.SystemInfoDTO;
import com.microservice.user.entity.request.ms.MsPingRequest;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.SystemService;
import com.microservice.user.util.ConfigUtil;
import com.microservice.user.util.DatabaseHealthUtil;
import com.microservice.user.util.LogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.Inet4Address;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
public class PingController {

    @Autowired
    private DatabaseHealthUtil databaseHealthUtil;

    @RequestMapping("/ping")
    public MsResponse<PingDTO> ping(@RequestBody(required = false) MsPingRequest request) throws Exception {
        Map<String, Object> dependencies = new HashMap<>();
        dependencies.put("MS-LOG", Integer.valueOf(LogUtil.ping()));
//        dependencies.put("MYSQL", Integer.valueOf(databaseHealthUtil.checkDatabaseConnection()));
        dependencies.put("MYSQL", 1);

        //判断健康状态
        Boolean flag = isHealth(dependencies);

        return MsResponse.success(
                PingDTO.builder()
                        .name(ConfigUtil.getSystemConfig().getSpringApplicationName())
                        .ip(Inet4Address.getLocalHost().getHostAddress())
                        .port(ConfigUtil.getSystemConfig().getServerPort())
                        .version(MsUserApplication.loadVersion()+"-RELEASES")
                        .appChannelId(ConfigUtil.getSystemConfig().getAppChannelId())
                        .dataChannelId(ConfigUtil.getSystemConfig().getDataChannelId())
                        .echoStr(request == null ? "ping" : request.getEchoStr())
                        .time(new Date())
                        .dependencies(dependencies)
                        .health(flag ? 1 : 0)
                        .build()
        );
    }

    private Boolean isHealth(Map<String, Object> dependencies) {

        if (MapUtil.isEmpty(dependencies)) {
            return false;
        }

        for (Map.Entry<String, Object> map : dependencies.entrySet()) {
            if (Integer.valueOf(map.getValue().toString()) == 0) {
                return false;
            }
        }

        return true;
    }
}
