package com.microservice.user.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.IdentityConstant;
import com.microservice.user.constant.SystemConstant;
import com.microservice.user.constant.TokenConstant;
import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.IdentityDTO;
import com.microservice.user.entity.dto.ms.UserDTO;
import com.microservice.user.entity.dto.ms.identity.IdentityByCompanyDTO;
import com.microservice.user.entity.request.ms.identity.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.exception.IdentityException;
import com.microservice.user.service.*;
import com.microservice.user.util.RedisUtil;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/identity")
public class IdentityController {

    @Autowired
    private UserService userService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private IdentityCompanyService identityCompanyService;
    @Autowired
    private IdentityDepartmentService identityDepartmentService;
    @Autowired
    private IdentityPositionService identityPositionService;

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addIdentity")
    public MsResponse<IdentityDTO> addIdentity(@Valid @RequestBody AddIdentityRequest request) {
        if (StringUtil.isEmpty(request.getUserId()) && StringUtil.isEmpty(request.getIdentityId())) {
            return MsResponse.fail("50001", "用户id或身份id错误");
        }
        String appChannelId = Common.getAppChannelId(request);
        String dataChannelId = Common.getDataChannelId(request);
        if (StringUtil.isEmpty(appChannelId)) {
            return MsResponse.fail("50002", "应用渠道id错误");
        }
        if (StringUtil.isEmpty(dataChannelId)) {
            return MsResponse.fail("50003", "数据渠道id错误");
        }
        UserInfoDAO userInfoDAO;
        if (!StringUtil.isEmpty(request.getUserId())) {
            userInfoDAO = userService.getUser(GetUserCondition.builder().userId(request.getUserId()).build());
        } else {
            userInfoDAO = userService.getUserByIdentityId(request.getIdentityId());
        }
        if (userInfoDAO == null) {
            return MsResponse.fail("50004", "用户不存在");
        }
        //添加身份
        IdentityInfoDAO identityInfoDAO = identityService.addIdentity(userInfoDAO.getUserId(), request.getNewIdentityId(), request.getIdentityName(), request.getExtraContent(), appChannelId, dataChannelId);
        //添加关联关系
        if (request.getRelationList() != null && !request.getRelationList().isEmpty()) {
            identityService.addIdentityRelationList(identityInfoDAO.getIdentityId(), request.getRelationList(), appChannelId);
        }
        //清除用户缓存
        userService.clearUserCache(userInfoDAO.getUserId());
        if (!StringUtil.isEmpty(request.getIdentityId())) {
            //清除身份缓存
            identityService.clearIdentityCache(request.getIdentityId());
        }
        return MsResponse.success(identityService.createIdentityEntity(appChannelId, identityInfoDAO.getIdentityId()));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateIdentity")
    public MsResponse<IdentityDTO> updateIdentity(@Valid @RequestBody UpdateIdentityRequest request) {
        UserInfoDAO userInfoDAO = userService.getUserByIdentityId(request.getIdentityId());
        if (userInfoDAO == null) {
            return MsResponse.fail("50002", "用户不存在");
        }
        IdentityInfoDAO identityInfoDAO = identityService.getIdentity(request.getIdentityId());
        if (identityInfoDAO == null) {
            return MsResponse.fail("50003", "身份不存在");
        }
        //修改身份信息
        IdentityInfoDAO updateData = IdentityInfoDAO.builder().identityId(request.getIdentityId()).build();
        if (request.getIdentityName() != null) {
            updateData.setIdentityName(request.getIdentityName());
        }
        if (request.getExtraContent() != null) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        if (request.getSort() != null) {
            updateData.setSort(request.getSort());
        }
        identityService.updateIdentity(updateData);
        //开始修改关联关系
        if (request.getRelationList() != null && !request.getRelationList().isEmpty()) {
            identityService.updateIdentityRelationList(request.getIdentityId(), request.getRelationList(), Common.getAppChannelId(request));
        }
        //清除用户缓存
        userService.clearUserCache(userInfoDAO.getUserId());
        //清除身份缓存
        identityService.clearIdentityCache(request.getIdentityId());
        return MsResponse.success(identityService.createIdentityEntity(Common.getAppChannelId(request), request.getIdentityId()));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteIdentity")
    public MsResponse<IdentityDTO> deleteIdentity(@Valid @RequestBody DeleteIdentityRequest request) {
        IdentityInfoDAO identityInfoDAO = identityService.getIdentity(request.getIdentityId());
        if (identityInfoDAO == null) {
            return MsResponse.fail("50002", "身份不存在");
        }
        IdentityDTO identityDTO = identityService.createIdentityEntity(Common.getAppChannelId(request), identityInfoDAO.getIdentityId());
        //删除身份
        identityService.deleteIdentity(request.getIdentityId());
        //退出登录
        tokenService.logout(GetTokenCondition.builder().identityId(identityInfoDAO.getIdentityId()).build(), TokenConstant.tokenStatus.CHANGE_EVENT.getCode().toString());
        //清除用户缓存
        userService.clearUserCache(identityDTO.getUserId());
        //清除身份缓存
        identityService.clearIdentityCache(request.getIdentityId());
        return MsResponse.success(identityDTO);
    }

    @RequestMapping("/getIdentity")
    public MsResponse<IdentityDTO> getIdentity(@Valid @RequestBody GetIdentityRequest request) {
        IdentityInfoDAO identityInfoDAO = identityService.getIdentity(request.getIdentityId());
        if (identityInfoDAO == null) {
            return MsResponse.fail("50002", "身份不存在");
        }
        if (request.getIsGetDepartmentListSign().equals(1)) {
            // 获取当前身份下的部门列表
            GetIdentityDepartmentCondition condition = new GetIdentityDepartmentCondition();
            condition.setIdentityId(identityInfoDAO.getIdentityId());
            List<IdentityDepartmentRelationDAO> idrList = identityDepartmentService.getIDRList(condition);
            // 获取当前身份下的公司列表  身份id是企业隔离的
            GetIdentityCompanyCondition getIdentityCompanyCondition = new GetIdentityCompanyCondition();
            getIdentityCompanyCondition.setIdentityId(identityInfoDAO.getIdentityId());
            List<ICRWithIdentityDAO> list = identityCompanyService.getICRWithIdentityList(getIdentityCompanyCondition);
            // 收集部门ID
            List<String> departmentIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(idrList)) {
                departmentIds = idrList.stream().map(IdentityDepartmentRelationDAO::getDepartmentId).collect(Collectors.toList());
            }
            // 封装数据
            IdentityDTO build = IdentityDTO.builder()
                    .identityId(identityInfoDAO.getIdentityId())
                    .departmentIdList(departmentIds)
                    .companyId(!CollectionUtils.isEmpty(list) ? list.get(0).getCompanyId() : null)
                    .build();
            return MsResponse.success(build);
        }

        return MsResponse.success(identityService.getIdentityEntityFromCache(Common.getAppChannelId(request), request.getIdentityId()));
    }

    @RequestMapping("/getIdentityList")
    public MsResponse<DataListDTO<IdentityDTO>> getIdentityList(@RequestBody GetIdentityListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), IdentityConstant.IDENTITY_MAIN_COLUMN);
        //构造查询条件
        GetIdentityCondition condition = JSONUtil.parseObj(conditionMap).toBean(GetIdentityCondition.class);
//        GetIdentityCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetIdentityCondition.class);
        //开始查询
        int dataCount = identityService.getIdentityCount(condition);
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<IdentityDTO>builder().build());
        }
        List<IdentityDTO> identityDTOList = identityService.getIdentityEntityFromCache(
                Common.getAppChannelId(request),
                identityService.getIdentityList(condition, (page - 1) * limit, limit).stream().map(IdentityDTO::getIdentityId).collect(Collectors.toList())
        );
//        identityDTOList = identityService.createIdentityEntity(
//                identityService.getIdentityList(condition, (page - 1) * limit, limit)
//                        .stream()
//                        .map(IdentityDTO::getIdentityId)
//                        .collect(Collectors.toList()));
        //开始过滤字段
        if (request.getIdentityKeyFilter() != null) {
            identityDTOList = identityService.doIdentityKeyFilter(identityDTOList, request.getIdentityKeyFilter());
        }
        return MsResponse.success(DataListDTO.<IdentityDTO>builder().dataCount(dataCount).dataList(identityDTOList).build());
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/changeIdentity")
    public MsResponse<UserDTO> changeIdentity(@Valid @RequestBody ChangeIdentityRequest request) {
        String appChannelId = Common.getAppChannelId(request);
        String dataChannelId = Common.getDataChannelId(request);
        UserInfoDAO userInfoDAO = userService.getUserByIdentityId(request.getIdentityId());
        if (userInfoDAO == null) {
            return MsResponse.fail("50004", "用户不存在");
        }
        IdentityInfoDAO identityInfoDAO = identityService.getIdentity(request.getIdentityId());
        if (identityInfoDAO == null) {
            return MsResponse.fail("50005", "身份不存在");
        }
        //生成新token
        TokenInfoDAO newToken = TokenInfoDAO.builder().token(StringUtil.randomString(32) + (new Date().getTime() / 1000)).identityId(request.getIdentityId()).userId(userInfoDAO.getUserId()).ip(Common.getIp()).endTimestamp(Long.valueOf((new Date().getTime() / 1000) + TokenConstant.USER_LOGIN_EXPIRE_MILLISECONDS)).appChannelId(appChannelId).dataChannelId(dataChannelId).createTime(new Date()).updateTime(new Date()).build();
        tokenService.addToken(newToken);
        //生成用户实体
        UserDTO userDTO = userService.createUserEntity(appChannelId, userInfoDAO.getUserId());
        //存入缓存
        RedisUtil.set(SystemConstant.REDIS_KEY_USER_TOKEN + newToken.getToken(), StringUtil.jsonEncode(userDTO), TokenConstant.USER_LOGIN_EXPIRE);
        //清除用户缓存
        userService.clearUserCache(userInfoDAO.getUserId());
        //清除身份缓存
        identityService.clearIdentityCache(request.getIdentityId());
        return MsResponse.success(userDTO);
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addIdentityRelation")
    public MsResponse<Map<String, Object>> addIdentityRelation(@Valid @RequestBody AddIdentityRelationRequest request) {
        IdentityInfoDAO identityInfoDAO = identityService.getIdentity(request.getIdentityId());
        if (identityInfoDAO == null) {
            return MsResponse.fail("50001", "身份不存在");
        }
        //添加关系
        identityService.addIdentityRelationList(identityInfoDAO.getIdentityId(), request.getRelationList(), Common.getAppChannelId(request));
        //清除用户缓存
        userService.clearUserCache(identityInfoDAO.getUserId());
        //清除身份缓存
        identityService.clearIdentityCache(request.getIdentityId());
        return MsResponse.success(new HashMap<>() {{
            put("relationList", request.getRelationList());
            put("identityId", request.getIdentityId());
            put("userId", identityInfoDAO.getUserId());
        }});
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateIdentityRelation")
    public MsResponse<Map<String, Object>> updateIdentityRelation(@RequestBody @Valid UpdateIdentityRelationRequest request) {
        IdentityInfoDAO identityInfoDAO = identityService.getIdentity(request.getIdentityId());
        if (identityInfoDAO == null) {
            return MsResponse.fail("50002", "身份不存在");
        }
        //修改关系
        identityService.updateIdentityRelationList(request.getIdentityId(), request.getRelationList(), Common.getAppChannelId(request));
        //清除用户缓存
        userService.clearUserCache(identityInfoDAO.getUserId());
        //清除身份缓存
        identityService.clearIdentityCache(request.getIdentityId());
        return MsResponse.success(new HashMap<>() {{
            put("relationList", request.getRelationList());
            put("identityId", request.getIdentityId());
            put("userId", identityInfoDAO.getUserId());
        }});
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteIdentityRelation")
    public MsResponse<Map<String, Object>> deleteIdentityRelation(@RequestBody @Valid DeleteIdentityRelationRequest request) {
        IdentityInfoDAO identityInfoDAO = identityService.getIdentity(request.getIdentityId());
        if (identityInfoDAO == null) {
            return MsResponse.fail("50002", "身份不存在");
        }
        //删除关系
        identityService.deleteIdentityRelationList(request.getIdentityId(), request.getRelationList());
        //清除用户缓存
        userService.clearUserCache(identityInfoDAO.getUserId());
        //清除身份缓存
        identityService.clearIdentityCache(request.getIdentityId());
        return MsResponse.success(new HashMap<>() {{
            put("relationList", StringUtil.jsonEncode(request.getRelationList()));
            put("identityId", request.getIdentityId());
            put("userId", identityInfoDAO.getUserId());
        }});
    }

    @RequestMapping("/getCompanyIdentityList")
    public MsResponse<DataListDTO<IdentityDTO>> getCompanyIdentityList(@RequestBody @Valid GetCompanyIdentityListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        int dataCount = 0;
        //符合条件的身份id列表
        List<String> identityIdList = new ArrayList<>();
        //查询条件map
        Map<String, Object> conditionMap = new HashMap<>() {{
            put("dataChannelId", Common.getDataChannelId(request));
        }};
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), IdentityConstant.IDENTITY_COMPANY_MAIN_COLUMN);
        //构造查询条件
        if (!StringUtil.isEmpty(request.getCompanyId())) {
            conditionMap.put("companyId", request.getCompanyId());
        }
        if (request.getCompanyIdList() != null) {
            conditionMap.put("companyIdList", request.getCompanyIdList());
        }
        if (!StringUtil.isEmpty(request.getDepartmentId())) {
            conditionMap.put("departmentId", request.getDepartmentId());
        }
        if (request.getDepartmentIdList() != null) {
            conditionMap.put("departmentIdList", request.getDepartmentIdList());
        }
        if (!StringUtil.isEmpty(request.getPositionId())) {
            conditionMap.put("positionId", request.getPositionId());
        }
        if (request.getPositionIdList() != null) {
            conditionMap.put("positionIdList", request.getPositionIdList());
        }
        if (conditionMap.get("positionId") != null || conditionMap.get("positionIdList") != null) {
            //构造查询条件
            GetIdentityPositionCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetIdentityPositionCondition.class);
            if (conditionMap.get("positionId") != null) {
                condition.setPositionId(conditionMap.get("positionId").toString());
            }
            if (conditionMap.get("positionIdList") != null) {
                condition.setPositionIdList(StringUtil.getListFromString(StringUtil.jsonEncode(conditionMap.get("positionIdList")), String.class));
            }
            dataCount = identityPositionService.getIPRWithIdentityCount(condition);
            if (dataCount == 0) {
                return MsResponse.success(DataListDTO.<IdentityDTO>builder().build());
            }
            List<IPRWithIdentityDAO> idrWithIdentityDAOList = identityPositionService.getIPRWithIdentityList(condition, (page - 1) * limit, limit);
            identityIdList = idrWithIdentityDAOList.stream().map(IPRWithIdentityDAO::getIdentityId).collect(Collectors.toList());
        } else if (conditionMap.get("departmentId") != null || conditionMap.get("departmentIdList") != null) {
            //构造查询条件
            GetIdentityDepartmentCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetIdentityDepartmentCondition.class);
            if (conditionMap.get("departmentId") != null) {
                condition.setDepartmentId(conditionMap.get("departmentId").toString());
            }
            if (conditionMap.get("departmentIdList") != null) {
                condition.setDepartmentIdList(StringUtil.getListFromString(StringUtil.jsonEncode(conditionMap.get("departmentIdList")), String.class));
            }
            dataCount = identityDepartmentService.getIDRWithIdentityCount(condition);
            if (dataCount == 0) {
                return MsResponse.success(DataListDTO.<IdentityDTO>builder().build());
            }
            List<IDRWithIdentityDAO> idrWithIdentityDAOList = identityDepartmentService.getIDRWithIdentityList(condition, (page - 1) * limit, limit);
            identityIdList = idrWithIdentityDAOList.stream().map(IDRWithIdentityDAO::getIdentityId).collect(Collectors.toList());
        } else if (conditionMap.get("companyId") != null || conditionMap.get("companyIdList") != null) {
            //构造查询条件
            GetIdentityCompanyCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetIdentityCompanyCondition.class);
            if (conditionMap.get("companyId") != null) {
                condition.setCompanyId(conditionMap.get("companyId").toString());
            }
            if (conditionMap.get("companyIdList") != null) {
                condition.setCompanyIdList(StringUtil.getListFromString(StringUtil.jsonEncode(conditionMap.get("companyIdList")), String.class));
            }
            dataCount = identityCompanyService.getICRWithIdentityCount(condition);
            if (dataCount == 0) {
                return MsResponse.success(DataListDTO.<IdentityDTO>builder().build());
            }
            List<ICRWithIdentityDAO> icrWithIdentityDAOList = identityCompanyService.getICRWithIdentityList(condition, (page - 1) * limit, limit);
            identityIdList = icrWithIdentityDAOList.stream().map(ICRWithIdentityDAO::getIdentityId).collect(Collectors.toList());
        }
        List<IdentityDTO> identityDTOList = new ArrayList<>();
        if (request.getIsSimple() > 0) {
            identityDTOList = identityService.createSimpleIdentityEntity(identityIdList);
        } else {
            identityDTOList = identityService.getIdentityEntityFromCache(Common.getAppChannelId(request), identityIdList);
        }
        //开始过滤字段
        if (request.getIdentityKeyFilter() != null) {
            identityDTOList = identityService.doIdentityKeyFilter(identityDTOList, request.getIdentityKeyFilter());
        }
        return MsResponse.success(DataListDTO.<IdentityDTO>builder()
                .dataCount(dataCount)
                .dataList(identityDTOList)
                .build());
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateIdentityList")
    public MsResponse<Map<String, Object>> updateIdentityList(@RequestBody UpdateIdentityListRequest updateIdentityListRequest) {
        if (updateIdentityListRequest.getIdentityList() == null || updateIdentityListRequest.getIdentityList().isEmpty()) {
            return MsResponse.fail("50001", "请求参数错误");
        }
        List<String> updateIdentityIdList = new ArrayList<>();
        List<String> identityIdList = updateIdentityListRequest.getIdentityList().stream().map(UpdateIdentityRequest::getIdentityId).collect(Collectors.toList());
        Map<String, UserInfoDAO> userMap = userService.getUserList(GetUserCondition.builder()
                .identityIdList(identityIdList)
                .build()).stream().collect(Collectors.toMap(UserInfoDAO::getUserId, dao -> dao, (dao1, dao2) -> dao2));
        Map<String, IdentityDTO> identityMap = identityService.getIdentityList(GetIdentityCondition.builder()
                .identityIdList(identityIdList)
                .build()).stream().collect(Collectors.toMap(IdentityDTO::getIdentityId, dto -> dto, (dto1, dto2) -> dto2));
        for (UpdateIdentityRequest request : updateIdentityListRequest.getIdentityList()) {
            IdentityDTO identityDTO = identityMap.get(request.getIdentityId());
            if (identityDTO == null) {
                throw new IdentityException("50003", "身份不存在");
            }
            UserInfoDAO userInfoDAO = userMap.get(identityDTO.getUserId());
            if (userInfoDAO == null) {
                throw new IdentityException("50002", "用户不存在");
            }
            //修改身份信息
            IdentityInfoDAO updateData = IdentityInfoDAO.builder()
                    .identityId(identityDTO.getIdentityId())
                    .identityName(request.getIdentityName())
                    .sort(request.getSort())
                    .build();
            if (request.getExtraContent() != null) {
                updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
            }
            identityService.updateIdentity(updateData);
            //开始修改关联关系
            if (request.getRelationList() != null && !request.getRelationList().isEmpty()) {
                identityService.updateIdentityRelationList(identityDTO.getIdentityId(), request.getRelationList(), Common.getAppChannelId(request));
            }
            //清除用户缓存
            userService.clearUserCache(userInfoDAO.getUserId());
            //清除身份缓存
            identityService.clearIdentityCache(identityDTO.getIdentityId());
            //已更新的身份id
            updateIdentityIdList.add(identityDTO.getIdentityId());
        }
        return MsResponse.success(new HashMap<>() {{
            put("identityIds", String.join(",", updateIdentityIdList));
            put("identityList", identityService.createIdentityEntity(Common.getAppChannelId(updateIdentityListRequest), updateIdentityIdList));
        }});
    }

    /**
     * 批量添加用户身份
     */
    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addIdentityList")
    public MsResponse<Map<String, Object>> addIdentityList(@RequestBody AddIdentityListRequest addIdentityListRequest) {
        if (addIdentityListRequest.getIdentityList() == null || addIdentityListRequest.getIdentityList().isEmpty()) {
            return MsResponse.fail("50001", "请求参数错误");
        }
        List<String> addIdentityIdList = new ArrayList<>();
        for (AddIdentityRequest request : addIdentityListRequest.getIdentityList()) {
            if (StringUtil.isEmpty(request.getUserId()) && StringUtil.isEmpty(request.getIdentityId())) {
                return MsResponse.fail("50001", "用户id或身份id错误");
            }
            UserInfoDAO userInfoDAO;
            if (!StringUtil.isEmpty(request.getUserId())) {
                userInfoDAO = userService.getUser(GetUserCondition.builder().userId(request.getUserId()).build());
            } else {
                userInfoDAO = userService.getUserByIdentityId(request.getIdentityId());
            }
            if (userInfoDAO == null) {
                throw new IdentityException("50004", "用户不存在");
            }
            //添加身份
            IdentityInfoDAO identityInfoDAO = identityService.addIdentity(userInfoDAO.getUserId(), request.getNewIdentityId(), request.getIdentityName(), request.getExtraContent(), Common.getAppChannelId(request), Common.getDataChannelId(request));
            //添加关联关系
            if (request.getRelationList() != null && !request.getRelationList().isEmpty()) {
                identityService.addIdentityRelationList(identityInfoDAO.getIdentityId(), request.getRelationList(), Common.getAppChannelId(request));
            }
            //清除用户缓存
            userService.clearUserCache(userInfoDAO.getUserId());
            //清除身份缓存
            if (!StringUtil.isEmpty(request.getIdentityId())) {
                //清除身份缓存
                identityService.clearIdentityCache(request.getIdentityId());
            }
            // 已添加的身份id
            addIdentityIdList.add(identityInfoDAO.getIdentityId());
        }
        return MsResponse.success(new HashMap<>() {{
            put("identityIds", String.join(",", addIdentityIdList));
            put("identityList", identityService.createIdentityEntity(Common.getAppChannelId(addIdentityListRequest), addIdentityIdList));
        }});
    }


    /**
     * 批量删除用户身份
     */
    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteIdentityList")
    public MsResponse<Map<String, Object>> deleteIdentityList(@Valid @RequestBody DeleteIdentityListRequest request) {
        List<IdentityDTO> identityList = identityService.getIdentityList(GetIdentityCondition.builder().identityIdList(request.getIdentityIdList()).build());
        if (CollectionUtils.isEmpty(identityList)) {
            return MsResponse.fail("50002", "身份不存在");
        }
        Map<String, IdentityDTO> identityMap = identityList.stream().collect(Collectors.toMap(IdentityDTO::getIdentityId, v -> v));
        for (String identityId : request.getIdentityIdList()) {
            if (!identityMap.containsKey(identityId) || identityMap.get(identityId) == null) {
                return MsResponse.fail("50002", "身份不存在");
            }
        }
        List<IdentityDTO> identityDTOList = identityService.createIdentityEntity(Common.getAppChannelId(request), request.getIdentityIdList());
        // 删除身份
        identityService.deleteIdentity(request.getIdentityIdList());
        // 退出登录
        tokenService.logout(GetTokenCondition.builder().identityIdList(request.getIdentityIdList()).build(), TokenConstant.tokenStatus.CHANGE_EVENT.getCode().toString());
        //清除用户缓存
        userService.clearUserCache(identityDTOList.stream().map(IdentityDTO::getUserId).collect(Collectors.toList()));
        // 清除身份缓存
        identityService.clearIdentityCache(request.getIdentityIdList());

        return MsResponse.success(new HashMap<>() {{
            put("identityIds", String.join(",", request.getIdentityIdList()));
            put("identityList", identityDTOList);
        }});
    }

    /**
     * 获取公司下所有的身份信息
     *
     * @param request
     * @return
     */
    @OptLog
    @RequestMapping("/getIdentityListByCompanyId")
    public MsResponse<List<IdentityByCompanyDTO>> getIdentityListByCompanyId(@RequestBody GetCompanyRequest request) {
        if (Objects.isNull(request)) {
            return MsResponse.fail("50002", "请求身份不能为空");
        }
        if (StrUtil.isEmpty(request.getCompanyId())) {
            return MsResponse.fail("50002", "公司ID不能为空");
        }
        List<IdentityByCompanyDTO> identityListByCompanyList = identityService.getIdentityListByCompanyId(request.getCompanyId());
        return MsResponse.success(identityListByCompanyList);
    }


}
