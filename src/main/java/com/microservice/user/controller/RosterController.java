package com.microservice.user.controller;

import cn.hutool.core.util.StrUtil;
import com.microservice.user.common.Common;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.roster.RosterAddListDTO;
import com.microservice.user.entity.dto.ms.roster.RosterIdDTO;
import com.microservice.user.entity.dto.ms.roster.RosterQueeryDTO;
import com.microservice.user.entity.dto.ms.roster.RosterUpdateDTO;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.entity.vo.RosterVO;
import com.microservice.user.service.RosterService;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * &#064;DATE: 2024/9/6
 * &#064;AUTHOR: XSL
 *
 */
@RestController
@RequestMapping("/roster")
public class RosterController {

    @Autowired
    private RosterService rosterService;

    /**
     * 分页查询花名册
     * @param rosterQueeryDTO
     * @return
     */
    @PostMapping("/list")
    public MsResponse<DataListDTO<RosterVO>> getRosterList(@RequestBody(required = false) RosterQueeryDTO rosterQueeryDTO) {
        if (Objects.isNull(rosterQueeryDTO)) {
            rosterQueeryDTO = RosterQueeryDTO.builder().build();
        }
        DataListDTO<RosterVO> allByRosterDAO = rosterService.findAllByRosterDAO(rosterQueeryDTO);
        return MsResponse.success(allByRosterDAO);
    }

    /**
     * 获取详情
     * @param rosterIdDTO
     * @return
     */
    @PostMapping("/info")
    public MsResponse<RosterVO> rosterInfo(@RequestBody(required = false) RosterIdDTO rosterIdDTO) {
        if (Objects.isNull(rosterIdDTO)) {
            return MsResponse.fail("50000", "请求参数不能为空");
        }
        if (StrUtil.isEmpty(rosterIdDTO.getIdentityId())) {
            return MsResponse.fail("50000", "ID不能为空");
        }
        RosterVO rosterVO = rosterService.findByIdentityId(rosterIdDTO.getIdentityId());
        return MsResponse.success(rosterVO);
    }

    /**
     * 批量新增花名册
     * @param rosterAddDTOList
     * @return
     */
    @PostMapping("/add")
    public MsResponse<?> addRoster(@RequestBody(required = false) RosterAddListDTO rosterAddDTOList) {
        if (Objects.isNull(rosterAddDTOList)) {
            return MsResponse.fail("50000", "请求参数不能为空");
        }
        if (StringUtil.isEmpty(rosterAddDTOList.getCompanyId())) {
            return MsResponse.fail("50000", "公司ID不能为空");
        }
        if (StringUtil.isEmpty(rosterAddDTOList.getAppChannelId())) {
            rosterAddDTOList.setAppChannelId(Common.getAppChannelId());
        }
        if (StringUtil.isEmpty(rosterAddDTOList.getDataChannelId())) {
            rosterAddDTOList.setDataChannelId(Common.getDataChannelId());
        }
        rosterService.addRosterDAO(rosterAddDTOList);
        return MsResponse.success();
    }

    /**
     * 更新花名册
     * @param rosterList
     * @return
     */
    @PostMapping("/update")
    public MsResponse<?> updateRoster(@RequestBody(required = false) List<RosterUpdateDTO> rosterList) {
        if (Objects.isNull(rosterList) || rosterList.isEmpty()) {
            return MsResponse.fail("50000", "请求参数不能为空");
        }
        rosterService.updateRosterDAO(rosterList);
        return MsResponse.success();
    }

    /**
     * 删除一条数据
     * @param rosterIdDTO
     * @return
     */
    @PostMapping("/delete")
    public MsResponse<?> deleteRoster(@RequestBody(required = false) RosterIdDTO rosterIdDTO) {
        if (Objects.isNull(rosterIdDTO)) {
            return MsResponse.fail("50000", "请求参数不能为空");
        }
        if (StrUtil.isEmpty(rosterIdDTO.getIdentityId())) {
            return MsResponse.fail("50000", "ID不能为空");
        }
        int i = rosterService.deleteRosterDAO(rosterIdDTO.getIdentityId());
        return MsResponse.success();
    }


}
