package com.microservice.user.controller;

import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.SystemConstant;
import com.microservice.user.constant.TokenConstant;
import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.IdentityDTO;
import com.microservice.user.entity.dto.ms.TokenDTO;
import com.microservice.user.entity.dto.ms.UserDTO;
import com.microservice.user.entity.request.ms.common.*;
import com.microservice.user.entity.request.ms.token.GetTokenListRequest;
import com.microservice.user.entity.response.ms.CheckAuthResponse;
import com.microservice.user.entity.response.ms.CheckEventResponse;
import com.microservice.user.entity.response.ms.CheckTokenResponse;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.*;
import com.microservice.user.util.RedisUtil;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/common")
public class CommonController {

    @Autowired
    private UserService userService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private GroupEventService groupEventService;
    @Autowired
    private GroupPageService groupPageService;
    @Autowired
    private GroupDataService groupDataService;

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/login")
    public MsResponse<UserDTO> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpServletRequest) {
        if (request.getIsCheckPassword() > 0 && StringUtil.isEmpty(request.getPassword())) {
            return MsResponse.fail("50002", "密码错误");
        }
        String appChannelId = Common.getAppChannelId(request);
        String dataChannelId = Common.getDataChannelId(request);
        //验证用户是否存在
        UserInfoDAO userInfoDAO = userService.getUser(GetUserCondition.builder().dataChannelId(dataChannelId).userName(request.getUserName()).build());
        if (userInfoDAO == null) {
            if (request.getIsCreateUser() <= 0) {
                return MsResponse.fail("50003", "用户不存在");
            }
            //isCreateUser = 1 时，如果用户不存在新建用户
            request.setPassword(StringUtil.md5(StringUtil.createRandomString(8)));
            UserDTO newUserDTO = userService.initUser(request.getUserName(), request.getPassword(), null, appChannelId, dataChannelId);
            userInfoDAO = userService.getUser(GetUserCondition.builder().dataChannelId(dataChannelId).userId(newUserDTO.getUserId()).build());
        }
        //验证密码
        if (request.getIsCheckPassword() > 0) {
            String password = "";
            if (request.getPassword().length() == 32) {
                password = request.getPassword();
            } else {
                password = StringUtil.md5(request.getPassword());
            }
            if (!StringUtil.myMd5(password).equals(userInfoDAO.getPassword())) {
                return MsResponse.fail("50004", "密码错误");
            }
        }
        //验证身份
        TokenInfoDAO newToken = TokenInfoDAO.builder()
                .token(StringUtil.randomString(32) + (new Date().getTime() / 1000))
                .userId(userInfoDAO.getUserId())
                .ip(httpServletRequest.getRemoteAddr())
                .endTimestamp((new Date().getTime() / 1000) + TokenConstant.USER_LOGIN_EXPIRE_MILLISECONDS)
                .appChannelId(Common.getAppChannelId(request))
                .dataChannelId(dataChannelId)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        TokenInfoDAO oldToken = tokenService.getToken(GetTokenCondition.builder()
                .userId(userInfoDAO.getUserId())
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build());
        String tokenIdentityId = "";
        if (oldToken != null) {//如果有上次登录token，使用上次登录token的身份
            //验证identity是否存在
            IdentityInfoDAO oldIdentity = identityService.getIdentity(GetIdentityCondition.builder()
                    .userId(userInfoDAO.getUserId())
                    .identityId(oldToken.getIdentityId())
                    .build());
            if (oldIdentity != null) {
                tokenIdentityId = oldIdentity.getIdentityId();
            }
        }
        if (StringUtil.isEmpty(tokenIdentityId)) {//没有上次登录token取第一个身份
            List<IdentityDTO> identityDTOList = identityService.getIdentityList(GetIdentityCondition.builder()
                    .userId(userInfoDAO.getUserId())
                    .build());
            tokenIdentityId = identityDTOList.get(0).getIdentityId();
        }
        newToken.setIdentityId(tokenIdentityId);
        tokenService.addToken(newToken);
        //生成用户实体
        UserDTO userDTO = userService.createUserEntity(appChannelId, userInfoDAO.getUserId());
        //存入缓存
        RedisUtil.set(SystemConstant.REDIS_KEY_USER_TOKEN + newToken.getToken(), StringUtil.jsonEncode(userDTO), TokenConstant.USER_LOGIN_EXPIRE);
        return MsResponse.success(userDTO);
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/logout")
    public MsResponse<Map<String, Object>> logout(@Valid @RequestBody LogoutRequest request) {
        List<String> tokenList = Optional.ofNullable(request.getTokenList()).map(list -> list.stream().filter(token -> !token.isEmpty()).collect(Collectors.toList())).orElse(new ArrayList<>());
        List<String> userIdList = Optional.ofNullable(request.getUserIdList()).map(list -> list.stream().filter(userId -> !userId.isEmpty()).collect(Collectors.toList())).orElse(new ArrayList<>());
        List<String> identityIdList = Optional.ofNullable(request.getIdentityIdList()).map(list -> list.stream().filter(identityId -> !identityId.isEmpty()).collect(Collectors.toList())).orElse(new ArrayList<>());
        if (tokenList.isEmpty() && userIdList.isEmpty() && identityIdList.isEmpty()) {
            return MsResponse.fail("50002", "参数错误");
        }
        tokenService.logout(GetTokenCondition.builder().tokenList(tokenList).userIdList(userIdList).identityIdList(identityIdList).build(), TokenConstant.tokenStatus.LOGOUT.getCode().toString());
        return MsResponse.success(new HashMap<>() {{
            put("tokenList", tokenList);
        }});
    }

    @RequestMapping("/checkToken")
    public MsResponse<CheckTokenResponse> checkToken(@Valid @RequestBody CheckTokenRequest request) {
        CheckTokenResponse checkTokenResponse = CheckTokenResponse.builder()
                .isAvailable(1)
                .build();
        boolean isRefresh = request.getIsRefresh() > 0;
        TokenInfoDAO tokenInfoDAO = null;
        String redisUserInfoStr = RedisUtil.get(SystemConstant.REDIS_KEY_USER_TOKEN + request.getToken());
        if (StringUtil.isEmpty(redisUserInfoStr)) {
            tokenInfoDAO = tokenService.checkToken(request.getToken());
            isRefresh = true;
        }
        //更新缓存
        if (isRefresh) {
            if (tokenInfoDAO == null) {
                tokenInfoDAO = tokenService.getToken(GetTokenCondition.builder().token(request.getToken()).build());
            }
            if (tokenInfoDAO == null) {
                return MsResponse.fail("50002", "token不存在");
            }
            UserDTO userDTO = userService.createUserEntity(Common.getAppChannelId(request), tokenInfoDAO.getUserId());
            //根据token数据确定isSelected
            userDTO.setToken(tokenInfoDAO.getToken());
            userDTO.setLastLoginIp(tokenInfoDAO.getIp());
            userDTO.setLastLoginTime(TimeUtil.date2String(tokenInfoDAO.getCreateTime()));
            boolean isSetSelected = false;
            if (!userDTO.getIdentityList().isEmpty()) {
                List<IdentityDTO> identityDTOList = userDTO.getIdentityList();
                for (IdentityDTO identityDTO : identityDTOList) {
                    if (identityDTO.getIdentityId().equals(tokenInfoDAO.getIdentityId())) {
                        userDTO.setIdentityId(identityDTO.getIdentityId());
                        isSetSelected = true;
                        identityDTO.setIsSelected(1);
                    } else {
                        identityDTO.setIsSelected(0);
                    }
                }
                if (!isSetSelected) {
                    IdentityDTO lastIdentity = identityDTOList.get(0);
                    lastIdentity.setIsSelected(1);
                    identityDTOList.set(0, lastIdentity);
                    userDTO.setIdentityId(lastIdentity.getIdentityId());
                }
                userDTO.setIdentityList(identityDTOList);
            }
            //重新生成token缓存
            redisUserInfoStr = StringUtil.jsonEncode(userDTO);
            RedisUtil.set(SystemConstant.REDIS_KEY_USER_TOKEN + request.getToken(), redisUserInfoStr, TokenConstant.USER_LOGIN_EXPIRE);
        }
        if (request.getIsSimple() <= 0) {
            UserDTO userDTO = StringUtil.jsonDecode(redisUserInfoStr, UserDTO.class);
            checkTokenResponse.setUser(userDTO);
        }
        return MsResponse.success(checkTokenResponse);
    }

    @RequestMapping("/checkEvent")
    public MsResponse<CheckEventResponse> checkEvent(@Valid @RequestBody CheckEventRequest request) {
        List<String> eventIdList = new ArrayList<>();
        if (!StringUtil.isEmpty(request.getIdentityId())) {
            List<GroupEventRelationDAO> gerList = groupEventService.getGERList(GetGroupEventCondition.builder().identityId(request.getIdentityId()).build());
            eventIdList = gerList.stream().map(GroupEventRelationDAO::getEventId).collect(Collectors.toList());
        }
        return MsResponse.success(
                CheckEventResponse.builder()
                        .eventId(request.getEventId())
                        .identityId(request.getIdentityId())
                        .isPass(eventIdList.contains(request.getEventId()) ? 1 : 0)
                        .build()
        );
    }

    @RequestMapping("/getTokenList")
    public MsResponse<DataListDTO<TokenDTO>> getTokenList(@RequestBody GetTokenListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        String appChannelId = Common.getAppChannelId(request);
        String dataChannelId = Common.getDataChannelId(request);
        GetTokenCondition condition = GetTokenCondition.builder()
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build();
        if (request.getTokenId() != null) {
            condition.setTokenId(request.getTokenId());
        }
        if (request.getIdentityId() != null) {
            condition.setIdentityId(request.getIdentityId());
        }
        if (request.getUserId() != null) {
            condition.setUserId(request.getUserId());
        }
        if (request.getToken() != null) {
            condition.setToken(request.getToken());
        }
        if (request.getIp() != null) {
            condition.setIp(request.getIp());
        }
        if (request.getTokenStatus() != null) {
            condition.setTokenStatus(request.getTokenStatus());
        }
        int dataCount = tokenService.getTokenCount(condition);
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<TokenDTO>builder().build());
        }
        return MsResponse.success(
                DataListDTO.<TokenDTO>builder()
                        .dataCount(dataCount)
                        .dataList(tokenService.getTokenList(condition, (page - 1) * limit, limit).stream().map(TokenDTO::create).collect(Collectors.toList()))
                        .build()
        );
    }

    /**
     * 验证权限（可同时验证事件和页面）
     *
     * @param request
     * @return
     */
    @RequestMapping("/checkAuth")
    public MsResponse<CheckAuthResponse> checkAuth(@Valid @RequestBody CheckAuthRequest request) {
        CheckAuthResponse response = CheckAuthResponse.builder()
                .eventId(request.getEventId())
                .pageId(request.getPageId())
                .data(request.getData())
                .identityId(request.getIdentityId())
                .build();
        if (!StringUtil.isEmpty(request.getEventId())) {
            List<GroupEventRelationDAO> gerList = groupEventService.getGERList(
                    GetGroupEventCondition.builder()
                            .identityId(request.getIdentityId())
                            .eventId(request.getEventId())
                            .build()
            );
            if (!gerList.isEmpty()) {
                response.setIsPassEvent(1);
            } else {
                response.setIsPassEvent(0);
            }
        }
        if (!StringUtil.isEmpty(request.getPageId())) {
            List<GroupPageRelationDAO> gprList = groupPageService.getGPRList(
                    GetGroupPageCondition.builder()
                            .identityId(request.getIdentityId())
                            .pageId(request.getPageId())
                            .build()
            );
            if (!gprList.isEmpty()) {
                response.setIsPassPage(1);
            } else {
                response.setIsPassPage(0);
            }
        }
        if (!StringUtil.isEmpty(request.getData())) {
            List<GroupDataRelationDAO> gdrList = groupDataService.getGDRList(
                    GetGroupDataCondition.builder()
                            .identityId(request.getIdentityId())
                            .data(request.getData())
                            .build()
            );
            if (!gdrList.isEmpty()) {
                response.setIsPassData(1);
            } else {
                response.setIsPassData(0);
            }
        }
        return MsResponse.success(response);
    }
}
