package com.microservice.user.controller;

import com.microservice.user.common.Common;
import com.microservice.user.constant.LogConstant;
import com.microservice.user.entity.condition.GetLogCondition;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.LogDTO;
import com.microservice.user.entity.request.ms.log.GetLogListRequest;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.LogService;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/log")
public class LogController {

    @Autowired
    private LogService logService;

    @RequestMapping("/getLogList")
    public MsResponse<DataListDTO<LogDTO>> getLogList(@RequestBody GetLogListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        String dataChannelId = Common.getDataChannelId(request);
        Map<String, Object> conditionMap = new HashMap<>() {{
            put("dataChannelId", dataChannelId);
        }};
        //isShowAll大于0时忽略渠道号
        if (request.getIsShowAll() > 0) {
            conditionMap.remove("dataChannelId");
        }
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), LogConstant.LOG_MAIN_COLUMN);
        //构造查询条件
        GetLogCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetLogCondition.class);
        //开始查询
        return MsResponse.success(
                DataListDTO.<LogDTO>builder()
                        .dataList(logService.getLogList(condition, (page - 1) * limit, limit)
                                .stream()
                                .map(LogDTO::create)
                                .collect(Collectors.toList()))
                        .dataCount(logService.getLogCount(condition))
                        .build()
        );
    }

}
