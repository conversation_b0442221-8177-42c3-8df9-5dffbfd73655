package com.microservice.user.controller;

import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.PositionConstant;
import com.microservice.user.entity.condition.GetIdentityPositionCondition;
import com.microservice.user.entity.condition.GetPositionCondition;
import com.microservice.user.entity.dao.IPRWithIdentityDAO;
import com.microservice.user.entity.dao.PositionInfoDAO;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.IdentityDTO;
import com.microservice.user.entity.dto.ms.PositionDTO;
import com.microservice.user.entity.request.ms.position.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.CompanyPositionService;
import com.microservice.user.service.DepartmentPositionService;
import com.microservice.user.service.IdentityPositionService;
import com.microservice.user.service.PositionService;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/position")
public class PositionController {

    @Autowired
    private PositionService positionService;
    @Autowired
    private CompanyPositionService companyPositionService;
    @Autowired
    private DepartmentPositionService departmentPositionService;
    @Autowired
    private IdentityPositionService identityPositionService;

    @RequestMapping("/getPositionList")
    public MsResponse<DataListDTO<PositionDTO>> getPositionList(@RequestBody GetPositionListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), PositionConstant.POSITION_MAIN_COLUMN);
        //构造查询条件
        GetPositionCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetPositionCondition.class);
        //开始查询
        return MsResponse.success(
                DataListDTO.<PositionDTO>builder()
                        .dataCount(positionService.getPositionCount(condition))
                        .dataList(positionService.getPositionList(condition, (page - 1) * limit, limit))
                        .build()
        );
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addPosition")
    public MsResponse<PositionDTO> addPosition(@Valid @RequestBody AddPositionRequest request) {
        //添加职位
        PositionInfoDAO positionInfoDAO = positionService.addPosition(request.getPositionName(), request.getParentId(), request.getExtraContent());
        //添加公司职位关系
        if (!StringUtil.isEmpty(request.getCompanyId())) {
            companyPositionService.addCompanyPositionRelation(request.getCompanyId(), positionInfoDAO.getPositionId());
        }
        //添加部门职位关系
        if (!StringUtil.isEmpty(request.getDepartmentId())) {
            departmentPositionService.addDepartmentPositionRelation(request.getDepartmentId(), positionInfoDAO.getPositionId());
        }
        return MsResponse.success(PositionDTO.create(positionInfoDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updatePosition")
    public MsResponse<PositionDTO> updatePosition(@Valid @RequestBody UpdatePositionRequest request) {
        PositionInfoDAO positionInfoDAO = positionService.getPosition(request.getPositionId());
        if (positionInfoDAO == null) {
            return MsResponse.fail("50002", "职位不存在");
        }
        PositionInfoDAO updateData = PositionInfoDAO.builder()
                .positionId(request.getPositionId())
                .build();
        if (request.getPositionName() != null) {
            updateData.setPositionName(request.getPositionName());
        }
        if (request.getParentId() != null) {
            updateData.setParentId(request.getParentId());
        }
        if (request.getExtraContent() != null) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        if (request.getSort() != null) {
            updateData.setSort(request.getSort());
        }
        positionService.updatePosition(updateData);
        BeanUtils.copyProperties(updateData, positionInfoDAO);
        //修改部门和职位关系
        if (request.getDepartmentId() != null) {
            departmentPositionService.updatePositionDepartmentRelation(request.getPositionId(), request.getDepartmentId());
        }
        return MsResponse.success(PositionDTO.create(positionInfoDAO));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deletePosition")
    public MsResponse<PositionDTO> deletePosition(@Valid @RequestBody DeletePositionRequest request) {
        return MsResponse.success(positionService.deletePosition(request.getPositionId(), request.getIsForce()));
    }

    @RequestMapping("/getPositionUserList")
    public MsResponse<DataListDTO<IdentityDTO>> getPositionUserList(@RequestBody GetPositionUserListRequest request) {
        if (StringUtil.isEmpty(request.getPositionId())
                && (request.getPositionIdList() == null || request.getPositionIdList().isEmpty())) {
            return MsResponse.fail("50001", "职位id错误 ");
        }
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        Set<String> positionIdSet = new HashSet<>();
        if (request.getPositionIdList() != null) {
            positionIdSet.addAll(request.getPositionIdList());
        }
        if (!StringUtil.isEmpty(request.getPositionId())) {
            positionIdSet.add(request.getPositionId());
        }
        positionIdSet = positionIdSet.stream().filter(positionId -> !StringUtil.isEmpty(positionId)).collect(Collectors.toSet());
        int dataCount = identityPositionService.getIPRCount(GetIdentityPositionCondition.builder().positionIdList(new ArrayList<>(positionIdSet)).build());
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<IdentityDTO>builder().build());
        }
        List<IdentityDTO> identityDTOList;
        if (request.getIsSimple() > 0) {
            List<IPRWithIdentityDAO> iprWithIdentityDAOList = identityPositionService.getIPRWithIdentityList(GetIdentityPositionCondition.builder()
                    .positionIdList(new ArrayList<>(positionIdSet))
                    .build());
            identityDTOList = iprWithIdentityDAOList.stream().map(IdentityDTO::create).collect(Collectors.toList());
        } else {
            identityDTOList = identityPositionService.getIPRList(GetIdentityPositionCondition.builder().positionIdList(new ArrayList<>(positionIdSet)).build(), (page - 1) * limit, limit)
                    .stream()
                    .map(idr -> IdentityDTO.builder()
                            .identityId(idr.getIdentityId())
                            .build())
                    .collect(Collectors.toList());
        }
        return MsResponse.success(
                DataListDTO.<IdentityDTO>builder()
                        .dataList(identityDTOList)
                        .dataCount(dataCount)
                        .build()
        );
    }
}
