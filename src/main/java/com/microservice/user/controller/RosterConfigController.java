package com.microservice.user.controller;

import com.microservice.user.common.Common;
import com.microservice.user.constant.RosterConfigConstant;
import com.microservice.user.entity.condition.GetRosterConfigCondition;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterConfigDateDTO;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterFieldDTO;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterGroupDTO;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterTeamDTO;
import com.microservice.user.entity.request.ms.rosterconfig.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.RosterConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 花名册配置
 *
 * <AUTHOR>
 * @time 2024-09-05
 */
@RestController
@RequestMapping ("/roster/config")
public class RosterConfigController {

    @Autowired
    private RosterConfigService rosterConfigService;

    /**
     * 获取花名册配置信息列表(关联出相应分组字段信息)
     */
    @PostMapping("/team/getRosterTeamList")
    public MsResponse<RosterConfigDateDTO<RosterTeamDTO>> getRosterTeamList(@RequestBody @Valid GetRosterTeamRequest request) {

        String appChannelId = request.getAppChannelId() != null ? request.appChannelId : Common.getAppChannelId();
        String dataChannelId = request.getDataChannelId() != null ? request.dataChannelId : Common.getDataChannelId();
        String companyId = request.getCompanyId();
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);

        GetRosterConfigCondition condition = GetRosterConfigCondition.builder()
                .companyId(companyId)
                .primaryKeyIds(request.getRosterTeamIds())
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .rosterFields(request.getFields())
                .name(request.getName())
                .isDelete(request.getIsDelete() != null ? request.getIsDelete() : RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                .isSystem(request.getIsSystem())
                .isShow(request.getIsShow())
                .startDateTime(request.getStartDateTime())
                .endDateTime(request.getEndDateTime()).build();

        int count = rosterConfigService.countRosterTeam(condition);
        if (count == 0) {
            return MsResponse.success(RosterConfigDateDTO.<RosterTeamDTO>builder().dataCount(0).dataList(new ArrayList<>()).build());
        }
        List<RosterTeamDTO> rosterTeamDTOList = rosterConfigService.getRosterTeamList(condition,request.getIsRelatedData(),page,limit);
        return MsResponse.success(RosterConfigDateDTO.<RosterTeamDTO>builder().dataList(rosterTeamDTOList).dataCount(count).build());
    }

    /**
     * 添加子集信息
     */
    @PostMapping("team/addRosterTeamInfo")
    public MsResponse addRosterTeamInfo(@RequestBody @Valid  SaveBatchRosterTeamRequest request) {
        rosterConfigService.saveBatchRosterTeamInfo(request);
        return MsResponse.success();
    }

    /**
     * 编辑子集信息
     */
    @PostMapping("team/updateRosterTeamInfo")
    public MsResponse updateRosterTeamInfo(@RequestBody List<UpdateRosterTeamRequest> rosterTeamRequestList) {
        rosterConfigService.updateBatchRosterTeamInfo(rosterTeamRequestList);
        return MsResponse.success();
    }

    /**
     * 删除子集信息
     */
    @PostMapping("team/delRosterTeamInfo")
    public MsResponse delRosterTeamInfo(@RequestBody(required = false) List<String> rosterTeamIdList) {
        if (rosterTeamIdList == null || rosterTeamIdList.isEmpty()) {
            return MsResponse.fail("50000", "请求参数不能为空");
        }
        rosterConfigService.delRosterTeamInfo(rosterTeamIdList);
        return MsResponse.success();
    }

    /**
     * 获取分组信息列表
     */
    @PostMapping("/group/getRosterGroupList")
    public MsResponse<RosterConfigDateDTO<RosterGroupDTO>> getRosterGroupList(@RequestBody @Valid GetRosterGroupRequest request) {

        String appChannelId = request.getAppChannelId() != null ? request.appChannelId : Common.getAppChannelId();
        String dataChannelId = request.getDataChannelId() != null ? request.dataChannelId : Common.getDataChannelId();
        String companyId = request.getCompanyId();
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);

        GetRosterConfigCondition condition = GetRosterConfigCondition.builder()
                .companyId(companyId)
                .primaryKeyIds(request.getRosterGroupIds())
                .rosterTeamIds(request.getRosterTramIds())
                .rosterFields(request.getFields())
                .appChannelId(appChannelId)
                .isDelete(request.getIsDelete() != null ? request.getIsDelete() : RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                .dataChannelId(dataChannelId)
                .name(request.getName())
                .isShow(request.getIsShow())
                .isSystem(request.getIsSystem())
                .startDateTime(request.getStartDateTime())
                .endDateTime(request.getEndDateTime()).build();

        int count = rosterConfigService.countRosterGroup(condition);
        if (count == 0) {
            return MsResponse.success(RosterConfigDateDTO.<RosterGroupDTO>builder().dataCount(0).dataList(new ArrayList<>()).build());
        }
        List<RosterGroupDTO> rosterTeamDTOList = rosterConfigService.getRosterGroupList(condition,request.getIsRelatedData(),page,limit);
        return MsResponse.success(RosterConfigDateDTO.<RosterGroupDTO>builder().dataList(rosterTeamDTOList).dataCount(count).build());
    }

    /**
     * 添加分组信息
     */
    @PostMapping("group/addRosterGroupInfo")
    public MsResponse addRosterGroupInfo(@RequestBody @Valid SaveBatchRosterGroupRequest request) {
        rosterConfigService.saveBatchRosterGroupInfo(request);
        return MsResponse.success();
    }

    /**
     * 编辑分组信息
     */
    @PostMapping("group/updateRosterGroupInfo")
    public MsResponse<RosterGroupDTO> updateRosterGroupInfo(@RequestBody List<UpdateRosterGroupRequest> updateRosterGroupRequestList) {
        rosterConfigService.updateBatchRosterGroupInfo(updateRosterGroupRequestList);
        return MsResponse.success();
    }

    /**
     * 删除分组信息
     */
    @PostMapping("group/delRosterGroupInfo")
    public MsResponse delRosterGroupInfo(@RequestBody(required = false) List<String> rosterGroupIdList) {
        if (rosterGroupIdList == null || rosterGroupIdList.isEmpty()) {
            return MsResponse.fail("50000", "请求参数不能为空");
        }
        rosterConfigService.delRosterGroupInfo(rosterGroupIdList);
        return MsResponse.success();
    }

    /**
     * 获取字段列表
     */
    @PostMapping("/field/getRosterFieldList")
    public MsResponse<RosterConfigDateDTO<RosterFieldDTO>> getRosterFieldList(@RequestBody GetRosterFieldRequest request) {

        String appChannelId = request.getAppChannelId() != null ? request.appChannelId : Common.getAppChannelId();
        String dataChannelId = request.getDataChannelId() != null ? request.dataChannelId : Common.getDataChannelId();
        String companyId = request.getCompanyId();
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);

        GetRosterConfigCondition condition = GetRosterConfigCondition.builder()
                .companyId(companyId)
                .primaryKeyIds(request.getRosterFields())
                .rosterTeamIds(request.getRosterTramIds())
                .rosterFields(request.getFields())
                .isSystem(request.getIsSystem())
                .isDelete(request.getIsDelete() != null ? request.getIsDelete() : RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                .rosterGroupIds(request.getRosterGroupIds())
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .name(request.getName())
                .startDateTime(request.getStartDateTime())
                .endDateTime(request.getEndDateTime())
                .isShow(request.getIsShow()).build();

        int count = rosterConfigService.countRosterField(condition);
        if (count == 0) {
            return MsResponse.success(RosterConfigDateDTO.<RosterFieldDTO>builder().dataCount(count).dataList(new ArrayList<>()).build());
        }
        List<RosterFieldDTO> rosterFieldDTOList = rosterConfigService.getRosterFieldList(condition,page,limit);
        return MsResponse.success(RosterConfigDateDTO.<RosterFieldDTO>builder().dataCount(count).dataList(rosterFieldDTOList).build());
    }


    /**
     * 添加字段信息
     */
    @PostMapping("field/addRosterFieldInfo")
    public MsResponse addRosterFieldInfo(@RequestBody @Valid SaveBatchRosterFieldRequest request) {
        rosterConfigService.addBatchRosterFieldInfo(request);
        return MsResponse.success();
    }

    /**
     * 编辑字段信息
     */
    @PostMapping("field/updateRosterFieldInfo")
    public MsResponse updateRosterFieldInfo(@RequestBody List<UpdateRosterFieldRequest> fieldRequestList) {
        rosterConfigService.updateRosterFieldInfo(fieldRequestList);
        return MsResponse.success();
    }

    /**
     * 删除字段信息
     */
    @PostMapping("field/delRosterFieldInfo")
    public MsResponse delRosterFieldInfo(@RequestBody(required = false) List<String> rosterFieldIdList) {
        if (rosterFieldIdList == null || rosterFieldIdList.isEmpty()) {
            return MsResponse.fail("50000", "请求参数不能为空");
        }
        rosterConfigService.delRosterFieldInfo(rosterFieldIdList);
        return MsResponse.success();
    }
}
