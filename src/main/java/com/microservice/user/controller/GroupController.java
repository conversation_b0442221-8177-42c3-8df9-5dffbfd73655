package com.microservice.user.controller;

import com.microservice.user.annotation.OptLog;
import com.microservice.user.common.Common;
import com.microservice.user.constant.GroupConstant;
import com.microservice.user.entity.condition.GetGroupCondition;
import com.microservice.user.entity.condition.GetGroupEventCondition;
import com.microservice.user.entity.condition.GetGroupPageCondition;
import com.microservice.user.entity.condition.GetIdentityGroupCondition;
import com.microservice.user.entity.dao.GroupEventRelationDAO;
import com.microservice.user.entity.dao.GroupInfoDAO;
import com.microservice.user.entity.dao.GroupPageRelationDAO;
import com.microservice.user.entity.dao.IGRWithIdentityDAO;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.GroupDTO;
import com.microservice.user.entity.request.ms.group.*;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.service.*;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/group")
public class GroupController {

    @Autowired
    private GroupService groupService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private IdentityGroupService identityGroupService;
    @Autowired
    private GroupEventService groupEventService;
    @Autowired
    private GroupPageService groupPageService;
    @Autowired
    private GroupDataService groupDataService;
    @Autowired
    private UserService userService;
    @Autowired
    private IdentityService identityService;

    @RequestMapping("/getGroupList")
    public MsResponse<DataListDTO<GroupDTO>> getGroupList(@RequestBody GetGroupListRequest request) {
        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getLimit(), 0);
        String dataChannelId = Common.getDataChannelId(request);
        Map<String, Object> conditionMap = new HashMap<>() {{
            put("dataChannelId", dataChannelId);
        }};
        //isShowAll大于0时忽略渠道号
        if (request.getIsShowAll() > 0) {
            conditionMap.remove("dataChannelId");
        }
        conditionMap = Common.createRequestConditionMap(conditionMap, request.getCondition(), GroupConstant.GROUP_MAIN_COLUMN);
        //构造查询条件
        GetGroupCondition condition = StringUtil.jsonDecode(StringUtil.jsonEncode(conditionMap), GetGroupCondition.class);
        int dataCount = groupService.getGroupCount(condition);
        if (dataCount == 0) {
            return MsResponse.success(DataListDTO.<GroupDTO>builder().build());
        }
        //开始查询
        List<GroupInfoDAO> groupInfoDAOList = groupService.getGroupList(condition, (page - 1) * limit, limit);
        List<String> groupIdList = groupInfoDAOList.stream().map(GroupInfoDAO::getGroupId).collect(Collectors.toList());
        //查询用户组内成员数量
        Map<String, Integer> groupId2IGRCountMap = identityGroupService.getGroupId2IdentityCountMap(groupIdList);
        Map<String, List<String>> groupId2IdentityIdListMap = new HashMap<>();
        Map<String, List<String>> groupId2EventIdListMap = new HashMap<>();
        Map<String, List<String>> groupId2PageIdListMap = new HashMap<>();
        Map<String, List<String>> groupId2DataListMap = new HashMap<>();
        if (groupIdList.size() > 0 && ((request.getCondition() != null && !request.getCondition().isEmpty()) || limit > 0)) {//获取全部用户组时不返回权限和用户
            //用户id列表
            groupId2IdentityIdListMap = identityGroupService.getGroupId2IdentityIdListMap(groupIdList);
            //获取用户组关联的事件
            groupId2EventIdListMap = groupEventService.getGroupId2EventIdListMap(groupIdList);
            //获取用户组关联的页面
            groupId2PageIdListMap = groupPageService.getGroupId2PageIdListMap(groupIdList);
            //获取用户组关联的数据
            groupId2DataListMap = groupDataService.getGroupId2DataListMap(groupIdList);
        }
        List<GroupDTO> groupDTOList = new ArrayList<>();
        for (GroupInfoDAO groupInfoDAO : groupInfoDAOList) {
            GroupDTO groupDTO = GroupDTO.create(groupInfoDAO);
            groupDTO.setUserCount(groupId2IGRCountMap.getOrDefault(groupDTO.getGroupId(), 0));
            groupDTO.setIdentityIdList(groupId2IdentityIdListMap.getOrDefault(groupDTO.getGroupId(), new ArrayList<>()));
            groupDTO.setEventIdList(groupId2EventIdListMap.getOrDefault(groupDTO.getGroupId(), new ArrayList<>()));
            groupDTO.setPageIdList(groupId2PageIdListMap.getOrDefault(groupDTO.getGroupId(), new ArrayList<>()));
            groupDTO.setDataList(groupId2DataListMap.getOrDefault(groupDTO.getGroupId(), new ArrayList<>()));
            groupDTOList.add(groupDTO);
        }
        return MsResponse.success(
                DataListDTO.<GroupDTO>builder()
                        .dataCount(dataCount)
                        .dataList(groupDTOList)
                        .build()
        );
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addGroup")
    public MsResponse<GroupDTO> addGroup(@Valid @RequestBody AddGroupRequest request) {
        String appChannelId = Common.getAppChannelId(request);
        String dataChannelId = Common.getDataChannelId(request);
        //添加用户组
        GroupInfoDAO groupInfoDAO = groupService.addGroup(request.getGroupName(), request.getExtraContent(), request.getSort(), appChannelId, dataChannelId);
        //添加用户组权限
        groupEventService.addGERList(groupInfoDAO.getGroupId(), request.getEventIdList());
        //添加用户组页面
        groupPageService.addGPRList(groupInfoDAO.getGroupId(), request.getPageIdList());
        //添加用户组数据
        groupDataService.addGDRList(groupInfoDAO.getGroupId(), request.getDataList());

        //用户信息关联用户组
        if (Objects.nonNull(request.getUserIdList())) {
            identityGroupService.updateIdentityGroupRelation(request.getUserIdList(), groupInfoDAO.getGroupId(), appChannelId);
        }

        return MsResponse.success(groupService.createGroupEntity(groupInfoDAO.getGroupId()));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateGroup")
    public MsResponse<GroupDTO> updateGroup(@Valid @RequestBody UpdateGroupRequest request) {
        GroupInfoDAO groupInfoDAO = groupService.getGroup(request.getGroupId());
        if (groupInfoDAO == null) {
            return MsResponse.fail("50002", "用户组不存在");
        }
        //修改用户组
        GroupInfoDAO updateData = new GroupInfoDAO();
        updateData.setGroupId(groupInfoDAO.getGroupId());
        if (request.getGroupName() != null) {
            updateData.setGroupName(request.getGroupName());
        }
        if (request.getExtraContent() != null) {
            updateData.setExtraContent(StringUtil.jsonEncode(request.getExtraContent()));
        }
        groupService.updateGroup(updateData);
        boolean isLogout = false;//是否需要退出登录
        if (request.getEventIdList() != null) {
            //修改用户组事件
            groupEventService.updateGroupEventRelation(groupInfoDAO.getGroupId(), request.getEventIdList());
            isLogout = true;
        }
        if (request.getPageIdList() != null) {
            //修改用户组页面
            groupPageService.updateGroupPageRelation(groupInfoDAO.getGroupId(), request.getPageIdList());
            isLogout = true;
        }
        if (request.getDataList() != null) {
            //修改用户组数据
            groupDataService.updateGroupDataRelation(groupInfoDAO.getGroupId(), request.getDataList());
            isLogout = true;
        }
        if (isLogout) {
            //用户组下的用户强制下线
            tokenService.logoutByGroupId(groupInfoDAO.getGroupId());
            // 清除用户组相关缓存
            List<IGRWithIdentityDAO> igrWithIdentityList = identityGroupService.getIGRWithIdentityList(GetIdentityGroupCondition
                    .builder().groupId(request.getGroupId()).build(), 0, 0);
            if (!CollectionUtils.isEmpty(igrWithIdentityList)) {
                List<String> identityIdList = igrWithIdentityList.stream().map(IGRWithIdentityDAO::getIdentityId).collect(Collectors.toList());
                List<String> userIdList = igrWithIdentityList.stream().map(IGRWithIdentityDAO::getUserId).collect(Collectors.toList());
                identityService.clearIdentityCache(identityIdList);
                userService.clearUserCache(userIdList);
            }
        }
        //用户信息关联用户组
        if (Objects.nonNull(request.getUserIdList())) {
            identityGroupService.updateIdentityGroupRelation(request.getUserIdList(),
                    groupInfoDAO.getGroupId(),
                    groupInfoDAO.getAppChannelId());
        }
        return MsResponse.success(groupService.createGroupEntity(groupInfoDAO.getGroupId()));
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deleteGroup")
    public MsResponse<GroupDTO> deleteGroup(@Valid @RequestBody DeleteGroupRequest request) {
        GroupDTO groupDTO = groupService.createGroupEntity(request.getGroupId());
        groupService.deleteGroup(request.getGroupId(), request.getIsForce());
        return MsResponse.success(groupDTO);
    }

    @OptLog
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/copyGroup")
    public MsResponse<Map<String, Object>> copyGroup(@Valid @RequestBody CopyGroupRequest request) {
        List<GroupDTO> groupDTOList = groupService.getGroupList(GetGroupCondition.builder()
                .appChannelId(request.getFromAppChannelId())
                .groupIdList(request.getGroupIdList())
                .build());
        if (groupDTOList.isEmpty()) {
            return MsResponse.success();
        }
        //生成用户组id映射关系
        Map<String, String> groupIdMap = new HashMap<>();
        for (GroupDTO groupDTO : groupDTOList) {
            String newGroupId = StringUtil.createUuid(StringUtil.md5(groupDTO.getGroupId()));
            groupIdMap.put(groupDTO.getGroupId(), newGroupId);
        }
        //添加用户组
        groupService.addGroupList(groupDTOList.stream().map(groupDTO -> GroupInfoDAO.builder()
                .groupId(groupIdMap.get(groupDTO.getGroupId()))
                .groupName(groupDTO.getGroupName())
                .extraContent(StringUtil.jsonEncode(groupDTO.getExtraContent()))
                .sort(groupDTO.getSort())
                .appChannelId(request.getToAppChannelId())
                .dataChannelId(groupDTO.getDataChannelId())
                .build()).collect(Collectors.toList()));
        //添加用户组和事件的关系
        List<GroupEventRelationDAO> groupEventRelationDAOList = groupEventService.getGroupEventRelationList(GetGroupEventCondition.builder()
                .groupIdList(new ArrayList<>(groupIdMap.keySet()))
                .build());
        int limit = 1000;
        int start = 0;
        int end = 0;
        int maxIndex = groupEventRelationDAOList.size();
        while (true) {
            start = end > 0 ? end : start;
            end = start + limit;
            end = Math.min(end, maxIndex);
            if (start > end) {
                break;
            }
            List<GroupEventRelationDAO> addDataList = groupEventRelationDAOList.subList(start, end);
            if (addDataList.isEmpty()) {
                break;
            }
            groupEventService.addGERList(addDataList.stream().map(groupEventRelationDAO -> GroupEventRelationDAO.builder()
                    .groupId(groupIdMap.get(groupEventRelationDAO.getGroupId()))
                    .eventId(groupEventRelationDAO.getEventId())
                    .build()).collect(Collectors.toList()));
        }
        //添加用户组和页面的关系
        List<GroupPageRelationDAO> groupPageRelationDAOList = groupPageService.getGPRList(GetGroupPageCondition.builder()
                .groupIdList(new ArrayList<>(groupIdMap.keySet()))
                .build());
        groupPageService.addGPRList(groupPageRelationDAOList.stream().map(groupPageRelationDAO -> GroupPageRelationDAO.builder()
                .groupId(groupIdMap.get(groupPageRelationDAO.getGroupId()))
                .pageId(groupPageRelationDAO.getPageId())
                .build()).collect(Collectors.toList()));
        return MsResponse.success();
    }
}