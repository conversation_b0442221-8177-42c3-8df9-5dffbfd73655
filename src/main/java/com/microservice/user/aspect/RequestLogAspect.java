package com.microservice.user.aspect;

import com.microservice.user.common.Common;
import com.microservice.user.exception.HttpHeaderEmptyException;
import com.microservice.user.util.HttpUtil;
import com.microservice.user.util.LogUtil;
import com.microservice.user.util.StringUtil;
import com.mssdk.log.entity.LogEntity;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;

@Aspect
@Component
@Order(1)
public class RequestLogAspect {

    //定义切点表达式,指定通知功能被应用的范围
    @Pointcut("execution(public * com.microservice.user.controller.*.*(..))")
    public void requestLog() {
    }

    @Before("requestLog()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
    }

    //通知包裹了目标方法，在目标方法调用之前和之后执行自定义的行为
    //ProceedingJoinPoint切入点可以获取切入点方法上的名字、参数、注解和对象
    @Around("requestLog()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        //获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Map<String, String> header = HttpUtil.getHeader(request);
        if (StringUtil.isEmpty(header.get("x-app_channel_id"))) {
            throw new HttpHeaderEmptyException("缺少请求头参数:x-app_channel_id", header);
        }
        if (StringUtil.isEmpty(header.get("x-data_channel_id"))) {
            throw new HttpHeaderEmptyException("缺少请求头参数:x-data_channel_id", header);
        }
        Common.setAppChannelId(header.get("x-app_channel_id"));
        Common.setDataChannelId(header.get("x-data_channel_id"));
        Common.setIp(request.getRemoteAddr());
        //默认使用业务层传的traceId，不传则自己生成
        String traceId = header.get("x-trace_id");
        traceId = StringUtil.isEmpty(traceId) ? UUID.randomUUID().toString() : traceId;
        Common.setTraceId(traceId);
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
//        String uuid = UUID.randomUUID().toString();
        LogUtil.info(LogEntity.builder()
                .bizParams(StringUtil.jsonEncode(header))
                .bizModule("client-header")
                .bizOper(request.getRequestURI())
                .traceId(Common.getTraceId())
                .build());
        LogUtil.info(LogEntity.builder()
                .bizParams(StringUtil.jsonEncode(getParameter(method, joinPoint.getArgs())))
                .bizModule("client-request")
                .bizOper(request.getRequestURI())
                .traceId(Common.getTraceId())
                .build());
        long startTime = System.currentTimeMillis();
        //前面是请求日志，后面是响应日志
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();
        String logContent = StringUtil.jsonEncode(result);
        LogUtil.info(LogEntity.builder()
                .rspResult(logContent.substring(0, Math.min(logContent.length(), 5000)))
                .bizModule("client-response")
                .bizOper("spend " + ((int) (endTime - startTime)) + " ms")
                .traceId(Common.getTraceId())
                .build());
        return result;
    }

    /**
     * 根据方法和传入的参数获取请求参数
     */
    private static Object getParameter(Method method, Object[] args) {
        List<Object> argList = new ArrayList<>();
        Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            //将RequestBody注解修饰的参数作为请求参数
            org.springframework.web.bind.annotation.RequestBody requestBody = parameters[i].getAnnotation(org.springframework.web.bind.annotation.RequestBody.class);
            if (requestBody != null) {
                argList.add(args[i]);
            }
            //将RequestParam注解修饰的参数作为请求参数
            RequestParam requestParam = parameters[i].getAnnotation(RequestParam.class);
            if (requestParam != null) {
                Map<String, Object> map = new HashMap<>();
                String key = parameters[i].getName();
                if (!StringUtils.isEmpty(requestParam.value())) {
                    key = requestParam.value();
                }
                map.put(key, args[i]);
                argList.add(map);
            }
        }
        if (argList.size() == 0) {
            return null;
        } else if (argList.size() == 1) {
            return argList.get(0);
        } else {
            return argList;
        }
    }
}
