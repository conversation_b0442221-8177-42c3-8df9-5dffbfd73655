package com.microservice.user.aspect;

import com.microservice.user.constant.LogConstant;
import com.microservice.user.exception.HttpHeaderEmptyException;
import com.microservice.user.service.LogService;
import com.microservice.user.util.HttpUtil;
import com.microservice.user.util.StringUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Aspect
@Component
@Order(1)
public class OperationLogAspect {

    @Autowired
    private LogService logService;

    //定义切点表达式,指定通知功能被应用的范围
    @Pointcut("@annotation(com.microservice.user.annotation.OptLog)")
    public void operationLog() {
    }

    @Before("operationLog()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
    }

    //通知包裹了目标方法，在目标方法调用之前和之后执行自定义的行为
    //ProceedingJoinPoint切入点可以获取切入点方法上的名字、参数、注解和对象
    @Around("operationLog()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        //获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Map<String, String> header = HttpUtil.getHeader(request);
        if (StringUtil.isEmpty(header.get("x-app_channel_id"))) {
            throw new HttpHeaderEmptyException("缺少请求头参数:x-app_channel_id", header);
        }
        if (StringUtil.isEmpty(header.get("x-data_channel_id"))) {
            throw new HttpHeaderEmptyException("缺少请求头参数:x-data_channel_id", header);
        }
        Object result = joinPoint.proceed();
        //获取操作日志文案
        LogConstant.OptLog optLog = LogConstant.OptLog.getOptLog(request.getRequestURI());
        if (optLog != null) {
            Map<String, Object> resultMap = StringUtil.getMapFromString(StringUtil.jsonEncode(result));
            logService.addLog(
                    optLog.getType(),
                    request.getRequestURI(),
                    optLog.getContent(),
                    (Map<String, Object>) resultMap.get("data"),
                    header.get("x-app_channel_id"),
                    header.get("x-data_channel_id")
            );
        }
        return result;
    }
}
