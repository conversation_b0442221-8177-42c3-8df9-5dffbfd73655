package com.microservice.user;

import com.microservice.user.listener.DependenciesCheckListener;
import com.microservice.user.util.ConfigUtil;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.io.*;
import java.net.Inet4Address;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@SpringBootApplication
@EnableTransactionManagement
@ComponentScan(basePackages = {"com.microservice.register", "com.microservice.user"})
public class MsUserApplication {

    public static String version;
    public static String name;
    public static String port;
    public static String ip;

    public static void main(String[] args) throws IOException {
        TimeZone.setDefault(TimeZone.getTimeZone(ZoneId.SHORT_IDS.get("CTT")));
        SpringApplication app = new SpringApplication(MsUserApplication.class);
        app.addListeners(new DependenciesCheckListener());
        app.run(args);
    }

    public static String loadVersion() {
        try {
            Resource resource = new ClassPathResource("version");
            InputStream inputStream = resource.getInputStream();
            BufferedReader reader = new BufferedReader(new
                    InputStreamReader(inputStream, StandardCharsets.UTF_8));
            return reader.readLine();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Properties loadConfigProperties(String fileName) throws IOException {
        File applicationConfigFile = new File(Optional.ofNullable(fileName).orElse(System.getProperty("user.dir") + File.separator + "config" + File.separator + "application.properties"));
        Properties applicationProperties = new Properties();
        if (applicationConfigFile.exists()) {
            InputStream in = new FileInputStream(applicationConfigFile);
            applicationProperties.load(new InputStreamReader(in, StandardCharsets.UTF_8));
        }
        return applicationProperties;
    }

    public static Map<String, String> loadConfigProperties(String[] args) {
        Map<String, String> map = new HashMap<>();
        for (String value : args) {
            String[] valueArr = value.split("=");
            map.put(valueArr[0].replaceAll("-", ""), valueArr[1]);
        }
        return map;
    }

    /**
     * 配置连接
     * @param systemConfigMap 配置项
     * @param context 环境
     */
    private static void connect(Map<String, Object> systemConfigMap, String context) {
        String upperCase = context.toUpperCase();
        if ("DEV".equals(upperCase)) {
            systemConfigMap.put("spring.datasource.url", "*******************************************************************************************************");
            systemConfigMap.put("spring.datasource.username", "root");
            systemConfigMap.put("spring.datasource.password", "YiyouDev+2021");
            systemConfigMap.put("msLogDomain", "http://dev.eeeyou.cn:7100/");
            systemConfigMap.put("spring.redis.host", "************");
            systemConfigMap.put("spring.redis.password", "YiyouDev+2021");
        } else if ("DRAFT".equals(upperCase)) {
            systemConfigMap.put("spring.datasource.url", "*******************************************************************************************************");
            systemConfigMap.put("spring.datasource.username", "root");
            systemConfigMap.put("spring.datasource.password", "YiyouDev+2021");
            systemConfigMap.put("msLogDomain", "http://draft.eeeyou.cn:7100/");
            systemConfigMap.put("spring.redis.host", "************");
            systemConfigMap.put("spring.redis.password", "YiyouDev+2021");
        } else if ("UAT".equals(upperCase)) {
            systemConfigMap.put("spring.datasource.url", "*********************************************************************************************************");
            systemConfigMap.put("spring.datasource.username", "root");
            systemConfigMap.put("spring.datasource.password", "YiyouDev+2021");
            systemConfigMap.put("msLogDomain", "http://uat.eeeyou.cn:7100/");
            systemConfigMap.put("spring.redis.host", "**************");
            systemConfigMap.put("spring.redis.password", "YiyouDev+2021");
        }
    }
}
