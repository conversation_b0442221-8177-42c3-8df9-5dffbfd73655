package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetGroupCondition;
import com.microservice.user.entity.condition.GetIdentityGroupCondition;
import com.microservice.user.entity.dao.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IdentityGroupRelationMapper {

    /**
     * 新增
     **/
    int addIdentityGroupRelation(IdentityGroupRelationDAO identityGroupRelation);

    /**
     * 刪除
     **/
    int deleteIdentityGroupRelation(@Param("condition") GetIdentityGroupCondition condition);

    /**
     * 更新
     **/
    int updateIdentityGroupRelation(IdentityGroupRelationDAO identityGroupRelation);

    /**
     * 查询 根据主键 id 查询
     **/
    IdentityGroupRelationDAO getIdentityGroupRelation(String identityId);

    /**
     * 查询 分页查询
     **/
    List<IdentityGroupRelationDAO> getIdentityGroupRelationList(@Param("condition") GetIdentityGroupCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getIdentityGroupRelationCount(@Param("condition") GetIdentityGroupCondition condition);

    List<IGRWithGroupDAO> getIGRWithGroupList(@Param("condition") GetGroupCondition condition);

    int addIdentityGroupRelationList(@Param("identityGroupRelationList") List<IdentityGroupRelationDAO> identityGroupRelationDAOList);

    List<IdentityGroupCountDAO> getIGRCountGroupByGroupId(@Param("condition") GetIdentityGroupCondition condition);

    List<IGRWithIdentityDAO> getIGRWithIdentityList(@Param("condition") GetIdentityGroupCondition condition, @Param("start") int start, @Param("limit") int limit);

    List<IGERWithIdentityDAO> getIGERWithIdentityList(@Param("condition") GetIdentityGroupCondition condition);

    int deleteIGRByIdentityId(@Param("identityId") String identityId, @Param("appChannelId") String appChannelId);

    List<IGRWithGroupDAO> getGroupList(@Param("companyId") String companyId, @Param("identityIdList") List<String> identityIdList);
}
