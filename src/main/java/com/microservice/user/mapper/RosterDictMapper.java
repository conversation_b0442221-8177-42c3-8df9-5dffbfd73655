package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetRosterConfigCondition;
import com.microservice.user.entity.dao.RosterDictDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RosterDictMapper {

    int deleteByPrimaryKey(String id);

    int insertDic(RosterDictDAO record);

    int insertSelective(RosterDictDAO record);

    RosterDictDAO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(RosterDictDAO record);

    List<RosterDictDAO> selectRosterDictList(@Param("condition") GetRosterConfigCondition condition);

    void deleteByField(@Param("field") String field,@Param("companyId") String companyId,
                       @Param("appChannelId") String appChannelId,@Param("dataChannelId") String dataChannelId);
}
