package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetCompanyDepartmentCondition;
import com.microservice.user.entity.condition.GetDepartmentCondition;
import com.microservice.user.entity.dao.CDRWithDepartmentDAO;
import com.microservice.user.entity.dao.CompanyDepartmentRelationDAO;
import com.microservice.user.entity.dto.ms.DepartmentDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CompanyDepartmentRelationMapper {

    /**
     * 新增
     **/
    int addCompanyDepartmentRelation(CompanyDepartmentRelationDAO companyDepartmentRelation);

    /**
     * 刪除
     **/
    int deleteCompanyDepartmentRelation(@Param("condition") GetCompanyDepartmentCondition condition);

    /**
     * 更新
     **/
    int updateCompanyDepartmentRelation(CompanyDepartmentRelationDAO companyDepartmentRelation);

    /**
     * 查询 根据主键 id 查询
     **/
    CompanyDepartmentRelationDAO getCompanyDepartmentRelation(String companyDepartmentRelationId);

    /**
     * 查询 分页查询
     **/
    List<CompanyDepartmentRelationDAO> getCompanyDepartmentRelationList(@Param("condition") GetCompanyDepartmentCondition condition);

    /**
     * 查询 分页查询 count
     **/
    int getCompanyDepartmentRelationCount();

    List<CDRWithDepartmentDAO> getCDRWithDepartmentList(@Param("condition") GetCompanyDepartmentCondition condition, @Param("start") int start, @Param("limit") int limit);

    int getCDRWithDepartmentCount(@Param("condition") GetCompanyDepartmentCondition condition);

    List<DepartmentDTO> queryByCompanyId(@Param("companyId") String companyId);
}
