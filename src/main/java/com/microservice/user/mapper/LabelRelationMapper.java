package com.microservice.user.mapper;

import com.microservice.user.entity.dao.LabelRelationDAO;
import com.microservice.user.entity.dto.ms.label.LabelMemberDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface LabelRelationMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(LabelRelationDAO record);

    int insertSelective(LabelRelationDAO record);

    LabelRelationDAO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(LabelRelationDAO record);

    int updateByPrimaryKeyWithBLOBs(LabelRelationDAO record);

    int updateByPrimaryKey(LabelRelationDAO record);

    void updateStatusByLabelId(@Param("labelId") String labelId, @Param("status") Integer status);

    void insertBatch(@Param("labelRelationDAOS") List<LabelRelationDAO> labelRelationDAOS);

    List<LabelMemberDTO> selectByLabelId(@Param("labelId") String labelId, @Param("labelIdList") List<String> labelIdList,@Param("condition") String condition);

    List<LabelRelationDAO> selectByLabelIdAndRelationId(@Param("labelId") String labelId,@Param("relationId")  String relationId,@Param("relationType")  Integer relationType);

    Map<String, List<String>> selectRelationIdsByLabelIds(
            @Param("labelIds") List<String> labelIds,
            @Param("relationType") Integer relationType);
}