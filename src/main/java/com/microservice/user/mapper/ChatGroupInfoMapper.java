package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetChatGroupCondition;
import com.microservice.user.entity.dao.ChatGroupInfoDAO;
import com.microservice.user.entity.dto.ms.SearchChatGroupDTO;
import com.microservice.user.entity.request.ms.chatGroup.SearchChatGroupRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChatGroupInfoMapper {

    /**
     * 新增
     **/
    int addChatGroup(ChatGroupInfoDAO chatGroupInfo);

    /**
     * 刪除
     **/
    int deleteChatGroup(@Param("condition") GetChatGroupCondition condition);

    /**
     * 更新
     **/
    int updateChatGroup(@Param("value") ChatGroupInfoDAO chatGroupInfo, @Param("condition") GetChatGroupCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    ChatGroupInfoDAO getChatGroup(@Param("condition") GetChatGroupCondition condition);

    /**
     * 查询 分页查询
     **/
    List<ChatGroupInfoDAO> getChatGroupList(@Param("condition") GetChatGroupCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getChatGroupCount(@Param("condition") GetChatGroupCondition condition);

    /**
     * 根据群名搜索群组
     * @param groupIdList
     * @param keyword
     * @return
     */
    List<SearchChatGroupDTO> searchChatGroupByName(@Param("groupIdList") List<String> groupIdList,@Param("keyword") String keyword);

    /**
     * 根据群成员备注名称搜索群组
     * @param searchGroupIdList
     * @param keyword
     * @return
     */
    List<SearchChatGroupDTO> searchChatGroupByUserRemarkName(@Param("groupIdList") List<String> groupIdList,@Param("keyword") String keyword);

    /**
     * 根据群成员名称搜索群组
     * @param searchGroupIdList
     * @param keyword
     * @return
     */
    List<SearchChatGroupDTO> searchChatGroupByUserName(@Param("groupIdList") List<String> groupIdList,@Param("keyword") String keyword);
}
