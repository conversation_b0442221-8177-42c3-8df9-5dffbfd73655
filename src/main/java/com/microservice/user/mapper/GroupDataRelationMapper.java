package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetGroupDataCondition;
import com.microservice.user.entity.dao.GroupDataRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GroupDataRelationMapper {

    /**
     * 新增
     **/
    int addGroupDataRelation(GroupDataRelationDAO groupPageRelation);

    /**
     * 刪除
     **/
    int deleteGroupDataRelation(@Param("condition") GetGroupDataCondition condition);

    /**
     * 更新
     **/
    int updateGroupDataRelation(GroupDataRelationDAO groupPageRelation);

    /**
     * 查询 根据主键 id 查询
     **/
    GroupDataRelationDAO getGroupDataRelation(String identityId);

    /**
     * 查询 分页查询
     **/
    List<GroupDataRelationDAO> getGroupDataRelationList(@Param("condition") GetGroupDataCondition condition);

    /**
     * 查询 分页查询 count
     **/
    int getGroupDataRelationCount();

    List<GroupDataRelationDAO> getGDRList(@Param("condition") GetGroupDataCondition condition);

    List<GroupDataRelationDAO> getIdentityDataList();

    int addGroupDataRelationList(@Param("dataList") List<GroupDataRelationDAO> dataList);
}
