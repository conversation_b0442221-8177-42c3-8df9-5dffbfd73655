package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetRosterConfigCondition;
import com.microservice.user.entity.dao.RosterGroupDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RosterGroupMapper {
    int deleteByPrimaryKey(String id);

    int insertRosterGroup(RosterGroupDAO record);

    RosterGroupDAO selectByPrimaryKey(String id);

    int updateRosterGroup(RosterGroupDAO record);

    List<RosterGroupDAO> selectRosterGroupList(@Param("condition") GetRosterConfigCondition condition, @Param("start") int start, @Param("limit") int limit);

    int countRosterGroup(@Param("condition") GetRosterConfigCondition condition);

    Integer getSortMax(@Param("condition") GetRosterConfigCondition condition);
}
