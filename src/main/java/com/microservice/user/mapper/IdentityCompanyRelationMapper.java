package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetIdentityCompanyCondition;
import com.microservice.user.entity.dao.ICRWithIdentityDAO;
import com.microservice.user.entity.dao.IdentityCompanyCountDAO;
import com.microservice.user.entity.dao.IdentityCompanyRelationDAO;
import com.microservice.user.entity.dto.ms.CompanyDepartmentPeopleNumDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IdentityCompanyRelationMapper {

    /**
     * 新增
     **/
    int addIdentityCompanyRelation(IdentityCompanyRelationDAO identityInfo);

    /**
     * 刪除
     **/
    int deleteIdentityCompanyRelation(@Param("condition") GetIdentityCompanyCondition condition);

    /**
     * 更新
     **/
    int updateIdentityCompanyRelation(IdentityCompanyRelationDAO identityInfo);

    /**
     * 查询 根据主键 id 查询
     **/
    IdentityCompanyRelationDAO getIdentityCompanyRelation(@Param("condition") GetIdentityCompanyCondition condition);

    /**
     * 查询 分页查询
     **/
    List<IdentityCompanyRelationDAO> getIdentityCompanyRelationList(@Param("condition") GetIdentityCompanyCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     * ps:暂时查询企业下的员工人数 2024-07-26
     **/
    int getIdentityCompanyRelationCount(@Param("condition") GetIdentityCompanyCondition condition);


    int addIdentityCompanyRelationList(@Param("identityCompanyRelationList") List<IdentityCompanyRelationDAO> identityCompanyRelationList);

    List<IdentityCompanyCountDAO> getICRCountGroupByCompanyId(@Param("condition") GetIdentityCompanyCondition condition);

    List<ICRWithIdentityDAO> getICRWithIdentityList(@Param("condition") GetIdentityCompanyCondition condition, @Param("start") int start, @Param("limit") int limit);

    int getICRWithIdentityCount(@Param("condition") GetIdentityCompanyCondition condition);

    /**
     * 查询公司部门人数
     *
     * @param companyIdList
     * @return java.util.List<com.microservice.user.entity.dto.ms.CompanyDepartmentPeopleNumDTO>
     * <AUTHOR>
     * @date 2024/12/13 15:11
     **/
    List<CompanyDepartmentPeopleNumDTO> getCompanyDepartmentPeopleNum(@Param("companyIdList") List<String> companyIdList);
}
