package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetIdentityFriendCondition;
import com.microservice.user.entity.dao.IdentityFriendRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IdentityFriendRelationMapper {

    /**
     * 新增
     **/
    int addIdentityFriendRelation(IdentityFriendRelationDAO identityFriendRelation);

    /**
     * 刪除
     **/
    int deleteIdentityFriendRelation(@Param("condition") GetIdentityFriendCondition condition);

    /**
     * 更新
     **/
    int updateIdentityFriendRelation(@Param("value") IdentityFriendRelationDAO identityFriendRelation, @Param("condition") GetIdentityFriendCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    IdentityFriendRelationDAO getIdentityFriendRelation(@Param("condition") GetIdentityFriendCondition condition);

    /**
     * 查询 分页查询
     **/
    List<IdentityFriendRelationDAO> getIdentityFriendRelationList(@Param("condition") GetIdentityFriendCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getIdentityFriendRelationCount(@Param("condition") GetIdentityFriendCondition condition);


    int addIdentityFriendRelationList(@Param("dataList") List<IdentityFriendRelationDAO> dataList);
}
