package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetRosterConfigCondition;
import com.microservice.user.entity.dao.RosterFieldDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RosterFieldMapper {
    int deleteByPrimaryKey(String id);

    int insertRosterField(RosterFieldDAO record);
    RosterFieldDAO selectByPrimaryKey(String id);

    int updateRosterField(RosterFieldDAO record);

    List<RosterFieldDAO> selectRosterFieldList(@Param("condition") GetRosterConfigCondition condition, @Param("start") int start, @Param("limit") int limit);

    int countRosterField(@Param("condition") GetRosterConfigCondition condition);

    Integer getSortMax(@Param("condition") GetRosterConfigCondition condition);
}
