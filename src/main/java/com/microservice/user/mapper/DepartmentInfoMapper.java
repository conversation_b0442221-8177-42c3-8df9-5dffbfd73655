package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetDepartmentCondition;
import com.microservice.user.entity.dao.DepartmentInfoDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DepartmentInfoMapper {

    /**
     * 新增
     **/
    int addDepartment(DepartmentInfoDAO departmentInfo);

    /**
     * 刪除
     **/
    int deleteDepartment(@Param("condition") GetDepartmentCondition condition);

    /**
     * 更新
     **/
    int updateDepartment(@Param("value") DepartmentInfoDAO departmentInfo, @Param("condition") GetDepartmentCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    DepartmentInfoDAO getDepartment(@Param("condition") GetDepartmentCondition condition);

    /**
     * 查询 分页查询
     **/
    List<DepartmentInfoDAO> getDepartmentList(@Param("condition") GetDepartmentCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getDepartmentCount(@Param("condition") GetDepartmentCondition condition);

}
