package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetGroupCondition;
import com.microservice.user.entity.dao.GroupInfoDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GroupInfoMapper {

    /**
     * 新增
     **/
    int addGroup(GroupInfoDAO groupInfo);

    /**
     * 刪除
     **/
    int deleteGroup(@Param("condition") GetGroupCondition condition);

    /**
     * 更新
     **/
    int updateGroup(@Param("value") GroupInfoDAO groupInfo, @Param("condition") GetGroupCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    GroupInfoDAO getGroup(@Param("condition") GetGroupCondition condition);

    /**
     * 查询 分页查询
     **/
    List<GroupInfoDAO> getGroupList(@Param("condition") GetGroupCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getGroupCount(@Param("condition") GetGroupCondition condition);


    int addGroupList(@Param("dataList") List<GroupInfoDAO> dataList);
}
