package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetUserCondition;
import com.microservice.user.entity.dao.IGERWithIdentityDAO;
import com.microservice.user.entity.dao.UserInfoDAO;
import com.microservice.user.entity.dao.UserInfoWithIdentityDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserInfoMapper {

    /**
     * 新增
     **/
    int addUser(UserInfoDAO userInfo);

    /**
     * 刪除
     **/
    int deleteUser(@Param("condition") GetUserCondition condition);

    /**
     * 更新
     **/
    int updateUser(@Param("value") UserInfoDAO userInfo, @Param("condition") GetUserCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    UserInfoDAO getUser(@Param("condition") GetUserCondition condition);

    UserInfoDAO getUserByIdentityId(@Param("identityId") String identityId);

    /**
     * 查询 分页查询
     **/
    List<UserInfoDAO> getUserList(@Param("condition") GetUserCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getUserCount(@Param("condition") GetUserCondition condition);

    List<IGERWithIdentityDAO> getIGERWithIdentityList(@Param("eventId") String eventId, @Param("companyId") String companyId);

    long getUserAndIdentityCount(@Param("companyId") String companyId, @Param("departmentIdList") List<String> departmentIdList
            , @Param("userName") String userName);

    List<UserInfoWithIdentityDAO> getUserAndIdentityList(@Param("companyId") String companyId
            , @Param("departmentIdList") List<String> departmentIdList
            , @Param("userName") String userName
            , @Param("start") int start, @Param("limit") int limit);


}
