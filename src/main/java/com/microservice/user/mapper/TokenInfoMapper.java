package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetTokenCondition;
import com.microservice.user.entity.dao.TokenInfoDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TokenInfoMapper {

    /**
     * 新增
     **/
    int addToken(TokenInfoDAO tokenInfo);

    /**
     * 刪除
     **/
    int deleteToken(@Param("condition") GetTokenCondition condition);

    /**
     * 更新
     **/
    int updateToken(@Param("value") TokenInfoDAO tokenInfo, @Param("condition") GetTokenCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    TokenInfoDAO getToken(@Param("condition") GetTokenCondition condition);

    /**
     * 查询 分页查询
     **/
    List<TokenInfoDAO> getTokenList(@Param("condition") GetTokenCondition condition, int start, int limit);

    List<TokenInfoDAO> getLastUserTokenListGroupByUserId(@Param("condition") GetTokenCondition condition);

    List<TokenInfoDAO> getLastUserTokenListGroupByUserIdAndAppChannelId(@Param("condition") GetTokenCondition condition);

    List<TokenInfoDAO> getLastUserTokenListGroupByIdentityId(@Param("condition") GetTokenCondition condition);

    List<TokenInfoDAO> getLastUserTokenListGroupByIdentityIdAndAppChannelId(@Param("condition") GetTokenCondition condition);

    int getTokenCount(@Param("condition") GetTokenCondition condition);
}
