package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetRosterConfigCondition;
import com.microservice.user.entity.dao.RosterTeamDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RosterTeamMapper {
    int deleteByPrimaryKey(String id);

    int insertRosterTeam(RosterTeamDAO record);

    RosterTeamDAO selectByPrimaryKey(String id);

    int updateRosterTeam(RosterTeamDAO record);

    int countRosterTeam(@Param("condition") GetRosterConfigCondition condition);

    List<RosterTeamDAO> selectRosterTeamList(@Param("condition") GetRosterConfigCondition condition, @Param("start") int start, @Param("limit") int limit);

    Integer getSortMax(@Param("condition") GetRosterConfigCondition condition);
}