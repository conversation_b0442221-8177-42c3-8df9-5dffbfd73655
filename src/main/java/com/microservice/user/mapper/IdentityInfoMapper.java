package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetIdentityCondition;
import com.microservice.user.entity.dao.IdentityByCompanyDAO;
import com.microservice.user.entity.dao.IdentityInfoDAO;
import com.microservice.user.entity.dao.IdentityInfoWithCompanyInfoDAO;
import com.microservice.user.entity.dao.IdentityWithUserAndCompanyDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IdentityInfoMapper {

    /**
     * 新增
     **/
    int addIdentity(IdentityInfoDAO identityInfo);

    /**
     * 刪除
     **/
    int deleteIdentity(@Param("condition") GetIdentityCondition condition);

    /**
     * 更新
     **/
    int updateIdentity(@Param("value") IdentityInfoDAO value, @Param("condition") GetIdentityCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    IdentityInfoDAO getIdentity(@Param("condition") GetIdentityCondition condition);

    IdentityInfoDAO getIdentityWithDepartmentList(@Param("condition") GetIdentityCondition condition);

    /**
     * 查询 分页查询
     **/
    List<IdentityInfoDAO> getIdentityList(@Param("condition") GetIdentityCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getIdentityCount(@Param("condition") GetIdentityCondition condition);


    List<IdentityInfoWithCompanyInfoDAO> getIdentityWithCompanyList(@Param("condition") GetIdentityCondition condition);


    List<IdentityWithUserAndCompanyDAO> getIdentityWithUserAndCompanyList(@Param("condition") GetIdentityCondition condition);

    List<String> getIdentityByDepartmentIds(@Param("departmentIds") List<String> departmentIds);

    List<IdentityByCompanyDAO> getIdentityByCompanyId(@Param("companyId") String companyId);

    /**
     * 获取身份信息
     *
     * @param companyId
     * @param userIdList
     * @return java.util.List<com.microservice.user.entity.dao.IdentityInfoWithCompanyInfoDAO>
     * <AUTHOR>
     * @date 2024/12/17 11:36
     **/
    List<IdentityInfoWithCompanyInfoDAO> getIdentityByCompanyIdOrUserId(@Param("companyId") String companyId, @Param("userIdList") List<String> userIdList);

    List<String> getIdentityByEmployeeStatus(@Param("identityIds") List<String> identityIds);
}
