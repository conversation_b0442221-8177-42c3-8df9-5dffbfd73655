package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetEntityCondition;
import com.microservice.user.entity.condition.GetEntityRelationCondition;
import com.microservice.user.entity.dao.EntityRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EntityRelationMapper {

    /**
     * 新增
     **/
    int addEntityRelation(EntityRelationDAO entityRelation);

    /**
     * 刪除
     **/
    int deleteEntityRelation(@Param("condition") GetEntityRelationCondition condition);

    /**
     * 更新
     **/
    int updateEntityRelation(@Param("value") EntityRelationDAO entityRelation, @Param("condition") GetEntityRelationCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    EntityRelationDAO getEntityRelation(@Param("condition") GetEntityRelationCondition condition);

    /**
     * 查询 分页查询
     **/
    List<EntityRelationDAO> getEntityRelationList(int start, int limit);

    /**
     * 查询 分页查询 count
     **/
    int getEntityRelationCount();

    int addEntityRelationList(@Param("entityRelationList") List<EntityRelationDAO> entityRelationDAOList);

    List<EntityRelationDAO> getERWithEntityList(@Param("condition") GetEntityRelationCondition condition, @Param("start") int start, @Param("limit") int limit);

    int getERWithEntityCount(@Param("condition") GetEntityRelationCondition condition);
}
