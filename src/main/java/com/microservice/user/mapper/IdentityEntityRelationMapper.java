package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetIdentityCompanyCondition;
import com.microservice.user.entity.condition.GetIdentityEntityCondition;
import com.microservice.user.entity.dao.ICRWithIdentityDAO;
import com.microservice.user.entity.dao.IdentityEntityRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IdentityEntityRelationMapper {

    /**
     * 新增
     **/
    int addIdentityEntityRelation(IdentityEntityRelationDAO identityInfo);

    /**
     * 刪除
     **/
    int deleteIdentityEntityRelation(@Param("condition") GetIdentityEntityCondition condition);

    /**
     * 更新
     **/
    int updateIdentityEntityRelation(IdentityEntityRelationDAO identityInfo);

    /**
     * 查询 根据主键 id 查询
     **/
    IdentityEntityRelationDAO getIdentityEntityRelation(@Param("condition") GetIdentityEntityCondition condition);

    /**
     * 查询 分页查询
     **/
    List<IdentityEntityRelationDAO> getIdentityEntityRelationList(@Param("condition") GetIdentityEntityCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getIdentityEntityRelationCount(@Param("condition") GetIdentityEntityCondition condition);


    int addIdentityEntityRelationList(@Param("identityEntityRelationList") List<IdentityEntityRelationDAO> identityEntityRelationList);
}
