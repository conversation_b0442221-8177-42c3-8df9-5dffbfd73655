package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetDepartmentCondition;
import com.microservice.user.entity.condition.GetIdentityDepartmentCondition;
import com.microservice.user.entity.dao.IDRWithIdentityDAO;
import com.microservice.user.entity.dao.IdentityCompanyRelationDAO;
import com.microservice.user.entity.dao.IdentityDepartmentRelationDAO;
import com.microservice.user.entity.dao.IDRWithDepartmentDAO;
import com.microservice.user.entity.dto.ms.DepartmentFatherAndSonDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IdentityDepartmentRelationMapper {

    /**
     * 新增
     **/
    int addIdentityDepartmentRelation(IdentityDepartmentRelationDAO identityInfo);

    /**
     * 刪除
     **/
    int deleteIdentityDepartmentRelation(@Param("condition") GetIdentityDepartmentCondition condition);

    /**
     * 更新
     **/
    int updateIdentityDepartmentRelation(IdentityDepartmentRelationDAO identityInfo);

    /**
     * 查询 根据主键 id 查询
     **/
    IdentityDepartmentRelationDAO getIdentityDepartmentRelation(String identityId);

    /**
     * 查询 分页查询
     **/
    List<IdentityDepartmentRelationDAO> getIdentityDepartmentRelationList(@Param("condition") GetIdentityDepartmentCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getIdentityDepartmentRelationCount(@Param("condition") GetIdentityDepartmentCondition condition);


    List<IDRWithDepartmentDAO> getIDRWithDepartmentList(@Param("condition") GetDepartmentCondition condition);


    int addIdentityDepartmentRelationList(@Param("identityDepartmentRelationList") List<IdentityDepartmentRelationDAO> identityDepartmentRelationList);

    List<IDRWithIdentityDAO> getIDRWithIdentityList(@Param("condition") GetIdentityDepartmentCondition condition, @Param("start") int start, @Param("limit") int limit);

    int getIDRWithIdentityCount(@Param("condition") GetIdentityDepartmentCondition condition);
    int getIDRWithIdentityDistinctCount(@Param("condition") GetIdentityDepartmentCondition condition);

    /**
     * 获取有效身份关联关系
     *
     * @param departmentId
     * @return java.util.List<com.microservice.user.entity.dao.IdentityDepartmentRelationDAO>
     * <AUTHOR>
     * @date 2025/3/31 17:59
     **/
    List<IdentityDepartmentRelationDAO> getIDRValidIdentityRelation(@Param("departmentId") String departmentId);

    List<IdentityDepartmentRelationDAO> batchGetIdentityIdsByDepartments(List<String> departmentIds);

    List<DepartmentFatherAndSonDTO> getDepartmentFatherAndSon(List<String> departmentIds);

}
