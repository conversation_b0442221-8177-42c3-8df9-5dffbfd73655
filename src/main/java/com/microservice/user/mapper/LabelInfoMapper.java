package com.microservice.user.mapper;


import com.microservice.user.entity.dao.LabelInfoDAO;
import com.microservice.user.entity.dto.ms.label.LabelInfoDTO;
import com.microservice.user.entity.request.ms.label.LabelQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface LabelInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(LabelInfoDAO record);

    int insertSelective(LabelInfoDAO record);

    LabelInfoDAO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(LabelInfoDAO record);

    int updateByPrimaryKey(LabelInfoDAO record);

    List<LabelInfoDTO> getLabelList(@Param("request") LabelQueryRequest request);

    List<LabelInfoDAO> getLabelListByParentId(@Param("labelId") String labelId,@Param("labelIdList") List<String> labelIdList);

    List<LabelInfoDTO> getLabelListByUser(@Param("relationId") List<String> relationId,@Param("relationType") Integer relationType);

    List<LabelInfoDTO> getExistLabelIds(@Param("list") List<String> labelIds);

}