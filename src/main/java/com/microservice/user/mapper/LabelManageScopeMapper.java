package com.microservice.user.mapper;


import com.microservice.user.entity.dao.LabelManageScopeDAO;
import com.microservice.user.entity.dto.ms.label.LabelManageScopeDTO;
import com.microservice.user.entity.dto.ms.label.LabelRelationDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface LabelManageScopeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(LabelManageScopeDAO record);

    int insertSelective(LabelManageScopeDAO record);

    LabelManageScopeDAO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(LabelManageScopeDAO record);

    int updateByPrimaryKeyWithBLOBs(LabelManageScopeDAO record);

    int updateByPrimaryKey(LabelManageScopeDAO record);


    int insertBatch(List<LabelManageScopeDAO> labelManageScopeDAOS);

    void updateStatusByLabelRelationId(@Param("labelId") String labelId,@Param("labelRelationId") Integer labelRelationId, Integer status);

    List<LabelManageScopeDTO> selectByLabelRelationId(@Param("labelRelationId") Integer labelRelationId);

    Set<LabelRelationDTO> selectByRelationId(@Param("labelId") String labelId,@Param("labelIdList") List<String> labelIdList, @Param("relationIds") List<String> relationIds, Integer relationType);
}