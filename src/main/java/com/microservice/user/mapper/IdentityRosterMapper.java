package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetRosterCondition;
import com.microservice.user.entity.dao.IdentityRosterDAO;
import com.microservice.user.entity.dao.RosterDAO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * &#064;DATE: 2024/9/5
 * &#064;AUTHOR: XSL
 *
 */
@Mapper
@Repository
public interface IdentityRosterMapper {

    /**
     * 查询集合总数量
     * @param roster
     * @return
     */
    public long findAllByRosterDAOCount(GetRosterCondition roster);

    /**
     * 查询集合
     * @param roster
     * @return
     */
    public List<IdentityRosterDAO> findAllByRosterDAO(GetRosterCondition roster);

    /**
     * 查询详情
     * @param identityId
     * @return
     */
    public IdentityRosterDAO findByIdentityId(String identityId);

    /**
     * 查询花名册数据
     * @param rosterId
     * @return
     */
    public RosterDAO getRosterByIdentityId(String rosterId);

    /**
     * 新增数据
     * @param roster
     * @return
     */
    public long addRosterDAO(RosterDAO roster);

    /**
     * 更新数据
     * @param roster
     * @return
     */
    public int updateRosterDAO(RosterDAO roster);

    /**
     * 逻辑删除
     * @param identityId
     * @return
     */
    public int deleteRosterDAO(String identityId);

}