package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetGroupEventCondition;
import com.microservice.user.entity.dao.GroupEventRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GroupEventRelationMapper {

    /**
     * 新增
     **/
    int addGroupEventRelation(GroupEventRelationDAO groupEventRelation);

    /**
     * 刪除
     **/
    int deleteGroupEventRelation(@Param("condition") GetGroupEventCondition condition);

    /**
     * 更新
     **/
    int updateGroupEventRelation(GroupEventRelationDAO groupEventRelation);

    /**
     * 查询 根据主键 id 查询
     **/
    GroupEventRelationDAO getGroupEventRelation(String identityId);

    /**
     * 查询 分页查询
     **/
    List<GroupEventRelationDAO> getGroupEventRelationList(@Param("condition") GetGroupEventCondition condition);

    /**
     * 查询 分页查询 count
     **/
    int getGroupEventRelationCount();

    List<GroupEventRelationDAO> getGERList(@Param("condition") GetGroupEventCondition condition);

    List<GroupEventRelationDAO> getIdentityEventList();

    int addGroupEventRelationList(@Param("dataList") List<GroupEventRelationDAO> dataList);
}
