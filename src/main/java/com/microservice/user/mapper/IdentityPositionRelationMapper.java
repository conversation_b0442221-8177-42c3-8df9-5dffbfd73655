package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetIdentityPositionCondition;
import com.microservice.user.entity.condition.GetPositionCondition;
import com.microservice.user.entity.dao.IPRWithIdentityDAO;
import com.microservice.user.entity.dao.IPRWithPositionDAO;
import com.microservice.user.entity.dao.IdentityDepartmentRelationDAO;
import com.microservice.user.entity.dao.IdentityPositionRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IdentityPositionRelationMapper {

    /**
     * 新增
     **/
    int addIdentityPositionRelation(IdentityPositionRelationDAO identityPositionRelation);

    /**
     * 刪除
     **/
    int deleteIdentityPositionRelation(@Param("condition") GetIdentityPositionCondition condition);

    /**
     * 更新
     **/
    int updateIdentityPositionRelation(IdentityPositionRelationDAO identityPositionRelation);

    /**
     * 查询 根据主键 id 查询
     **/
    IdentityPositionRelationDAO getIdentityPositionRelation(String identityId);

    /**
     * 查询 分页查询
     **/
    List<IdentityPositionRelationDAO> getIdentityPositionRelationList(@Param("condition") GetIdentityPositionCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getIdentityPositionRelationCount(@Param("condition") GetIdentityPositionCondition condition);


    List<IPRWithPositionDAO> getIPRWithPositionList(@Param("condition") GetPositionCondition condition);


    int addIdentityPositionRelationList(@Param("identityPositionRelationList") List<IdentityPositionRelationDAO> identityPositionRelationList);


    List<IPRWithIdentityDAO> getIPRWithIdentityList(@Param("condition") GetIdentityPositionCondition condition, @Param("start") int start, @Param("limit") int limit);

    int getIPRWithIdentityCount(@Param("condition") GetIdentityPositionCondition condition);

    List<IPRWithPositionDAO> getPositionList(@Param("companyId") String companyId, @Param("identityIdList") List<String> identityIdList);
}
