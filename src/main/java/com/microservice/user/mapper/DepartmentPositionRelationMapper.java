package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetDepartmentPositionCondition;
import com.microservice.user.entity.condition.GetPositionCondition;
import com.microservice.user.entity.dao.DPRWithPositionDAO;
import com.microservice.user.entity.dao.DepartmentPositionRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DepartmentPositionRelationMapper {

    /**
     * 新增
     **/
    int addDepartmentPositionRelation(DepartmentPositionRelationDAO departmentPositionRelation);

    /**
     * 刪除
     **/
    int deleteDepartmentPositionRelation(@Param("condition") GetDepartmentPositionCondition condition);

    /**
     * 更新
     **/
    int updateDepartmentPositionRelation(DepartmentPositionRelationDAO departmentPositionRelation);

    /**
     * 查询 根据主键 id 查询
     **/
    DepartmentPositionRelationDAO getDepartmentPositionRelation(String departmentPositionRelationId);

    /**
     * 查询 分页查询
     **/
    List<DepartmentPositionRelationDAO> getDepartmentPositionRelationList(@Param("condition") GetDepartmentPositionCondition condition);

    /**
     * 查询 分页查询 count
     **/
    int getDepartmentPositionRelationCount();

    List<DPRWithPositionDAO> getDPRWithPositionList(@Param("condition") GetDepartmentPositionCondition condition, @Param("start") int start, @Param("limit") int limit);

    int getDPRWithPositionCount(@Param("condition") GetDepartmentPositionCondition condition);
}
