package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetLogCondition;
import com.microservice.user.entity.dao.LogInfoDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LogInfoMapper {

    /**
     * 新增
     **/
    int addLog(LogInfoDAO logInfo);

    /**
     * 刪除
     **/
    int deleteLog(@Param("condition") GetLogCondition condition);

    /**
     * 更新
     **/
    int updateLog(LogInfoDAO logInfo);

    /**
     * 查询 根据主键 id 查询
     **/
    LogInfoDAO getLog(String logId);

    /**
     * 查询 分页查询
     **/
    List<LogInfoDAO> getLogList(@Param("condition") GetLogCondition condition, int start, int limit);

    /**
     * 查询 分页查询 count
     **/
    int getLogCount(@Param("condition") GetLogCondition condition);

}
