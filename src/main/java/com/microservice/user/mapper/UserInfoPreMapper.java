package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetUserPreCondition;
import com.microservice.user.entity.dao.UserInfoPreDAO;
import com.microservice.user.entity.dto.ms.CompanyDepartmentPeopleNumDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserInfoPreMapper {

    /**
     * 新增
     **/
    int addUserPre(UserInfoPreDAO userInfoPre);

    /**
     * 刪除
     **/
    int deleteUserPre(@Param("condition") GetUserPreCondition condition);

    /**
     * 更新
     **/
    int updateUserPre(UserInfoPreDAO userInfoPre);

    /**
     * 查询 根据主键 id 查询
     **/
    UserInfoPreDAO getUserPre(@Param("condition") GetUserPreCondition condition);

    /**
     * 查询 分页查询
     **/
    List<UserInfoPreDAO> getUserPreList(@Param("condition") GetUserPreCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getUserPreCount(@Param("condition") GetUserPreCondition condition);

    /**
     * 查询公司部门人数
     *
     * @param companyIdList
     * @return java.util.List<com.microservice.user.entity.dto.ms.CompanyDepartmentPeopleNumDTO>
     * <AUTHOR>
     * @date 2024/12/13 15:11
     **/
    List<CompanyDepartmentPeopleNumDTO> getCompanyDepartmentPeopleNum(@Param("companyIdList") List<String> companyIdList);

    /**
     * 获取有效未加入用户列表
     *
     * @param departmentId
     * @return java.util.List<com.microservice.user.entity.dao.UserInfoPreDAO>
     * <AUTHOR>
     * @date 2025/4/1 9:58
     **/
    List<UserInfoPreDAO> getValidUserList(@Param("departmentId") String departmentId);

    /**
     * 删除未加入用户的部门绑定
     *
     * @param departmentId
     * @return void
     * <AUTHOR>
     * @date 2025/4/1 9:58
     **/
    void deleteDepartment(@Param("departmentId") String departmentId);
}
