package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetCompanyPositionCondition;
import com.microservice.user.entity.condition.GetPositionCondition;
import com.microservice.user.entity.dao.CPRWithPositionDAO;
import com.microservice.user.entity.dao.CompanyPositionRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CompanyPositionRelationMapper {

    /**
     * 新增
     **/
    int addCompanyPositionRelation(CompanyPositionRelationDAO companyPositionRelation);

    /**
     * 刪除
     **/
    int deleteCompanyPositionRelation(@Param("condition") GetCompanyPositionCondition condition);

    /**
     * 更新
     **/
    int updateCompanyPositionRelation(CompanyPositionRelationDAO companyPositionRelation);

    /**
     * 查询 根据主键 id 查询
     **/
    CompanyPositionRelationDAO getCompanyPositionRelation(String companyPositionRelationId);

    /**
     * 查询 分页查询
     **/
    List<CompanyPositionRelationDAO> getCompanyPositionRelationList(@Param("condition") GetCompanyPositionCondition condition);

    /**
     * 查询 分页查询 count
     **/
    int getCompanyPositionRelationCount(@Param("condition") GetCompanyPositionCondition condition);

    List<CPRWithPositionDAO> getCPRWithPositionList(@Param("condition") GetCompanyPositionCondition condition, @Param("start") int start, @Param("limit") int limit);

    int getCPRWithPositionCount(@Param("condition") GetCompanyPositionCondition condition);

}
