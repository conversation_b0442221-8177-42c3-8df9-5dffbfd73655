package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetEntityCondition;
import com.microservice.user.entity.condition.GetEntityRelationCondition;
import com.microservice.user.entity.dao.EntityInfoDAO;
import com.microservice.user.entity.dao.EntityRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EntityInfoMapper {

    /**
     * 新增
     **/
    int addEntity(EntityInfoDAO entityInfo);

    /**
     * 刪除
     **/
    int deleteEntity(@Param("condition") GetEntityCondition condition);

    /**
     * 更新
     **/
    int updateEntity(@Param("value") EntityInfoDAO entityInfo, @Param("condition") GetEntityCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    EntityInfoDAO getEntity(@Param("condition") GetEntityCondition condition);

    /**
     * 查询 分页查询
     **/
    List<EntityInfoDAO> getEntityList(@Param("condition") GetEntityCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getEntityCount(@Param("condition") GetEntityCondition condition);

}
