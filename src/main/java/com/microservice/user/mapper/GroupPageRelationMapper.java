package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetGroupPageCondition;
import com.microservice.user.entity.dao.GroupPageRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GroupPageRelationMapper {

    /**
     * 新增
     **/
    int addGroupPageRelation(GroupPageRelationDAO groupPageRelation);

    /**
     * 刪除
     **/
    int deleteGroupPageRelation(@Param("condition") GetGroupPageCondition condition);

    /**
     * 更新
     **/
    int updateGroupPageRelation(GroupPageRelationDAO groupPageRelation);

    /**
     * 查询 根据主键 id 查询
     **/
    GroupPageRelationDAO getGroupPageRelation(String identityId);

    /**
     * 查询 分页查询
     **/
    List<GroupPageRelationDAO> getGroupPageRelationList(@Param("condition") GetGroupPageCondition condition);

    /**
     * 查询 分页查询 count
     **/
    int getGroupPageRelationCount();

    List<GroupPageRelationDAO> getGPRList(@Param("condition") GetGroupPageCondition condition);

    List<GroupPageRelationDAO> getIdentityPageList();

    int addGroupPageRelationList(@Param("dataList") List<GroupPageRelationDAO> dataList);
}
