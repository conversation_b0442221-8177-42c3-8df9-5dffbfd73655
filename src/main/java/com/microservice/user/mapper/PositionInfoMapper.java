package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetPositionCondition;
import com.microservice.user.entity.dao.PositionInfoDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PositionInfoMapper {

    /**
     * 新增
     **/
    int addPosition(PositionInfoDAO positionInfo);

    /**
     * 刪除
     **/
    int deletePosition(@Param("condition") GetPositionCondition condition);

    /**
     * 更新
     **/
    int updatePosition(@Param("value") PositionInfoDAO positionInfo, @Param("condition") GetPositionCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    PositionInfoDAO getPosition(@Param("condition") GetPositionCondition condition);

    /**
     * 查询 分页查询
     **/
    List<PositionInfoDAO> getPositionList(@Param("condition") GetPositionCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getPositionCount(@Param("condition") GetPositionCondition condition);

}
