package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetIdentityChatGroupCondition;
import com.microservice.user.entity.dao.IdentityChatGroupRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IdentityChatGroupRelationMapper {

    /**
     * 新增
     **/
    int addIdentityChatGroupRelation(IdentityChatGroupRelationDAO identityInfo);

    /**
     * 刪除
     **/
    int deleteIdentityChatGroupRelation(@Param("condition") GetIdentityChatGroupCondition condition);

    /**
     * 更新
     **/
    int updateIdentityChatGroupRelation(@Param("value") IdentityChatGroupRelationDAO identityInfo, @Param("condition") GetIdentityChatGroupCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    IdentityChatGroupRelationDAO getIdentityChatGroupRelation(@Param("condition") GetIdentityChatGroupCondition condition);

    /**
     * 查询 分页查询
     **/
    List<IdentityChatGroupRelationDAO> getIdentityChatGroupRelationList(@Param("condition") GetIdentityChatGroupCondition condition, @Param("start") int start, @Param("limit") int limit);

    /**
     * 查询 分页查询 count
     **/
    int getIdentityChatGroupRelationCount(@Param("condition") GetIdentityChatGroupCondition condition);

    int addIdentityChatGroupRelationList(@Param("dataList") List<IdentityChatGroupRelationDAO> dataList);
}
