package com.microservice.user.mapper;

import com.microservice.user.entity.dao.CompanyLabelRelationDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CompanyLabelRelationMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CompanyLabelRelationDAO record);

    int insertSelective(CompanyLabelRelationDAO record);

    CompanyLabelRelationDAO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CompanyLabelRelationDAO record);

    int updateByPrimaryKey(CompanyLabelRelationDAO record);

    void updateStatusByLabelId(@Param("labelId") String labelId,@Param("status") Integer status);
}