package com.microservice.user.mapper;

import com.microservice.user.entity.condition.GetCompanyCondition;
import com.microservice.user.entity.dao.CompanyInfoDAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface CompanyInfoMapper {

    /**
     * 新增
     **/
    int addCompany(CompanyInfoDAO companyInfo);

    /**
     * 刪除
     **/
    int deleteCompany(@Param("condition") GetCompanyCondition condition);

    /**
     * 更新
     **/
    int updateCompany(@Param("value") CompanyInfoDAO companyInfo, @Param("condition") GetCompanyCondition condition);

    /**
     * 查询 根据主键 id 查询
     **/
    CompanyInfoDAO getCompany(@Param("condition") GetCompanyCondition condition);

    /**
     * 查询 分页查询
     **/
    List<CompanyInfoDAO> getCompanyList(@Param("condition") GetCompanyCondition condition, int start, int limit);

    List<Map<String, Object>> getCompanyMapList(@Param("condition") GetCompanyCondition condition, @Param("columnList") List<String> columnList, int start, int limit);

    /**
     * 查询 分页查询 count
     **/
    int getCompanyCount(@Param("condition") GetCompanyCondition condition);

    List<CompanyInfoDAO> getCompanyListByIds(List<String> companyIds);

    List<CompanyInfoDAO> getAllDescendantCompanies(GetCompanyCondition req);

}
