package com.microservice.user.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 花名册配置常量类
 */
public class RosterConfigConstant {


    public static final int ROSTER_TEAM_MAX_COUNT = 10; // 默认每个公司下子集上限限制

    public static final int ROSTER_GROUP_MAX_COUNT = 0; // 默认子集下规则分组上限 0 表示不限制

    public static final int ROSTER_FIELD_MAX_COUNT = 0; // 默认分组下规则数量上限 0 表示不限制
    @Getter
    @AllArgsConstructor
    public enum IsShow {
        HIDE(0,"隐藏"),
        SHOW(1,"显示")
        ;
        private final Integer code;
        private final String dec;
    }

    @Getter
    @AllArgsConstructor
    public enum IsDeleted {
        NOT_DELETED(0,"未删除"),
        DELETED(1,"已删除")
        ;
        private final Integer code;
        private final String dec;
    }

    @Getter
    @AllArgsConstructor
    public enum IsSystem {
        IS_SYSTEM(1,"系统内置"),
        NO_SYSTEM(0,"非系统内置")
        ;
        private final Integer code;
        private final String dec;
    }

}
