package com.microservice.user.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

public class LogConstant {

    public static String[] LOG_MAIN_COLUMN = {
            "id",
            "type",
            "opt",
            "content",
            "appChannelId",
            "dataChannelId",
    };

    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    public enum OptLog {

        LOGIN(1, "登录", "登录成功，用户id：{userId}，用户名：{userName}", "/common/login"),
        LOGOUT(2, "退出", "退出成功", "/common/logout"),
        USER_ADD_USER(3, "添加用户", "添加用户，用户id：{userId}，用户名：{userName}", "/user/addUser"),
        USER_UPDATE_USER(4, "修改用户", "修改用户，用户id：{userId}，用户名：{userName}", "/user/updateUser"),
        USER_DELETE_USER(5, "删除用户", "删除用户，用户id：{userId}，用户名：{userName}", "/user/deleteUser"),
        IDENTITY_ADD_IDENTITY(6, "添加身份", "添加身份，身份id：{identityId}", "/identity/addIdentity"),
        IDENTITY_UPDATE_IDENTITY(7, "修改身份", "修改身份，身份id：{identityId}", "/identity/updateIdentity"),
        IDENTITY_DELETE_IDENTITY(8, "删除身份", "删除身份，身份id：{identityId}", "/identity/deleteIdentity"),
        IDENTITY_CHANGE_IDENTITY(9, "切换身份", "切换身份，新身份id：{identityId}", "/identity/changeIdentity"),
        COMPANY_ADD_COMPANY(10, "添加公司", "添加公司，公司id：{companyId}，公司名称：{companyName}", "/company/addCompany"),
        COMPANY_UPDATE_COMPANY(11, "修改公司", "修改公司，公司id：{companyId}，公司名称：{companyName}", "/company/updateCompany"),
        COMPANY_DELETE_COMPANY(12, "删除公司", "删除公司，公司id：{companyId}，公司名称：{companyName}", "/company/deleteCompany"),
        DEPARTMENT_ADD_DEPARTMENT(13, "添加部门", "添加部门，部门id：{departmentId}，部门名称：{departmentName}", "/department/addDepartment"),
        DEPARTMENT_UPDATE_DEPARTMENT(14, "修改部门", "修改部门，部门id：{departmentId}，部门名称：{departmentName}", "/department/updateDepartment"),
        DEPARTMENT_DELETE_DEPARTMENT(15, "删除部门", "删除部门，部门id：{departmentId}，部门名称：{departmentName}", "/department/deleteDepartment"),
        POSITION_ADD_POSITION(16, "添加职位", "添加职位，职位id：{positionId}，职位名称：{positionName}", "/position/addPosition"),
        POSITION_UPDATE_POSITION(17, "修改职位", "修改职位，职位id：{positionId}，职位名称：{positionName}", "/position/updatePosition"),
        POSITION_DELETE_POSITION(18, "删除职位", "删除职位，职位id：{positionId}，职位名称：{positionName}", "/position/deletePosition"),
        GROUP_ADD_GROUP(19, "添加用户组", "添加用户组，用户组id：{groupId}，用户组名称：{groupName}", "/group/addGroup"),
        GROUP_UPDATE_GROUP(20, "修改用户组", "修改用户组，用户组id：{groupId}，用户组名称：{groupName}", "/group/updateGroup"),
        GROUP_DELETE_GROUP(21, "删除用户组", "删除用户组，用户组id：{groupId}，用户组名称：{groupName}", "/group/deleteGroup"),
        CHAT_GROUP_ADD_CHAT_GROUP(22, "创建群聊", "创建群聊，群聊id：{chatGroupId}，群聊名称：{chatGroupName}", "/chatGroup/addChatGroup"),
        CHAT_GROUP_UPDATE_CHAT_GROUP(23, "修改群聊", "修改群聊，群聊id：{chatGroupId}，群聊名称：{chatGroupName}", "/chatGroup/updateChatGroup"),
        CHAT_GROUP_DELETE_CHAT_GROUP(24, "删除群聊", "删除群聊，群聊id：{chatGroupId}，群聊名称：{chatGroupName}", "/chatGroup/deleteChatGroup"),
        CHAT_GROUP_ADD_CHAT_GROUP_IDENTITY(25, "添加群成员", "添加群成员，群聊id：{chatGroupId}，身份id：{identityId}", "/chatGroup/addChatGroupIdentity"),
        CHAT_GROUP_UPDATE_CHAT_GROUP_IDENTITY(26, "修改群成员", "修改群成员，群聊id：{chatGroupId}，身份id：{identityId}", "/chatGroup/updateChatGroupIdentity"),
        CHAT_GROUP_DELETE_CHAT_GROUP_IDENTITY(27, "删除群成员", "删除群成员，群聊id：{chatGroupId}，身份id：{identityId}", "/chatGroup/deleteChatGroupIdentity"),
        FRIEND_ADD_FRIEND(28, "添加好友", "添加好友，身份id：{identityId}，目标身份id：{targetIdentityId}", "/friend/addFriend"),
        FRIEND_UPDATE_FRIEND(29, "修改好友", "修改好友，身份id：{identityId}，目标身份id：{targetIdentityId}", "/friend/updateFriend"),
        FRIEND_DELETE_FRIEND(30, "删除好友", "删除好友，身份id：{identityId}，目标身份id：{targetIdentityId}", "/friend/deleteFriend"),
        ENTITY_ADD_ENTITY(31, "添加标签", "添加标签，标签id：{entityId}，标签名称：{entityName}", "/entity/addEntity"),
        ENTITY_UPDATE_ENTITY(32, "修改标签", "修改标签，标签id：{entityId}，标签名称：{entityName}", "/entity/updateEntity"),
        ENTITY_DELETE_ENTITY(33, "删除标签", "删除标签，标签id：{entityId}，标签名称：{entityName}", "/entity/deleteEntity"),
        IDENTITY_ADD_IDENTITY_RELATION(34, "添加身份关联关系", "添加身份关联关系，身份id：{identityId}，关联关系：{relationList}", "/identity/addIdentityRelation"),
        IDENTITY_UPDATE_IDENTITY_RELATION(35, "修改身份关联关系", "修改身份关联关系，身份id：{identityId}，关联关系：{relationList}", "/identity/updateIdentityRelation"),
        IDENTITY_DELETE_IDENTITY_RELATION(36, "删除身份关联关系", "删除身份关联关系，身份id：{identityId}，关联关系：{relationList}", "/identity/deleteIdentityRelation"),
        ENTITY_ADD_ENTITY_RELATION(37, "添加标签关系", "添加标签关系，标签id：{entityId}，目标标签id：{targetEntityId}", "/entity/addEntityRelation"),
        ENTITY_UPDATE_ENTITY_RELATION(38, "修改标签关系", "修改标签关系，标签id：{entityId}，目标标签id：{targetEntityId}", "/entity/updateEntityRelation"),
        ENTITY_DELETE_ENTITY_RELATION(39, "删除标签关系", "删除标签关系，标签id：{entityId}，目标标签id：{targetEntityId}", "/entity/deleteEntityRelation"),
        USER_ADD_USER_PRE(40, "添加预注册用户", "添加预注册用户，用户id：{preUserId}，用户名：{userName}", "/user/addUserPre"),
        USER_UPDATE_USER_PRE(41, "修改预注册用户", "修改预注册用户，用户id：{preUserId}，用户名：{userName}", "/user/updateUserPre"),
        USER_DELETE_USER_PRE(42, "删除预注册用户", "删除预注册用户，用户id：{preUserId}，用户名：{userName}", "/user/deleteUserPre"),
        IDENTITY_UPDATE_IDENTITY_LIST(43, "批量修改身份", "批量修改身份，{identityIds}", "/identity/updateIdentityList"),
        USER_UPDATE_USER_PRE_LIST(44, "批量修改预注册用户", "批量修改预注册用户，{preUserIds}", "/user/updateUserPreList"),
        IDENTITY_ADD_IDENTITY_LIST(45, "批量添加身份", "批量添加身份，{identityIds}", "/identity/addIdentityList"),
        ;

        private int type;
        private String opt;
        private String content;
        private String uri;

        private static Map<String, OptLog> optLogMap;

        public static OptLog getOptLog(String uri) {
            if (optLogMap == null) {
                optLogMap = new HashMap<>();
                for (OptLog optLog : OptLog.values()) {
                    optLogMap.put(optLog.getUri(), optLog);
                }
            }
            return optLogMap.get(uri);
        }
    }
}
