package com.microservice.user.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

public class TokenConstant {

    /**
     * redis 秒
     */
    public static final long USER_LOGIN_EXPIRE = 86400 * 30;


    //数据库 秒 30天
    public static final long USER_LOGIN_EXPIRE_MILLISECONDS = 24 * 60 * 60 * 30 ;

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum tokenStatus {
        LOGOUT(40000, "退出成功"),
        CHANGE_IDENTITY(40001, "切换公司身份"),
        LOGIN_ON_OTHER_DEVICE(40002, "您的账号在另一台设备登录。如非本人操作，则密码可能已泄露，建议修改密码或重新登录。"),
        CHANGE_EVENT(40003, "权限变动"),
        DELETE_IDENTITY(40004, "删除公司"),
        ;
        private Integer code;
        private String name;
    }
}
