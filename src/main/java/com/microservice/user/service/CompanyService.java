package com.microservice.user.service;

import com.microservice.user.constant.SystemConstant;
import com.microservice.user.constant.TokenConstant;
import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.*;
import com.microservice.user.entity.request.ms.company.CompanyKeyFilter;
import com.microservice.user.entity.request.ms.company.GetCompanyTreeWithPeopleNumRequest;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.exception.CompanyException;
import com.microservice.user.mapper.CompanyInfoMapper;
import com.microservice.user.mapper.IdentityCompanyRelationMapper;
import com.microservice.user.mapper.UserInfoPreMapper;
import com.microservice.user.util.RedisUtil;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.PropertyValues;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CompanyService {

    @Autowired
    private CompanyInfoMapper companyInfoMapper;
    @Autowired
    private CompanyDepartmentService companyDepartmentService;
    @Autowired
    private CompanyPositionService companyPositionService;
    @Autowired
    private IdentityCompanyService identityCompanyService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SystemService systemService;
    @Autowired
    private UserService userService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private IdentityCompanyRelationMapper identityCompanyRelationMapper;
    @Autowired
    private UserInfoPreMapper userInfoPreMapper;

    public CompanyInfoDAO addCompany(String companyName, Map<String, Object> extraContent, String appChannelId, String dataChannelId) {
        CompanyInfoDAO companyInfoDAO = new CompanyInfoDAO();
        companyInfoDAO.setCompanyId(StringUtil.createUuid());
        companyInfoDAO.setCompanyName(companyName);
        companyInfoDAO.setExtraContent(StringUtil.jsonEncode(extraContent));
        companyInfoDAO.setAppChannelId(appChannelId);
        companyInfoDAO.setDataChannelId(dataChannelId);
        return addCompany(companyInfoDAO);
    }

    public CompanyInfoDAO addCompany(CompanyInfoDAO companyInfoDAO) {
        //验证公司是否已存在
        CompanyInfoDAO checkCompany = getCompany(
                GetCompanyCondition.builder()
                        .companyName(companyInfoDAO.getCompanyName())
                        .dataChannelId(companyInfoDAO.getDataChannelId())
                        .build()
        );
        if (checkCompany != null) {
            throw new CompanyException("66008", "公司名称已存在");
        }
        if (StringUtil.isEmpty(companyInfoDAO.getCompanyId())) {
            companyInfoDAO.setCompanyId(StringUtil.createUuid());
        }
        if (companyInfoDAO.getCreateTime() == null) {
            companyInfoDAO.setCreateTime(new Date());
        }
        if (companyInfoDAO.getCreateTime() == null) {
            companyInfoDAO.setUpdateTime(new Date());
        }
        if (companyInfoMapper.addCompany(companyInfoDAO) <= 0) {
            throw new CompanyException("66001", "添加公司失败");
        }
        return companyInfoDAO;
    }

    public CompanyInfoDAO getCompany(String companyId) {
        return getCompany(GetCompanyCondition.builder()
                .companyId(companyId)
                .build());
    }

    public CompanyInfoDAO getCompany(GetCompanyCondition condition) {
        return companyInfoMapper.getCompany(condition);
    }

    public void updateCompany(CompanyInfoDAO companyInfoDAO) {
        //如果修改公司名称，验证公司名称是否已存在
        if (companyInfoDAO.getCompanyName() != null) {
            CompanyInfoDAO checkCompany = getCompany(
                    GetCompanyCondition.builder()
                            .companyName(companyInfoDAO.getCompanyName())
                            .dataChannelId(companyInfoDAO.getDataChannelId())
                            .build()
            );
            if (checkCompany != null && !checkCompany.getCompanyId().equals(companyInfoDAO.getCompanyId())) {
                throw new CompanyException("66009", "公司名称已存在");
            }
        }
        companyInfoDAO.setUpdateTime(new Date());
        if (companyInfoMapper.updateCompany(
                companyInfoDAO,
                GetCompanyCondition.builder()
                        .companyId(companyInfoDAO.getCompanyId())
                        .dataChannelId(companyInfoDAO.getDataChannelId())
                        .build()
        ) <= 0) {
            throw new CompanyException("66002", "更新公司失败");
        }
    }


    public void deleteCompany(String companyId, int isForce) {
        CompanyInfoDAO companyInfoDAO = getCompany(companyId);
        if (companyInfoDAO == null) {
            return;
        }
        //检查子公司
        CompanyInfoDAO child = companyInfoMapper.getCompany(GetCompanyCondition.builder()
                .parentId(companyId)
                .build());
        if (child != null) {
            if (isForce <= 0) {
                throw new CompanyException("66003", "公司下存在子公司，不能删除");
            }
            companyInfoMapper.deleteCompany(GetCompanyCondition.builder()
                    .parentId(companyId)
                    .build());
        }
        //检查部门
        List<CompanyDepartmentRelationDAO> cdrList = companyDepartmentService.getCDRListByCompanyId(companyId);
        if (!cdrList.isEmpty()) {
            if (isForce <= 0) {
                throw new CompanyException("66004", "公司下存在部门，不能删除");
            }
            companyDepartmentService.deleteCDRByCompanyId(companyId);
        }
        //检查职位
        List<CompanyPositionRelationDAO> cprList = companyPositionService.getCPRListByCompanyId(companyId);
        if (!cprList.isEmpty()) {
            if (isForce <= 0) {
                throw new CompanyException("66005", "公司下存在职位，不能删除");
            }
            companyPositionService.deleteCPRByCompanyId(companyId);
        }
        //检查用户
        List<IdentityCompanyRelationDAO> icrList = identityCompanyService.getICRListByCompanyId(companyId);
        if (!icrList.isEmpty()) {
            if (isForce <= 0) {
                throw new CompanyException("66006", "公司下存在用户，不能删除");
            }
            companyPositionService.deleteCPRByCompanyId(companyId);
        }
        //删除公司
        if (companyInfoMapper.deleteCompany(GetCompanyCondition.builder().companyId(companyId).build()) <= 0) {
            throw new CompanyException("66007", "删除失败");
        }
        //删除公司下所有用户的token
        List<TokenInfoDAO> tokenInfoDAOList = tokenService.getTokenList(
                GetTokenCondition.builder()
                        .identityIdList(icrList.stream().map(IdentityCompanyRelationDAO::getIdentityId).collect(Collectors.toList()))
                        .build()
        );
        tokenService.updateToken(
                TokenConstant.tokenStatus.DELETE_IDENTITY.getCode().toString(),
                GetTokenCondition.builder()
                        .tokenIdList(tokenInfoDAOList.stream().map(TokenInfoDAO::getTokenId).collect(Collectors.toList()))
                        .build()
        );
        //清除登录缓存
        RedisUtil.del(tokenInfoDAOList.stream().map(tokenInfoDAO -> SystemConstant.REDIS_KEY_USER_TOKEN + tokenInfoDAO.getToken()).collect(Collectors.toList()));
    }

    public TreeItemDTO createCompanyTree(CompanyDTO company,
                                         List<CompanyDTO> subCompanyList,
                                         List<DepartmentDTO> departmentList,
                                         List<PositionDTO> positionList,
                                         List<ICRWithIdentityDTO> identityCompanyList,
                                         List<IDRWithIdentityDTO> identityDepartmentList,
                                         List<IPRWithIdentityDTO> identityPositionList) {
        Set<String> userIdSet = new HashSet<>();
        Map<String, DepartmentDTO> departmentId2DepartmentMap = departmentList.stream().collect(Collectors.toMap(DepartmentDTO::getDepartmentId, departmentDTO -> departmentDTO));
        Map<String, PositionDTO> positionId2PositionMap = positionList.stream().collect(Collectors.toMap(PositionDTO::getPositionId, positionDTO -> positionDTO));
        //用户id和公司用户关系，计算无部门和无职位的用户
        Map<String, IdentityDTO> identityId2IdentityMap = new HashMap<>();
        for (ICRWithIdentityDTO identity : identityCompanyList) {
            String key = identity.getIdentityId() + "_" + identity.getCompanyId();
            identityId2IdentityMap.put(key, identity);
            //收集用户id
            userIdSet.add(identity.getUserId());
        }
        //构造部门和用户map
        Map<String, List<IdentityDTO>> departmentId2IdentityListMap = new HashMap<>();
        for (IDRWithIdentityDTO identity : identityDepartmentList) {
            List<IdentityDTO> identityList = departmentId2IdentityListMap.getOrDefault(identity.getDepartmentId(), new LinkedList<>());
            identityList.add(identity);
            departmentId2IdentityListMap.put(identity.getDepartmentId(), identityList);
            //删除公司下用户
            DepartmentDTO departmentDTO = departmentId2DepartmentMap.get(identity.getDepartmentId());
            if (departmentDTO != null) {
                identityId2IdentityMap.remove(identity.getIdentityId() + "_" + departmentDTO.getCompanyId());
            }
            //收集用户id
            userIdSet.add(identity.getUserId());
        }
        //构造职位和用户map
        Map<String, List<IdentityDTO>> positionId2IdentityListMap = new HashMap<>();
        for (IPRWithIdentityDTO identity : identityPositionList) {
            List<IdentityDTO> identityList = positionId2IdentityListMap.getOrDefault(identity.getPositionId(), new LinkedList<>());
            identityList.add(identity);
            positionId2IdentityListMap.put(identity.getPositionId(), identityList);
            //删除公司下用户
            PositionDTO positionDTO = positionId2PositionMap.get(identity.getPositionId());
            if (positionDTO != null) {
                identityId2IdentityMap.remove(identity.getIdentityId() + "_" + positionDTO.getCompanyId());
            }
            //收集用户id
            userIdSet.add(identity.getUserId());
        }
        //构造公司id和部门map
        Map<String, List<DepartmentDTO>> companyId2DepartmentListMap = departmentList.stream().collect(Collectors.groupingBy(DepartmentDTO::getCompanyId));
        //构造公司id和职位map
        Map<String, List<PositionDTO>> companyId2PositionListMap = positionList.stream().collect(Collectors.groupingBy(PositionDTO::getCompanyId));
        //构造公司下无部门和职位用户
        Map<String, List<IdentityDTO>> companyId2FreeUserListMap = new HashMap<>();
        for (Map.Entry<String, IdentityDTO> entry : identityId2IdentityMap.entrySet()) {
            String[] keyArr = entry.getKey().split("_");
            String companyId = keyArr[1];
            List<IdentityDTO> identityList = companyId2FreeUserListMap.getOrDefault(companyId, new LinkedList<>());
            identityList.add(entry.getValue());
            companyId2FreeUserListMap.put(companyId, identityList);
        }
        //获取用户信息
        List<UserInfoDAO> userInfoDAOList = userService.getUserList(GetUserCondition.builder().userIdList(new ArrayList<>(userIdSet)).build());
        Map<String, UserInfoDAO> userId2UserMap = userInfoDAOList.stream().collect(Collectors.toMap(UserInfoDAO::getUserId, dao -> dao, (dao1, dao2) -> dao1));
        //创建公司树形结构
        TreeItemDTO treeItemDTO = createCompanyDTO(
                company,
                companyId2DepartmentListMap.getOrDefault(company.getCompanyId(), new ArrayList<>()),
                departmentId2IdentityListMap,
                companyId2PositionListMap.getOrDefault(company.getCompanyId(), new ArrayList<>()),
                positionId2IdentityListMap,
                companyId2FreeUserListMap.getOrDefault(company.getCompanyId(), new ArrayList<>()),
                userId2UserMap
        );
        for (CompanyDTO subCompany : subCompanyList) {
            if (subCompany.getParentId().equals(company.getCompanyId())) {
                List<TreeItemDTO> children = treeItemDTO.getChildren();
                children.add(getCompanyDsf(
                        subCompany,
                        subCompanyList,
                        companyId2DepartmentListMap.getOrDefault(company.getCompanyId(), new ArrayList<>()),
                        departmentId2IdentityListMap,
                        companyId2PositionListMap.getOrDefault(company.getCompanyId(), new ArrayList<>()),
                        positionId2IdentityListMap,
                        companyId2FreeUserListMap.getOrDefault(company.getCompanyId(), new ArrayList<>()),
                        userId2UserMap
                ));
                treeItemDTO.setChildren(children);
            }
        }
        return treeItemDTO;
    }

    public TreeItemDTO getCompanyDsf(CompanyDTO companyDTO, List<CompanyDTO> subCompanyList, List<DepartmentDTO> departmentDTOList, Map<String, List<IdentityDTO>> departmentId2IdentityListMap, List<PositionDTO> positionDTOList, Map<String, List<IdentityDTO>> positionId2IdentityListMap, List<IdentityDTO> freeUserList, Map<String, UserInfoDAO> userId2UserMap) {
        TreeItemDTO treeItemDTO = createCompanyDTO(companyDTO, departmentDTOList, departmentId2IdentityListMap, positionDTOList, positionId2IdentityListMap, freeUserList, userId2UserMap);
        for (CompanyDTO company : subCompanyList) {
            if (company.getParentId() != null && company.getParentId().equals(companyDTO.getCompanyId())) {
                List<TreeItemDTO> children = treeItemDTO.getChildren();
                children.add(getCompanyDsf(company, subCompanyList, departmentDTOList, departmentId2IdentityListMap, positionDTOList, positionId2IdentityListMap, freeUserList, userId2UserMap));
                treeItemDTO.setChildren(children);
            }
        }
        return treeItemDTO;
    }

    public TreeItemDTO getDepartmentDsf(DepartmentDTO departmentDTO, List<DepartmentDTO> departmentDTOList, Map<String, List<IdentityDTO>> departmentId2IdentityListMap, List<PositionDTO> positionDTOList, Map<String, List<IdentityDTO>> positionId2IdentityListMap, Map<String, UserInfoDAO> userId2UserMap) {
        TreeItemDTO treeItemDTO = createDepartmentDTO(departmentDTO, departmentId2IdentityListMap, positionDTOList, positionId2IdentityListMap, userId2UserMap);
        for (DepartmentDTO department : departmentDTOList) {
            if (department.getParentId() != null && department.getParentId().equals(departmentDTO.getDepartmentId())) {
                List<TreeItemDTO> children = treeItemDTO.getChildren();
                children.add(getDepartmentDsf(department, departmentDTOList, departmentId2IdentityListMap, positionDTOList, positionId2IdentityListMap, userId2UserMap));
                treeItemDTO.setChildren(children);
            }
        }
        return treeItemDTO;
    }

    public TreeItemDTO getPositionDsf(PositionDTO positionDTO, List<PositionDTO> positionDTOList, Map<String, List<IdentityDTO>> positionId2IdentityListMap, Map<String, UserInfoDAO> userId2UserMap) {
        TreeItemDTO treeItemDTO = createPositionDTO(positionDTO, positionId2IdentityListMap, userId2UserMap);
        for (PositionDTO position : positionDTOList) {
            if (position.getParentId() != null && position.getParentId().equals(positionDTO.getPositionId())) {
                List<TreeItemDTO> children = treeItemDTO.getChildren();
                children.add(getPositionDsf(position, positionDTOList, positionId2IdentityListMap, userId2UserMap));
                treeItemDTO.setChildren(children);
            }
        }
        return treeItemDTO;
    }

    public TreeItemDTO createCompanyDTO(CompanyDTO companyDTO, List<DepartmentDTO> departmentDTOList, Map<String, List<IdentityDTO>> departmentId2IdentityListMap, List<PositionDTO> positionDTOList, Map<String, List<IdentityDTO>> positionId2IdentityListMap, List<IdentityDTO> freeUserList, Map<String, UserInfoDAO> userId2UserMap) {
        TreeItemDTO treeItemDTO = TreeItemDTO.builder()
                .id(companyDTO.getCompanyId())
                .name(companyDTO.getCompanyName())
                .type("company")
                .sort(companyDTO.getSort())
                .extraContent(companyDTO.getExtraContent())
                .build();
        List<TreeItemDTO> children = new LinkedList<>();
        for (DepartmentDTO department : departmentDTOList) {
            if (StringUtil.isEmpty(department.getParentId())) {
                children.add(getDepartmentDsf(department, departmentDTOList, departmentId2IdentityListMap, positionDTOList, positionId2IdentityListMap, userId2UserMap));
            }
        }
        for (PositionDTO position : positionDTOList) {
            if (StringUtil.isEmpty(position.getParentId()) && StringUtil.isEmpty(position.getDepartmentId())) {
                children.add(getPositionDsf(position, positionDTOList, positionId2IdentityListMap, userId2UserMap));
            }
        }
        //不属于任何部门的用户
        if (!freeUserList.isEmpty()) {
            children.addAll(freeUserList.stream().map(
                    identityDTO -> createIdentityTreeItem(identityDTO, userId2UserMap.get(identityDTO.getUserId()))
            ).collect(Collectors.toList()));
        }
        treeItemDTO.setChildren(children);
        return treeItemDTO;
    }


    public TreeItemDTO createDepartmentDTO(DepartmentDTO departmentDTO, Map<String, List<IdentityDTO>> departmentId2IdentityListMap, List<PositionDTO> positionDTOList, Map<String, List<IdentityDTO>> positionId2IdentityListMap, Map<String, UserInfoDAO> userId2UserMap) {
        TreeItemDTO treeItemDTO = TreeItemDTO.builder()
                .id(departmentDTO.getDepartmentId())
                .name(departmentDTO.getDepartmentName())
                .type("department")
                .extraContent(departmentDTO.getExtraContent())
                .sort(departmentDTO.getSort())
                .build();
        List<TreeItemDTO> children = new LinkedList<>();
        for (PositionDTO position : positionDTOList) {
            if (position.getDepartmentId().equals(departmentDTO.getDepartmentId())) {
                children.add(getPositionDsf(position, positionDTOList, positionId2IdentityListMap, userId2UserMap));
            }
        }
        //部门下的用户
        List<IdentityDTO> departmentIdentityList = departmentId2IdentityListMap.getOrDefault(departmentDTO.getDepartmentId(), new ArrayList<>());
        if (!departmentIdentityList.isEmpty()) {
            children.addAll(departmentIdentityList.stream().map(
                    identityDTO -> createIdentityTreeItem(identityDTO, userId2UserMap.get(identityDTO.getUserId()))
            ).collect(Collectors.toList()));
        }
        treeItemDTO.setChildren(children);
        return treeItemDTO;
    }

    public TreeItemDTO createPositionDTO(PositionDTO positionDTO, Map<String, List<IdentityDTO>> positionId2IdentityListMap, Map<String, UserInfoDAO> userId2UserMap) {
        TreeItemDTO treeItemDTO = TreeItemDTO.builder()
                .id(positionDTO.getPositionId())
                .name(positionDTO.getPositionName())
                .type("position")
                .extraContent(positionDTO.getExtraContent())
                .sort(positionDTO.getSort())
                .build();
        List<TreeItemDTO> children = new LinkedList<>();
        List<IdentityDTO> positionIdentityList = positionId2IdentityListMap.getOrDefault(positionDTO.getPositionId(), new ArrayList<>());
        if (!positionIdentityList.isEmpty()) {
            for (IdentityDTO identity : positionIdentityList) {
                if (StringUtil.isEmpty(identity.getIdentityId())) {
                    continue;
                }
                children.add(createIdentityTreeItem(identity, userId2UserMap.get(identity.getUserId())));
            }
        }
        treeItemDTO.setChildren(children);
        return treeItemDTO;
    }

    public TreeItemDTO createIdentityTreeItem(IdentityDTO identity, UserInfoDAO user) {
        Map<String, Object> extraContent = new HashMap<>();
        if (identity.getExtraContent() != null) {
            extraContent.putAll(identity.getExtraContent());
        }
        Map<String, Object> uccExtraContent = new HashMap<>();
        if (user != null) {
            if (!StringUtil.isEmpty(user.getExtraContent())) {
                extraContent.putAll(StringUtil.getMapFromString(user.getExtraContent()));
            }
            extraContent.put("userName", user.getUserName());
            if (!StringUtil.isEmpty(user.getUccExtraContent())) {
                uccExtraContent = StringUtil.getMapFromString(user.getUccExtraContent());
            }
        }
        extraContent.put("identityId", identity.getIdentityId());
        extraContent.put("userId", identity.getUserId());
        String identityId = identity.getIdentityId();
        String userId = null;
        if (user != null && user.getUserId() != null) {
            userId = user.getUserId();
        }
        return TreeItemDTO.builder()
                .id(identity.getIdentityId())
                .name(identity.getIdentityName())
                .type("identity")
                .sort(identity.getSort())
                .extraContent(extraContent)
                .identityId(identityId)
                .userId(userId)
                .uccExtraContent(uccExtraContent)
                .build();
    }

    public List<CompanyInfoDAO> getCompanyList(GetCompanyCondition condition, int start, int limit) {
        if (condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty()) {
            return new ArrayList<>();
        }
        return companyInfoMapper.getCompanyList(condition, start, limit);
    }

    public List<CompanyInfoDAO> getAllDescendantCompanies(GetCompanyCondition req) {
        return companyInfoMapper.getAllDescendantCompanies(req);
    }

    public List<CompanyInfoDAO> getCompanyList(GetCompanyCondition condition) {
        return getCompanyList(condition, 0, 0);
    }

    public List<CompanyInfoDAO> getCompanySonListByIds(GetCompanyCondition condition) {
        return getCompanyList(condition, 0, 0);
    }

    public List<CompanyInfoDAO> getCompanyListByIds(List<String> companyIds) {
        return companyInfoMapper.getCompanyListByIds(companyIds);
    }

    public List<CompanyInfoDAO> getCompanyList(GetCompanyCondition condition, List<String> columnList, int start, int limit) {
        if (condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty()) {
            return new ArrayList<>();
        }
        if (columnList != null && !columnList.isEmpty()) {
            List<Map<String, Object>> newCompanyMapList = new ArrayList<>();
            List<Map<String, Object>> companyMapList = companyInfoMapper.getCompanyMapList(condition, columnList, start, limit);
            for (Map<String, Object> companyMap : companyMapList) {
                Map<String, Object> newCompanyMap = new HashMap<>();
                BeanUtils.copyProperties(companyMap, newCompanyMap);
                Map<String, Object> companyExtraContent = new HashMap<>();
                for (Map.Entry<String, Object> entry : companyMap.entrySet()) {
                    if (entry.getKey().startsWith("extra_content")) {
                        companyExtraContent = StringUtil.getMapFromString(StringUtil.jsonEncode(entry.getValue()));
                    } else {
                        newCompanyMap.put(StringUtil.underscoreToCamel(entry.getKey()), entry.getValue());
                    }
                }
                newCompanyMap.put("extraContent", StringUtil.jsonEncode(companyExtraContent));
                newCompanyMapList.add(newCompanyMap);
            }
            return newCompanyMapList.stream().map(map -> StringUtil.jsonDecode(StringUtil.jsonEncode(map), CompanyInfoDAO.class)).collect(Collectors.toList());
        }
        return companyInfoMapper.getCompanyList(condition, start, limit);
    }

    public int getCompanyCount(GetCompanyCondition condition) {
        if (condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty()) {
            return 0;
        }
        return companyInfoMapper.getCompanyCount(condition);
    }

    public int deleteCompany(GetCompanyCondition condition) {
        return companyInfoMapper.deleteCompany(condition);
    }

    public void clearCompanyCache(String appChannelId, String companyId) {
        //获取公司下所有身份
        List<ICRWithIdentityDAO> icrList = identityCompanyService.getICRWithIdentityList(GetIdentityCompanyCondition.builder()
                .companyId(companyId)
                .build());
        if (!icrList.isEmpty()) {
            RedisUtil.del(icrList.stream().map(dao -> systemService.getIdentityEntityRedisKey(appChannelId, dao.getIdentityId())).collect(Collectors.toList()));
            RedisUtil.del(icrList.stream().map(dao -> systemService.getUserEntityRedisKey(appChannelId, dao.getUserId())).collect(Collectors.toList()));
        }
    }

    /**
     * 获取公司树，带人数
     *
     * @param request
     * @return com.microservice.user.entity.response.ms.MsResponse<com.microservice.user.entity.dto.ms.TreeItemV2DTO>
     * <AUTHOR>
     * @date 2024/12/12 16:04
     **/
    public MsResponse<TreeItemV2DTO> getCompanyTreeWithPeopleNum(GetCompanyTreeWithPeopleNumRequest request) {
        CompanyInfoDAO companyInfoDAO = this.getCompany(request.getCompanyId());
        if (companyInfoDAO == null) {
            return MsResponse.fail("50001", "公司不存在");
        }
        CompanyDTO companyDTO = CompanyDTO.create(companyInfoDAO);
        // 子公司概念已废弃
        List<String> companyIdList = Collections.singletonList(request.getCompanyId());
        //部门列表（部门和公司关系）
        List<DepartmentDTO> departmentList = departmentService.getDepartmentList(GetDepartmentCondition.builder()
                .companyIdList(companyIdList)
                .build());
        Map<String, List<DepartmentDTO>> departmentMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(departmentList)) {
            departmentMap = departmentList.stream().collect(Collectors.groupingBy(v -> v.getCompanyId() + "_" + (StringUtils.isEmpty(v.getParentId()) ? "" : v.getParentId())));
        }
        // 已加入用户数
        List<CompanyDepartmentPeopleNumDTO> alreadyJoinedList = identityCompanyRelationMapper.getCompanyDepartmentPeopleNum(companyIdList);
        Map<String, List<String>> alreadyJoinedMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(alreadyJoinedList)) {
            alreadyJoinedMap = alreadyJoinedList.stream().collect(Collectors.toMap(v -> v.getCompanyId() + "_" + (StringUtils.isEmpty(v.getDepartmentId()) ? "" : v.getDepartmentId()), CompanyDepartmentPeopleNumDTO::getUserIdList));
        }
        // 未加入用户数
        List<CompanyDepartmentPeopleNumDTO> preJoinedList = userInfoPreMapper.getCompanyDepartmentPeopleNum(companyIdList);
        Map<String, List<String>> preJoinedMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(alreadyJoinedList)) {
            preJoinedMap = preJoinedList.stream().collect(Collectors.toMap(v -> v.getCompanyId() + "_" + (StringUtils.isEmpty(v.getDepartmentId()) ? "" : v.getDepartmentId()), CompanyDepartmentPeopleNumDTO::getUserIdList));
        }
        // 生成树形结构
        TreeItemV2DTO treeItemV2DTO = getCompanyItem(companyDTO, departmentMap, alreadyJoinedMap, preJoinedMap);
        return MsResponse.success(treeItemV2DTO);
    }

    /**
     * 获取公司节点
     *
     * @param companyDTO
     * @param departmentMap
     * @param alreadyJoinedMap
     * @param preJoinedMap
     * @return com.microservice.user.entity.dto.ms.TreeItemV2DTO
     * <AUTHOR>
     * @date 2024/12/13 15:55
     **/
    public TreeItemV2DTO getCompanyItem(CompanyDTO companyDTO
            , Map<String, List<DepartmentDTO>> departmentMap
            , Map<String, List<String>> alreadyJoinedMap
            , Map<String, List<String>> preJoinedMap) {
        TreeItemV2DTO treeItemV2DTO = TreeItemV2DTO.builder()
                .id(companyDTO.getCompanyId())
                .name(companyDTO.getCompanyName())
                .extraContent(companyDTO.getExtraContent())
                .type("company")
                .sort(companyDTO.getSort())
                .currentAlreadyJoinedCount(getListSize(alreadyJoinedMap.get(companyDTO.getCompanyId() + "_"))
                        + getListSize(alreadyJoinedMap.get(companyDTO.getCompanyId() + "_" + companyDTO.getCompanyId())))
                .currentPreJoinedCount(getListSize(preJoinedMap.get(companyDTO.getCompanyId() + "_"))
                        + getListSize(preJoinedMap.get(companyDTO.getCompanyId() + "_" + companyDTO.getCompanyId())))
                .build();
        List<DepartmentDTO> departmentDTOList = departmentMap.get(companyDTO.getCompanyId() + "_");
        if (CollectionUtils.isEmpty(departmentDTOList)) {
            treeItemV2DTO.setAlreadyJoinedCount(treeItemV2DTO.getCurrentAlreadyJoinedCount());
            treeItemV2DTO.setPreJoinedCount(treeItemV2DTO.getCurrentPreJoinedCount());
            treeItemV2DTO.setTotalCount(treeItemV2DTO.getAlreadyJoinedCount() + treeItemV2DTO.getPreJoinedCount());
            return treeItemV2DTO;
        }
        List<TreeItemV2DTO> departmentItemList = new ArrayList<>();
        List<String> alreadyJoinedList = new ArrayList<>();
        List<String> preJoinedList = new ArrayList<>();
        alreadyJoinedList.addAll(alreadyJoinedMap.getOrDefault(companyDTO.getCompanyId() + "_", Collections.emptyList()));
        alreadyJoinedList.addAll(alreadyJoinedMap.getOrDefault(companyDTO.getCompanyId() + "_" + companyDTO.getCompanyId(), Collections.emptyList()));
        preJoinedList.addAll(preJoinedMap.getOrDefault(companyDTO.getCompanyId() + "_", Collections.emptyList()));
        preJoinedList.addAll(preJoinedMap.getOrDefault(companyDTO.getCompanyId() + "_" + companyDTO.getCompanyId(), Collections.emptyList()));
        for (DepartmentDTO departmentDTO : departmentDTOList) {
            TreeItemV2DTO departmentItem = getCompanyDepartmentItem(departmentDTO, departmentMap, alreadyJoinedMap, preJoinedMap);
            if (Objects.nonNull(departmentItem) && !CollectionUtils.isEmpty(departmentItem.getAlreadyJoinedList())) {
                alreadyJoinedList.addAll(departmentItem.getAlreadyJoinedList());
            }
            if (Objects.nonNull(departmentItem) && !CollectionUtils.isEmpty(departmentItem.getPreJoinedList())) {
                preJoinedList.addAll(departmentItem.getPreJoinedList());
            }
            departmentItemList.add(departmentItem);
        }
        treeItemV2DTO.setAlreadyJoinedList(addList(alreadyJoinedList, alreadyJoinedMap.get(companyDTO.getCompanyId() + "_")));
        treeItemV2DTO.setPreJoinedList(addList(preJoinedList, preJoinedMap.get(companyDTO.getCompanyId() + "_")));
        treeItemV2DTO.setAlreadyJoinedCount(alreadyJoinedList.stream().distinct().collect(Collectors.toList()).size());
        treeItemV2DTO.setPreJoinedCount(preJoinedList.stream().distinct().collect(Collectors.toList()).size());
        treeItemV2DTO.setTotalCount(treeItemV2DTO.getAlreadyJoinedCount() + treeItemV2DTO.getPreJoinedCount());
        treeItemV2DTO.setChildren(departmentItemList);
        return treeItemV2DTO;
    }

    /**
     * 获取部门节点
     *
     * @param departmentDTO
     * @param departmentMap
     * @param alreadyJoinedMap
     * @param preJoinedMap
     * @return com.microservice.user.entity.dto.ms.TreeItemV2DTO
     * <AUTHOR>
     * @date 2024/12/13 15:55
     **/
    public TreeItemV2DTO getCompanyDepartmentItem(DepartmentDTO departmentDTO
            , Map<String, List<DepartmentDTO>> departmentMap
            , Map<String, List<String>> alreadyJoinedMap
            , Map<String, List<String>> preJoinedMap) {
        TreeItemV2DTO treeItemV2DTO = TreeItemV2DTO.builder()
                .id(departmentDTO.getDepartmentId())
                .name(departmentDTO.getDepartmentName())
                .extraContent(departmentDTO.getExtraContent())
                .type("department")
                .sort(departmentDTO.getSort())
                .currentAlreadyJoinedCount(getListSize(alreadyJoinedMap.get(departmentDTO.getCompanyId() + "_" + departmentDTO.getDepartmentId())))
                .currentPreJoinedCount(getListSize(preJoinedMap.get(departmentDTO.getCompanyId() + "_" + departmentDTO.getDepartmentId())))
                .build();
        List<DepartmentDTO> departmentDTOList = departmentMap.get(departmentDTO.getCompanyId() + "_" + departmentDTO.getDepartmentId());
        if (CollectionUtils.isEmpty(departmentDTOList)) {
            treeItemV2DTO.setAlreadyJoinedList(alreadyJoinedMap.getOrDefault(departmentDTO.getCompanyId() + "_" + departmentDTO.getDepartmentId(), Collections.emptyList()));
            treeItemV2DTO.setPreJoinedList(preJoinedMap.getOrDefault(departmentDTO.getCompanyId() + "_" + departmentDTO.getDepartmentId(), Collections.emptyList()));
            treeItemV2DTO.setAlreadyJoinedCount(treeItemV2DTO.getCurrentAlreadyJoinedCount());
            treeItemV2DTO.setPreJoinedCount(treeItemV2DTO.getCurrentPreJoinedCount());
            treeItemV2DTO.setTotalCount(treeItemV2DTO.getCurrentAlreadyJoinedCount() + treeItemV2DTO.getCurrentPreJoinedCount());
            return treeItemV2DTO;
        }
        List<String> alreadyJoinedList = new ArrayList<>();
        List<String> preJoinedList = new ArrayList<>();
        List<TreeItemV2DTO> departmentItemList = new ArrayList<>();
        for (DepartmentDTO department : departmentDTOList) {
            TreeItemV2DTO departmentItem = getCompanyDepartmentItem(department, departmentMap, alreadyJoinedMap, preJoinedMap);
            if (Objects.nonNull(departmentItem) && !CollectionUtils.isEmpty(departmentItem.getAlreadyJoinedList())) {
                alreadyJoinedList.addAll(departmentItem.getAlreadyJoinedList());
            }
            if (Objects.nonNull(departmentItem) && !CollectionUtils.isEmpty(departmentItem.getPreJoinedList())) {
                preJoinedList.addAll(departmentItem.getPreJoinedList());
            }
            departmentItemList.add(departmentItem);
        }
        treeItemV2DTO.setAlreadyJoinedList(addList(alreadyJoinedList, alreadyJoinedMap.get(departmentDTO.getCompanyId() + "_" + departmentDTO.getDepartmentId())));
        treeItemV2DTO.setPreJoinedList(addList(preJoinedList, preJoinedMap.get(departmentDTO.getCompanyId() + "_" + departmentDTO.getDepartmentId())));
        treeItemV2DTO.setAlreadyJoinedCount(treeItemV2DTO.getAlreadyJoinedList().stream().distinct().collect(Collectors.toList()).size());
        treeItemV2DTO.setPreJoinedCount(treeItemV2DTO.getPreJoinedList().stream().distinct().collect(Collectors.toList()).size());
        treeItemV2DTO.setTotalCount(treeItemV2DTO.getAlreadyJoinedCount() + treeItemV2DTO.getPreJoinedCount());
        treeItemV2DTO.setChildren(departmentItemList);
        return treeItemV2DTO;
    }

    /**
     * 获取list数量
     *
     * @param list
     * @return int
     * <AUTHOR>
     * @date 2024/12/23 11:26
     **/
    private <E> int getListSize(List<E> list) {
        return CollectionUtils.isEmpty(list) ? 0 : list.size();
    }

    /**
     * 合并列表
     *
     * @param one
     * @param two
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/12/23 14:22
     **/
    private List<String> addList(List<String> one, List<String> two) {
        if (!CollectionUtils.isEmpty(one) && !CollectionUtils.isEmpty(two)) {
            one.addAll(two);
            return one.stream().distinct().collect(Collectors.toList());
        } else if (!CollectionUtils.isEmpty(one) && CollectionUtils.isEmpty(two)) {
            return one;
        } else if (CollectionUtils.isEmpty(one) && !CollectionUtils.isEmpty(two)) {
            return two;
        } else {
            return Collections.emptyList();
        }
    }

    public List<CompanyDTO> doCompanyKeyFilter(List<CompanyDTO> companyList, CompanyKeyFilter companyKeyFilter) {
        if (companyList != null) {
            for (CompanyDTO company : companyList) {
                doCompanyKeyFilter(company, companyKeyFilter);
            }
        }
        return companyList;
    }

    public CompanyDTO doCompanyKeyFilter(CompanyDTO company, CompanyKeyFilter companyKeyFilter) {
        //过滤扩展字段的key
        if (companyKeyFilter.getExtraContentKeyList() != null && !companyKeyFilter.getExtraContentKeyList().isEmpty()) {
            Map<String, Object> newExtraContent = new HashMap<>();
            for (Map.Entry<String, Object> entry : company.getExtraContent().entrySet()) {
                if (companyKeyFilter.getExtraContentKeyList().contains(entry.getKey())) {
                    newExtraContent.put(entry.getKey(), entry.getValue());
                }
            }
            company.setExtraContent(newExtraContent);
        }
        return company;
    }

}

