package com.microservice.user.service;

import com.microservice.user.common.Common;
import com.microservice.user.constant.SystemConstant;
import com.microservice.user.constant.TokenConstant;
import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.*;
import com.microservice.user.entity.dto.ms.identity.IdentityByCompanyDTO;
import com.microservice.user.entity.request.ms.identity.IdentityKeyFilter;
import com.microservice.user.entity.request.ms.item.RelationItem;
import com.microservice.user.exception.IdentityException;
import com.microservice.user.mapper.IdentityInfoMapper;
import com.microservice.user.util.RedisUtil;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class IdentityService {

    @Autowired
    private TokenService tokenService;
    @Autowired
    private IdentityInfoMapper identityInfoMapper;
    @Autowired
    private IdentityCompanyService identityCompanyService;
    @Autowired
    private IdentityDepartmentService identityDepartmentService;
    @Autowired
    private IdentityPositionService identityPositionService;
    @Autowired
    private IdentityGroupService identityGroupService;
    @Autowired
    private GroupEventService groupEventService;
    @Autowired
    private IdentityEntityService identityEntityService;
    @Autowired
    private SystemService systemService;

    public List<IdentityDTO> getIdentityList(GetIdentityCondition condition, int start, int limit) {
        if ((condition.getUserIdList() != null && condition.getUserIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return identityInfoMapper.getIdentityList(condition, start, limit).stream()
                .sorted(Comparator.comparing(IdentityInfoDAO::getCreateTime))
                .map(IdentityDTO::create)
                .collect(Collectors.toList());
    }

    public List<IdentityDTO> getIdentityList(GetIdentityCondition condition) {
        if ((condition.getUserIdList() != null && condition.getUserIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return identityInfoMapper.getIdentityList(condition, 0, 0).stream()
                .sorted(Comparator.comparing(IdentityInfoDAO::getCreateTime))
                .map(IdentityDTO::create)
                .collect(Collectors.toList());
    }

    public Integer getIdentityCount(GetIdentityCondition condition) {
        if ((condition.getUserIdList() != null && condition.getUserIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return identityInfoMapper.getIdentityCount(condition);
    }

    /**
     * 添加一个用户
     *
     * @param userId
     * @param identityId
     * @param identityName
     * @param extraContent
     * @param appChannelId
     * @param dataChannelId
     * @return
     */
    public IdentityInfoDAO addIdentity(String userId, String identityId, String identityName, Map<String, Object> extraContent,
                                       String appChannelId, String dataChannelId) {

        if (!StringUtil.isEmpty(identityId)) {
            IdentityInfoDAO identityInfoDAO = getIdentity(identityId);
            if (identityInfoDAO != null) {
                throw new IdentityException("61007", "身份已存在");
            }
        }
        if (extraContent == null || extraContent.isEmpty()) {
            extraContent = new HashMap<>() {{
                put("identityName", identityName);
            }};
        }
        IdentityInfoDAO identityInfoDAO = IdentityInfoDAO.builder()
                .identityId(StringUtil.isEmpty(identityId) ? StringUtil.createUuid() : identityId)
                .identityName(identityName)
                .userId(userId)
                .extraContent(StringUtil.jsonEncode(extraContent))
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build();
        if (identityInfoMapper.addIdentity(identityInfoDAO) <= 0) {
            throw new IdentityException("61001", "添加身份失败");
        }
        return identityInfoDAO;
    }

    public IdentityInfoDAO addIdentity(String userId, String identityName, Map<String, Object> extraContent,
                                       String appChannelId, String dataChannelId) {
        return addIdentity(userId, StringUtil.createUuid(), identityName, extraContent, appChannelId, dataChannelId);
    }

    public void deleteIdentity(String identityId) {
        IdentityInfoDAO identityInfoDAO = identityInfoMapper.getIdentity(GetIdentityCondition.builder().identityId(identityId).build());
        if (identityInfoDAO == null) {
            throw new IdentityException("61002", "身份不存在");
        }
//        if (identityInfoDAO.getIdentityId().equals(identityInfoDAO.getUserId())) {
//            throw new IdentityException("61006", "不能删除默认身份");
//        }
        if (deleteIdentity(new ArrayList<>() {{
            add(identityId);
        }}) <= 0) {
            throw new IdentityException("61003", "删除身份失败");
        }
    }

    public int deleteIdentity(List<String> identityIdList) {
        if (CollectionUtils.isEmpty(identityIdList)) {
            return 0;
        }
        //删除身份和公司关系
        identityCompanyService.deleteICR(GetIdentityCompanyCondition.builder().identityIdList(identityIdList).build());
        //删除身份和部门关系
        identityDepartmentService.deleteIDR(GetIdentityDepartmentCondition.builder().identityIdList(identityIdList).build());
        //删除身份和职位关系
        identityPositionService.deleteIPR(GetIdentityPositionCondition.builder().identityIdList(identityIdList).build());
        //删除身份和用户组关系
        identityGroupService.deleteIGR(GetIdentityGroupCondition.builder().identityIdList(identityIdList).build());
        //删除用户和标签关系
        identityEntityService.deleteIER(GetIdentityEntityCondition.builder().identityIdList(identityIdList).build());
        // 删除身份信息
        return identityInfoMapper.deleteIdentity(GetIdentityCondition.builder().identityIdList(identityIdList).build());
    }

    public IdentityInfoDAO getIdentity(String identityId) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityException("61005", "身份id错误");
        }
        return identityInfoMapper.getIdentity(GetIdentityCondition.builder().identityId(identityId).build());
    }

    public IdentityInfoDAO getIdentity(GetIdentityCondition condition) {
        return identityInfoMapper.getIdentity(condition);
    }

    public void updateIdentity(IdentityInfoDAO identityInfoDAO) {
        identityInfoDAO.setUpdateTime(new Date());
        if (identityInfoMapper.updateIdentity(
                identityInfoDAO,
                GetIdentityCondition.builder()
                        .identityId(identityInfoDAO.getIdentityId())
                        .build()
        ) <= 0) {
            throw new IdentityException("61004", "更新身份失败");
        }
    }

    public List<IdentityDTO> createSimpleIdentityEntity(List<String> identityIdList) {
        if (CollectionUtils.isEmpty(identityIdList)) {
            return new ArrayList<>();
        }
        return identityInfoMapper.getIdentityList(GetIdentityCondition.builder().identityIdList(identityIdList).build(), 0, 0)
                .stream()
                .map(IdentityDTO::create)
                .collect(Collectors.toList());
    }

    public IdentityDTO createIdentityEntity(String appChannelId, String identityId) {
        List<IdentityDTO> identityDTOList = createIdentityEntity(
                appChannelId,
                new ArrayList<String>() {{
                    add(identityId);
                }}
        );
        return identityDTOList.size() > 0 ? identityDTOList.get(0) : null;
    }

    public List<IdentityDTO> createIdentityEntity(String appChannelId, List<String> identityIdList) {
        List<IdentityDTO> userDTOList = new LinkedList<>();
        if (identityIdList.isEmpty()) {
            return userDTOList;
        }
        List<IdentityWithUserAndCompanyDAO> identityWithUserAndCompanyList = identityInfoMapper.getIdentityWithUserAndCompanyList(GetIdentityCondition.builder()
                .identityIdList(identityIdList)
                .build());
        //获取最近一次登录的身份
        List<TokenInfoDAO> tokenInfoDAOList = tokenService.getLastUserTokenListGroupByIdentityId(identityIdList);
//        List<TokenInfoDAO> tokenInfoDAOList = tokenService.getLastUserTokenListGroupByIdentityIdAndAppChannelId(appChannelId, identityIdList);
        Map<String, TokenInfoDAO> identityId2TokenMap = tokenInfoDAOList.stream().collect(Collectors.toMap(TokenInfoDAO::getIdentityId, tokenInfoDAO -> tokenInfoDAO, (dao1, dao2) -> dao1));
        //用户部门
        Map<String, List<String>> identityId2DepartmentIdListMap = identityDepartmentService.getIDRList(GetIdentityDepartmentCondition.builder().identityIdList(identityIdList).build())
                .stream()
                .collect(
                        Collectors.groupingBy(
                                IdentityDepartmentRelationDAO::getIdentityId,
                                Collectors.mapping(IdentityDepartmentRelationDAO::getDepartmentId, Collectors.toList())
                        )
                );
        //用户职位
        Map<String, List<String>> identityId2PositionIdListMap = identityPositionService.getIPRList(GetIdentityPositionCondition.builder().identityIdList(identityIdList).build())
                .stream()
                .collect(
                        Collectors.groupingBy(
                                IdentityPositionRelationDAO::getIdentityId,
                                Collectors.mapping(IdentityPositionRelationDAO::getPositionId, Collectors.toList())
                        )
                );
        //用户组
        List<IGRWithGroupDAO> igrWithGroupDAOList = identityGroupService.getIGRWithGroupList(GetGroupCondition.builder()
                .identityIdList(identityIdList)
//                .appChannelId(appChannelId)
                .build());
        Map<String, List<IGRWithGroupDAO>> identityId2GroupListMap = igrWithGroupDAOList.stream().collect(Collectors.groupingBy(IGRWithGroupDAO::getIdentityId));
        //生成身份权限关系
        Map<String, List<String>> identityId2EventIdListMap = groupEventService.getGERList(GetGroupEventCondition.builder().identityIdList(identityIdList).build())
                .stream()
                .collect(
                        Collectors.groupingBy(
                                GroupEventRelationDAO::getIdentityId,
                                Collectors.mapping(GroupEventRelationDAO::getEventId, Collectors.toList())
                        )
                );
        //生成用户数据
        for (IdentityWithUserAndCompanyDAO identityInfoDAO : identityWithUserAndCompanyList) {
            IdentityDTO identityDTO = IdentityDTO.create(identityInfoDAO);
            TokenInfoDAO tokenInfoDAO = identityId2TokenMap.get(identityInfoDAO.getIdentityId());
            if (tokenInfoDAO != null) {
                identityDTO.setToken(TokenDTO.create(tokenInfoDAO));
                identityDTO.setIsSelected(identityInfoDAO.getIdentityId().equals(tokenInfoDAO.getIdentityId()) ? 1 : 0);
            }
            //部门
            identityDTO.setDepartmentIdList(identityId2DepartmentIdListMap.get(identityInfoDAO.getIdentityId()));
            //职位
            identityDTO.setPositionIdList(identityId2PositionIdListMap.get(identityInfoDAO.getIdentityId()));
            //用户组
            List<IGRWithGroupDAO> igrList = identityId2GroupListMap.getOrDefault(identityInfoDAO.getIdentityId(), new ArrayList<>());
            identityDTO.setIgrList(igrList.stream().map(IdentityGroupRelationDTO::new).collect(Collectors.toList()));
            identityDTO.setGroupList(igrList.stream().map(GroupDTO::create).collect(Collectors.toList()));
            identityDTO.setGroupIdList(igrList.stream().map(IGRWithGroupDAO::getGroupId).collect(Collectors.toList()));
            //事件
            identityDTO.setEventIdList(identityId2EventIdListMap.get(identityInfoDAO.getIdentityId()));
            userDTOList.add(identityDTO);
        }
        return userDTOList;
    }

    public void addIdentityRelationList(String identityId, List<RelationItem> relationItemList, String appChannelId) {
        for (RelationItem relationItem : relationItemList) {
            if ("company".equals(relationItem.getRelateType())) {
                identityCompanyService.addIdentityCompanyRelationList(identityId, relationItem.getRelateIdList(), relationItem.getExtraContent());
            } else if ("department".equals(relationItem.getRelateType())) {
                identityDepartmentService.addIdentityDepartmentRelationList(identityId, relationItem.getRelateIdList(), relationItem.getExtraContent());
            } else if ("position".equals(relationItem.getRelateType())) {
                identityPositionService.addIdentityPositionRelationList(identityId, relationItem.getRelateIdList(), relationItem.getExtraContent());
            } else if ("group".equals(relationItem.getRelateType())) {
                identityGroupService.addIdentityGroupRelation(identityId, relationItem.getRelateIdList(), appChannelId);
            } else if ("entity".equals(relationItem.getRelateType())) {
                identityEntityService.addIdentityEntityRelationList(identityId, relationItem.getRelateIdList(), relationItem.getExtraContent());
            }
        }
    }

    public void updateIdentityRelationList(String identityId, List<RelationItem> relationItemList, String appChannelId) {
        for (RelationItem relationItem : relationItemList) {
            if ("company".equals(relationItem.getRelateType())) {
                identityCompanyService.updateIdentityCompanyRelationList(identityId, relationItem.getRelateIdList(), relationItem.getExtraContent());
            } else if ("department".equals(relationItem.getRelateType())) {
                identityDepartmentService.updateIdentityDepartmentRelationList(identityId, relationItem.getRelateIdList(), relationItem.getExtraContent());
            } else if ("position".equals(relationItem.getRelateType())) {
                identityPositionService.updateIdentityPositionRelationList(identityId, relationItem.getRelateIdList(), relationItem.getExtraContent());
            } else if ("group".equals(relationItem.getRelateType())) {
                boolean isLogout = identityGroupService.updateIdentityGroupRelation(identityId, relationItem.getRelateIdList(), appChannelId);
                if (isLogout) {
                    //退出登录
                    tokenService.logout(GetTokenCondition.builder().identityId(identityId).appChannelId(appChannelId).build(), TokenConstant.tokenStatus.CHANGE_EVENT.getCode().toString());
                }
            } else if ("entity".equals(relationItem.getRelateType())) {
                identityEntityService.updateIdentityEntityRelationList(identityId, relationItem.getRelateIdList(), relationItem.getExtraContent());
            }
        }
    }

    public void deleteIdentityRelationList(String identityId, List<RelationItem> relationItemList) {
        for (RelationItem relationItem : relationItemList) {
            if ("company".equals(relationItem.getRelateType())) {
                identityCompanyService.deleteIdentityCompanyRelation(identityId, relationItem.getRelateIdList());
            } else if ("department".equals(relationItem.getRelateType())) {
                identityDepartmentService.deleteIdentityDepartmentRelation(identityId, relationItem.getRelateIdList());
            } else if ("position".equals(relationItem.getRelateType())) {
                identityPositionService.deleteIdentityPositionRelation(identityId, relationItem.getRelateIdList());
            } else if ("group".equals(relationItem.getRelateType())) {
                identityGroupService.deleteIdentityGroupRelation(identityId, relationItem.getRelateIdList());
            } else if ("entity".equals(relationItem.getRelateType())) {
                identityEntityService.deleteIdentityEntityRelation(identityId, relationItem.getRelateIdList());
            }
        }
    }

    public IdentityDTO getIdentityEntityFromCache(String appChannelId, String identityId) {
        List<IdentityDTO> identityDTOList = getIdentityEntityFromCache(
                appChannelId,
                new ArrayList<>() {{
                    add(identityId);
                }}
        );
        return identityDTOList.size() > 0 ? identityDTOList.get(0) : null;
    }

    public List<IdentityDTO> getIdentityEntityFromCache(String appChannelId, List<String> identityIdList) {
        //先从缓存获取
        List<IdentityDTO> cacheIdentityDTOList = new LinkedList<>();
        List<String> redisIdentityIdList = new LinkedList<>();//redis中存在的identityId
        List<String> dbIdentityIdList = new LinkedList<>();//要从数据库查询的identityId
        //批量获取多个key
        List<String> redisKeyList = identityIdList.stream().map(identityId -> systemService.getIdentityEntityRedisKey(appChannelId, identityId)).collect(Collectors.toList());
        List<String> redisValueList = RedisUtil.mGet(redisKeyList);
        for (String redisValue : redisValueList) {
            if (redisValue == null) continue;
            IdentityDTO identityDTO = StringUtil.jsonDecode(redisValue, IdentityDTO.class);
            if (identityDTO != null) {
                redisIdentityIdList.add(identityDTO.getIdentityId());
                cacheIdentityDTOList.add(identityDTO);
            }
        }
        //过滤出要从数据库查的identityId
        for (String identityId : identityIdList) {
            if (!redisIdentityIdList.contains(identityId)) {
                dbIdentityIdList.add(identityId);
            }
        }
        List<IdentityDTO> dbIdentityDTOList = createIdentityEntity(appChannelId, dbIdentityIdList);
        setIdentityEntityToCache(dbIdentityDTOList, appChannelId);
        Map<String, IdentityDTO> cacheIdentityMap = cacheIdentityDTOList.stream().collect(Collectors.toMap(IdentityDTO::getIdentityId, identityDTO -> identityDTO, (identityDTO1, identityDTO2) -> identityDTO1));
        cacheIdentityMap.putAll(dbIdentityDTOList.stream().collect(Collectors.toMap(IdentityDTO::getIdentityId, identityDTO -> identityDTO, (identityDTO1, identityDTO2) -> identityDTO1)));
        return identityIdList.stream().map(cacheIdentityMap::get).collect(Collectors.toList());
    }

    public void setIdentityEntityToCache(List<IdentityDTO> identityDTOList, String appChannelId) {
        for (IdentityDTO identityDTO : identityDTOList) {
            RedisUtil.set(
                    systemService.getIdentityEntityRedisKey(appChannelId, identityDTO.getIdentityId()),
                    StringUtil.jsonEncode(identityDTO),
                    SystemConstant.REDIS_IDENTITY_ENTITY_EXPIRE
            );
        }
    }

    public void clearIdentityCache(String identityId) {
        Set<String> keySet = RedisUtil.keys(systemService.getIdentityEntityRedisKey("*", identityId));
        if (!keySet.isEmpty()) {
            RedisUtil.del(new ArrayList<>(keySet));
        }
        //清除身份相关的token缓存
        List<String> tokenList = tokenService.getTokenList(GetTokenCondition.builder()
                .identityId(identityId)
                .build()).stream().map(TokenInfoDAO::getToken).collect(Collectors.toList());
        if (!tokenList.isEmpty()) {
            RedisUtil.del(new ArrayList<>(tokenList.stream().map(token -> SystemConstant.REDIS_KEY_USER_TOKEN + token).collect(Collectors.toList())));
        }
    }

    public void clearIdentityCache(List<String> identityIdList) {
        if (CollectionUtils.isEmpty(identityIdList)) {
            return;
        }
        List<String> identityKeyList = identityIdList.stream().map(identityId -> systemService.getIdentityEntityRedisKey("*", identityId)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(identityKeyList)) {
            RedisUtil.del(identityKeyList);
        }
        //清除身份相关的token缓存
        List<String> tokenList = tokenService.getTokenList(GetTokenCondition.builder().identityIdList(identityIdList).build())
                .stream().map(TokenInfoDAO::getToken).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tokenList)) {
            RedisUtil.del(tokenList.stream().map(token -> SystemConstant.REDIS_KEY_USER_TOKEN + token).collect(Collectors.toList()));
        }
    }

    public int deleteIdentity(GetIdentityCondition condition) {
        return identityInfoMapper.deleteIdentity(condition);
    }

    public List<IdentityDTO> doIdentityKeyFilter(List<IdentityDTO> identityDTOList, IdentityKeyFilter identityKeyFilter) {
        if (identityDTOList != null) {
            for (IdentityDTO identityDTO : identityDTOList) {
                doIdentityKeyFilter(identityDTO, identityKeyFilter);
            }
        }
        return identityDTOList;
    }

    public IdentityDTO doIdentityKeyFilter(IdentityDTO identityDTO, IdentityKeyFilter identityKeyFilter) {
        //过滤扩展字段的key
        if (identityKeyFilter.getExtraContentKeyList() != null && !identityKeyFilter.getExtraContentKeyList().isEmpty()) {
            Map<String, Object> newExtraContent = new HashMap<>();
            for (Map.Entry<String, Object> entry : identityDTO.getExtraContent().entrySet()) {
                if (identityKeyFilter.getExtraContentKeyList().contains(entry.getKey())) {
                    newExtraContent.put(entry.getKey(), entry.getValue());
                }
            }
            identityDTO.setExtraContent(newExtraContent);
        }
        //过滤身份的公司属性
        if (identityKeyFilter.getIsGetCompany() <= 0) {
            identityDTO.setCompany(null);
        }
        //过滤身份的用户属性
        if (identityKeyFilter.getIsGetUser() <= 0) {
            identityDTO.setUser(null);
        }
        //过滤身份的事件id列表属性
        if (identityKeyFilter.getIsGetEventIdList() <= 0) {
            identityDTO.setEventIdList(null);
        }
        //过滤身份的用户组
        if (identityKeyFilter.getIsGetGroupList() <= 0) {
            identityDTO.setGroupList(null);
        }
        //过滤身份的token
        if (identityKeyFilter.getIsGetToken() <= 0) {
            identityDTO.setToken(null);
        }
        //过滤身份的用户组关系
        if (identityKeyFilter.getIsGetIgrList() <= 0) {
            identityDTO.setIgrList(null);
        }
        // 过滤部门
        if (identityKeyFilter.getIsGetDepartmentList() <= 0) {
            identityDTO.setDepartmentList(null);
        }
        // 过滤职位
        if (identityKeyFilter.getIsGetPositionList() <= 0) {
            identityDTO.setPositionList(null);
        }
        return identityDTO;
    }

    public List<String> getIdentityByDepartmentIds(List<String> departmentIds) {
        return identityInfoMapper.getIdentityByDepartmentIds(departmentIds);
    }

    /**
     * 获取公司下所有的人员信息
     *
     * @param companyId
     * @return
     */
    public List<IdentityByCompanyDTO> getIdentityListByCompanyId(String companyId) {
        List<IdentityByCompanyDAO> identityByCompanyId = identityInfoMapper.getIdentityByCompanyId(companyId);
        return identityByCompanyId.stream().map(IdentityByCompanyDTO::create).collect(Collectors.toList());
    }

    /**
     * 查询未离职成员
     * @param identityIds
     * @return
     */
    public List<String> getIdentityByEmployeeStatus(List<String> identityIds) {

        return identityInfoMapper.getIdentityByEmployeeStatus(identityIds);
    }
}
