package com.microservice.user.service;

import com.microservice.user.entity.condition.GetEntityRelationCondition;
import com.microservice.user.entity.dao.EntityRelationDAO;
import com.microservice.user.entity.dto.ms.EntityRelationDTO;
import com.microservice.user.exception.EntityRelationException;
import com.microservice.user.mapper.EntityRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class EntityRelationService {

    @Autowired
    private EntityRelationMapper entityRelationMapper;

    public List<EntityRelationDTO> addEntityRelationList(String entityId, List<String> targetEntityIdList, Map<String, Object> extraContent) {
        List<EntityRelationDAO> entityRelationDAOList = targetEntityIdList.stream().map(targetEntityId -> EntityRelationDAO.builder()
                .entityId(entityId)
                .targetEntityId(targetEntityId)
                .extraContent((extraContent == null || extraContent.isEmpty()) ? "{}" : StringUtil.jsonEncode(extraContent))
                .build()).collect(Collectors.toList());
        entityRelationMapper.addEntityRelationList(entityRelationDAOList);
        return entityRelationDAOList.stream().map(EntityRelationDTO::create).collect(Collectors.toList());
    }

    public List<EntityRelationDTO> addEntityRelationList(String entityId, List<String> targetEntityIdList) {
        return addEntityRelationList(entityId, targetEntityIdList, new HashMap<>());
    }

    public void updateEntityRelation(EntityRelationDAO updateData, GetEntityRelationCondition condition) {
        updateData.setUpdateTime(new Date());
        if (entityRelationMapper.updateEntityRelation(updateData, condition) <= 0) {
            throw new EntityRelationException("61801", "更新标签关系失败");
        }
    }

    public EntityRelationDAO getEntityRelation(GetEntityRelationCondition condition) {
        return entityRelationMapper.getEntityRelation(condition);
    }

    public void deleteEntityRelation(GetEntityRelationCondition condition) {
        if (condition.getEntityIdList().isEmpty() && condition.getTargetEntityIdList().isEmpty()) {
            throw new EntityRelationException("61802", "删除条件错误");
        }
        if (entityRelationMapper.deleteEntityRelation(condition) <= 0) {
            throw new EntityRelationException("61803", "删除失败");
        }
    }

//    public List<EntityRelationDAO> getEntityRelationList(GetEntityRelationCondition condition, int start, int limit) {
//        return entityInfoMapper
//    }

    public List<EntityRelationDAO> getERWithEntityList(GetEntityRelationCondition condition, int start, int limit) {
        if ((condition.getEntityIdList() != null && condition.getEntityIdList().isEmpty())
                || (condition.getTargetEntityId() != null && condition.getTargetEntityId().isEmpty())) {
            return new ArrayList<>();
        }
        return entityRelationMapper.getERWithEntityList(condition, start, limit);
    }

    public int getERWithEntityCount(GetEntityRelationCondition condition) {
        if ((condition.getEntityIdList() != null && condition.getEntityIdList().isEmpty())
                || (condition.getTargetEntityId() != null && condition.getTargetEntityId().isEmpty())) {
            return 0;
        }
        return entityRelationMapper.getERWithEntityCount(condition);
    }
}
