package com.microservice.user.service;

import com.microservice.user.MsUserApplication;
import com.microservice.user.constant.SystemConstant;
import com.microservice.user.entity.dto.ms.SystemInfoDTO;
import com.microservice.user.util.ConfigUtil;
import com.microservice.user.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class SystemService {

    public SystemInfoDTO getSystemInfo() {
        SystemInfoDTO systemInfoDTO = SystemInfoDTO.builder()
                .name(MsUserApplication.name)
                .ip(MsUserApplication.ip)
                .port(MsUserApplication.port)
                .version(MsUserApplication.version)
                .appChannelId(ConfigUtil.getSystemConfig().getAppChannelId())
                .dataChannelId(ConfigUtil.getSystemConfig().getDataChannelId())
                .build();
        //依赖项
        Map<String, Object> dependencyMap = new HashMap<>() {{
            put("checkMsLog", StringUtil.isEmpty(ConfigUtil.getSystemConfig().getMsLogDomain()) ? 0 : 1);
            put("logLevel", ConfigUtil.getSystemConfig().getLogLevel());
        }};
        systemInfoDTO.setDependencies(dependencyMap);
        //检查健康
        int health = 1;
        for (Map.Entry<String, Object> entry : dependencyMap.entrySet()) {
            if (entry.getKey().startsWith("check")) {
                if (Integer.parseInt(entry.getValue().toString()) != 1) {
                    health = 0;
                    break;
                }
            }
        }
        systemInfoDTO.setHealth(health);
        return systemInfoDTO;
    }

    public String getUserEntityRedisKey(String appChannelId, String userId) {
        return SystemConstant.REDIS_KEY_USER_ENTITY + "-" + userId;
    }

    public String getIdentityEntityRedisKey(String appChannelId, String identityId) {
        return SystemConstant.REDIS_KEY_IDENTITY_ENTITY + "-" + identityId;
    }

}
