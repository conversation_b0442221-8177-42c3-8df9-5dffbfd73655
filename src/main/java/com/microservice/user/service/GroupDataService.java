package com.microservice.user.service;

import com.microservice.user.entity.condition.GetGroupDataCondition;
import com.microservice.user.entity.dao.GroupDataRelationDAO;
import com.microservice.user.exception.GroupDataException;
import com.microservice.user.mapper.GroupDataRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class GroupDataService {

    @Autowired
    private GroupDataRelationMapper gdrMapper;

    public List<GroupDataRelationDAO> getGDRList(GetGroupDataCondition condition) {
        if ((condition.getDataList() != null && condition.getDataList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getGroupIdList() != null && condition.getGroupIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return gdrMapper.getGDRList(condition);
    }

    public Map<String, List<String>> getGroupId2DataListMap(List<String> groupIdList) {
        if (groupIdList.isEmpty()) {
            return new HashMap<>();
        }
        List<GroupDataRelationDAO> gdrDAOList = gdrMapper.getGroupDataRelationList(
                GetGroupDataCondition.builder()
                        .groupIdList(groupIdList)
                        .build()
        );
        return gdrDAOList.stream().collect(
                Collectors.groupingBy(
                        GroupDataRelationDAO::getGroupId,
                        Collectors.mapping(GroupDataRelationDAO::getData, Collectors.toList())
                )
        );
    }

    public void addGDRList(String groupId, List<String> dataList) {
        if (dataList != null && !dataList.isEmpty()) {
            gdrMapper.addGroupDataRelationList(
                    dataList.stream()
                            .map(data -> GroupDataRelationDAO.builder()
                                    .data(data)
                                    .groupId(groupId)
                                    .build())
                            .collect(Collectors.toList())
            );
        }
    }

    public void updateGroupDataRelation(String groupId, List<String> dataList) {
        if (StringUtil.isEmpty(groupId)) {
            throw new GroupDataException("61101", "用户组id错误");
        }
        gdrMapper.deleteGroupDataRelation(GetGroupDataCondition.builder().groupId(groupId).build());
        addGDRList(groupId, dataList);
    }

    public void deleteGDRByGroupId(String groupId) {
        if (StringUtil.isEmpty(groupId)) {
            throw new GroupDataException("61102", "用户组id错误");
        }
        gdrMapper.deleteGroupDataRelation(GetGroupDataCondition.builder().groupId(groupId).build());
    }

    public int deleteGDR(GetGroupDataCondition condition) {
        return gdrMapper.deleteGroupDataRelation(condition);
    }
}
