package com.microservice.user.service;

import com.microservice.user.entity.condition.GetGroupCondition;
import com.microservice.user.entity.condition.GetIdentityGroupCondition;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.GroupDTO;
import com.microservice.user.exception.IdentityGroupException;
import com.microservice.user.mapper.IdentityGroupRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class IdentityGroupService {

    @Autowired
    private IdentityGroupRelationMapper igrMapper;
    @Autowired
    private GroupService groupService;

    @Autowired
    private UserService userService;
    @Autowired
    private IdentityService identityService;

    /**
     * @param identityId   身份id
     * @param groupIdList  用户组id
     * @param appChannelId 渠道号
     * @return bool 是否修改了身份和用户组的关系
     */
    public boolean updateIdentityGroupRelation(String identityId, List<String> groupIdList, String appChannelId) {
        //要添加的用户组id
        Set<String> newGroupIdList = groupService.getGroupList(GetGroupCondition.builder()
                .groupIdList(groupIdList)
                .appChannelId(appChannelId)
                .build()).stream().map(GroupDTO::getGroupId).collect(Collectors.toSet());
        //已有用户组
        List<IGRWithGroupDAO> igrWithGroupDAOList = igrMapper.getIGRWithGroupList(GetGroupCondition.builder()
                .identityId(identityId)
                .appChannelId(appChannelId)
                .build());
        //已有用户组id
        Set<String> oldGroupIdList = igrWithGroupDAOList.stream().map(IGRWithGroupDAO::getGroupId).collect(Collectors.toSet());
        //如果要添加的用户组和原有用户组一致，不更新
        if (newGroupIdList.size() == oldGroupIdList.size() && newGroupIdList.containsAll(oldGroupIdList)) {
            return false;
        }
        if (igrWithGroupDAOList.size() > 0) {
            igrMapper.deleteIdentityGroupRelation(GetIdentityGroupCondition.builder()
                    .identityId(identityId)
                    .groupIdList(igrWithGroupDAOList.stream().map(IGRWithGroupDAO::getGroupId).collect(Collectors.toList()))
                    .build());
        }
        addIdentityGroupRelation(identityId, groupIdList, appChannelId);
        return true;
    }

    public void updateIdentityGroupRelation(List<String> identityIdList, String groupId, String appChannelId) {
        //要添加的用户组id
        if (Objects.isNull(identityIdList) || StringUtils.isEmpty(groupId)) {
            return;
        }

        GroupInfoDAO groupInfo = groupService.getGroup(GetGroupCondition.builder()
                .groupId(groupId)
                .appChannelId(appChannelId)
                .build());

        if (Objects.isNull(groupInfo)) {
            return;
        }

        //已有用户组
        List<IGRWithGroupDAO> igrWithGroupDAOList = igrMapper.getIGRWithGroupList(GetGroupCondition.builder()
//                .identityIdList(identityIdList)
                .groupId(groupId)
                .appChannelId(appChannelId)
                .build());

        List<String> oldIdentityGroupList = igrWithGroupDAOList.stream().map(IGRWithGroupDAO::getIdentityId).collect(Collectors.toList());

        List<String> addIdentityGroupIdList;
        if (CollectionUtils.isEmpty(oldIdentityGroupList)) {
            addIdentityGroupIdList = identityIdList;
        } else {
            addIdentityGroupIdList = identityIdList.stream().filter(s ->
                        !oldIdentityGroupList.contains(s)
            ).collect(Collectors.toList());
        }

        oldIdentityGroupList.removeAll(identityIdList);

        //如果要添加的用户组和原有用户组一致，不更新
        if (!CollectionUtils.isEmpty(oldIdentityGroupList)) {
            igrMapper.deleteIdentityGroupRelation(GetIdentityGroupCondition.builder()
                    .identityIdList(oldIdentityGroupList.stream().distinct().collect(Collectors.toList()))
                    .groupId(groupId)
                    .build());
            //删除缓存
            userService.delUserCache(oldIdentityGroupList);
        }

        if (!CollectionUtils.isEmpty(addIdentityGroupIdList)) {
            igrMapper.addIdentityGroupRelationList(
                    addIdentityGroupIdList.stream()
                            .map(userId -> IdentityGroupRelationDAO.builder()
                                    .groupId(groupId)
                                    .identityId(userId)
                                    .build())
                            .collect(Collectors.toList()));
            userService.delUserCache(addIdentityGroupIdList);
            userService.updateUserCache(appChannelId,addIdentityGroupIdList);
        }
    }


    public void deleteIGRByIdentityId(String identityId) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityGroupException("65001", "身份id错误");
        }
        igrMapper.deleteIdentityGroupRelation(GetIdentityGroupCondition.builder().identityId(identityId).build());
    }

    public void deleteIGRByGroupId(String groupId) {
        if (StringUtil.isEmpty(groupId)) {
            throw new IdentityGroupException("65003", "用户组id错误");
        }
        igrMapper.deleteIdentityGroupRelation(GetIdentityGroupCondition.builder().groupId(groupId).build());
    }

    public void addIdentityGroupRelation(String identityId, List<String> groupIdList, String appChannelId) {
        if (groupIdList != null && groupIdList.size() > 0) {
            groupIdList = groupIdList.stream().distinct().filter(groupId -> !StringUtil.isEmpty(groupId)).collect(Collectors.toList());
            List<GroupDTO> groupDTOList = groupService.getGroupList(GetGroupCondition.builder()
                    .groupIdList(groupIdList)
                    .appChannelId(appChannelId)
                    .build());
            if (groupDTOList.size() > 0) {
                groupIdList = groupDTOList.stream().map(GroupDTO::getGroupId).collect(Collectors.toList());
                //查询已存在的关系
                List<IGRWithGroupDAO> igrWithGroupDAOList = igrMapper.getIGRWithGroupList(GetGroupCondition.builder()
                        .identityId(identityId)
                        .appChannelId(appChannelId)
                        .build());
                List<String> existGroupIdList = igrWithGroupDAOList.stream().map(IGRWithGroupDAO::getGroupId).collect(Collectors.toList());
                //过滤已存在的group_id
                groupIdList.removeAll(existGroupIdList);
                if (!groupIdList.isEmpty()) {
                    igrMapper.addIdentityGroupRelationList(
                            groupIdList.stream()
                                    .map(groupId -> IdentityGroupRelationDAO.builder()
                                            .groupId(groupId)
                                            .identityId(identityId)
                                            .build())
                                    .collect(Collectors.toList()));
                }
            }
        }
    }

    public List<IdentityGroupRelationDAO> getIGRList(GetIdentityGroupCondition condition, int start, int limit) {
        if ((condition.getGroupIdList() != null && condition.getGroupIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return igrMapper.getIdentityGroupRelationList(condition, start, limit);
    }

    public List<IdentityGroupRelationDAO> getIGRList(GetIdentityGroupCondition condition) {
        return getIGRList(condition, 0, 0);
    }

    public int getIGRCount(GetIdentityGroupCondition condition) {
        if ((condition.getGroupIdList() != null && condition.getGroupIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return igrMapper.getIdentityGroupRelationCount(condition);
    }

    public List<IdentityGroupCountDAO> getIGRCountGroupByGroupId(GetIdentityGroupCondition condition) {
        if ((condition.getGroupIdList() != null && condition.getGroupIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return igrMapper.getIGRCountGroupByGroupId(condition);
    }

    public Map<String, List<String>> getGroupId2IdentityIdListMap(List<String> groupIdList) {
        if (groupIdList.isEmpty()) {
            return new HashMap<>();
        }
        List<IdentityGroupRelationDAO> igrDAOList = getIGRList(
                GetIdentityGroupCondition.builder()
                        .groupIdList(groupIdList)
                        .build()
        );
        return igrDAOList.stream().collect(
                Collectors.groupingBy(
                        IdentityGroupRelationDAO::getGroupId,
                        Collectors.mapping(IdentityGroupRelationDAO::getIdentityId, Collectors.toList())
                )
        );
    }

    public Map<String, Integer> getGroupId2IdentityCountMap(List<String> groupIdList) {
        return getIGRCountGroupByGroupId(
                GetIdentityGroupCondition.builder()
                        .groupIdList(groupIdList)
                        .build()
        ).stream().collect(Collectors.toMap(IdentityGroupCountDAO::getGroupId, IdentityGroupCountDAO::getDataCount));
    }

    public List<IGRWithIdentityDAO> getIGRWithIdentityList(GetIdentityGroupCondition condition, int start, int limit) {
        if ((condition.getGroupIdList() != null && condition.getGroupIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return igrMapper.getIGRWithIdentityList(condition, start, limit);
    }

    public void deleteIdentityGroupRelation(String identityId, List<String> groupIdList) {
        if (!StringUtil.isEmpty(identityId) && !groupIdList.isEmpty()) {
            igrMapper.deleteIdentityGroupRelation(GetIdentityGroupCondition.builder()
                    .groupIdList(groupIdList)
                    .identityId(identityId)
                    .build());
        }
    }

    public int deleteIGR(GetIdentityGroupCondition condition) {
        return igrMapper.deleteIdentityGroupRelation(condition);
    }

    public List<IGRWithGroupDAO> getIGRWithGroupList(GetGroupCondition condition) {
        return igrMapper.getIGRWithGroupList(condition);
    }
}
