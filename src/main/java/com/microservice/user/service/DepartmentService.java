package com.microservice.user.service;

import com.microservice.user.constant.SystemConstant;
import com.microservice.user.constant.TokenConstant;
import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.DepartmentDTO;
import com.microservice.user.entity.dto.ms.UserInfoPreDTO;
import com.microservice.user.exception.CompanyException;
import com.microservice.user.exception.DepartmentException;
import com.microservice.user.mapper.DepartmentInfoMapper;
import com.microservice.user.mapper.DepartmentPositionRelationMapper;
import com.microservice.user.mapper.UserInfoPreMapper;
import com.microservice.user.util.RedisUtil;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class DepartmentService {

    @Autowired
    private DepartmentInfoMapper departmentInfoMapper;
    @Autowired
    private CompanyDepartmentService companyDepartmentService;
    @Autowired
    private DepartmentPositionService departmentPositionService;
    @Autowired
    private IdentityDepartmentService identityDepartmentService;
    @Autowired
    private UserInfoPreMapper userInfoPreMapper;

    public List<DepartmentDTO> getDepartmentList(GetDepartmentCondition condition) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return getDepartmentList(condition, 0, 0);
    }

    public List<DepartmentDTO> getDepartmentList(GetDepartmentCondition condition, int start, int limit) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        if (!StringUtil.isEmpty(condition.getCompanyId()) || condition.getCompanyIdList() != null) {
            return companyDepartmentService.getCDRWithDepartmentList(
                            GetCompanyDepartmentCondition.builder()
                                    .companyId(condition.getCompanyId())
                                    .companyIdList(condition.getCompanyIdList())
                                    .departmentIdList(condition.getDepartmentIdList())
                                    .departmentId(condition.getDepartmentId())
                                    .build(), start, limit)
                    .stream().map(DepartmentDTO::create).collect(Collectors.toList());
        } else {
            return departmentInfoMapper.getDepartmentList(condition, start, limit)
                    .stream()
                    .map(DepartmentDTO::create)
                    .collect(Collectors.toList());
        }
    }

    public int getDepartmentCount(GetDepartmentCondition condition) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        if (!StringUtil.isEmpty(condition.getCompanyId())
                || (condition.getCompanyIdList() != null && !condition.getCompanyIdList().isEmpty())) {
            return companyDepartmentService.getCDRWithDepartmentCount(
                    GetCompanyDepartmentCondition.builder()
                            .companyId(condition.getCompanyId())
                            .companyIdList(condition.getCompanyIdList())
                            .departmentIdList(condition.getDepartmentIdList())
                            .departmentId(condition.getDepartmentId())
                            .build());
        } else {
            return departmentInfoMapper.getDepartmentCount(condition);
        }
    }

    public DepartmentInfoDAO addDepartment(String departmentName, String parentId, Map<String, Object> extraContent, Integer sort) {
        if (extraContent == null || extraContent.isEmpty()) {
            extraContent = new HashMap<>() {{
                put("departmentName", departmentName);
            }};
        }
        DepartmentInfoDAO departmentInfoDAO = new DepartmentInfoDAO();
        departmentInfoDAO.setDepartmentId(StringUtil.createUuid());
        departmentInfoDAO.setDepartmentName(departmentName);
        departmentInfoDAO.setSort(sort);
        departmentInfoDAO.setParentId(parentId);
        departmentInfoDAO.setExtraContent(StringUtil.jsonEncode(extraContent));
        departmentInfoDAO.setCreateTime(new Date());
        if (departmentInfoMapper.addDepartment(departmentInfoDAO) <= 0) {
            throw new DepartmentException("67001", "添加部门失败");
        }
        return departmentInfoDAO;
    }

    public DepartmentInfoDAO getDepartment(String departmentId) {
        return getDepartment(GetDepartmentCondition.builder()
                .departmentId(departmentId)
                .build());
    }

    public DepartmentInfoDAO getDepartment(GetDepartmentCondition condition) {
        return departmentInfoMapper.getDepartment(condition);
    }

    public void updateDepartment(DepartmentInfoDAO departmentInfoDAO) {
        departmentInfoDAO.setUpdateTime(new Date());
        if (departmentInfoMapper.updateDepartment(
                departmentInfoDAO,
                GetDepartmentCondition.builder()
                        .departmentId(departmentInfoDAO.getDepartmentId())
                        .build()
        ) <= 0) {
            throw new CompanyException("67002", "更新部门失败");
        }
    }

    public DepartmentDTO deleteDepartment(String departmentId, int isForce) {
        DepartmentInfoDAO departmentInfoDAO = getDepartment(departmentId);
        if (departmentInfoDAO == null) {
            return null;
        }
        //检查子部门
        DepartmentInfoDAO child = departmentInfoMapper.getDepartment(GetDepartmentCondition.builder()
                .parentId(departmentId)
                .build());
        if (child != null) {
            if (isForce <= 0) {
                throw new CompanyException("67003", "部门下存在子部门，不能删除");
            }
            departmentInfoMapper.deleteDepartment(GetDepartmentCondition.builder()
                    .parentId(departmentId)
                    .build());
        }
        //检查用户
        List<IdentityDepartmentRelationDAO> idrList = identityDepartmentService.getIDRValidIdentityRelation(departmentId);
        if (!CollectionUtils.isEmpty(idrList) ) {
            if (isForce <= 0) {
                throw new CompanyException("67006", "部门下存在用户，不能删除");
            }
            identityDepartmentService.deleteIDR(GetIdentityDepartmentCondition.builder().departmentId(departmentId).build());
        }
        List<UserInfoPreDAO> userPreList = userInfoPreMapper.getValidUserList(departmentId);
        if (!CollectionUtils.isEmpty(userPreList)) {
            if (isForce <= 0) {
                throw new CompanyException("67006", "部门下存在用户，不能删除");
            }
            userInfoPreMapper.deleteDepartment(departmentId);
        }
        //删除部门
        if (departmentInfoMapper.deleteDepartment(GetDepartmentCondition.builder().departmentId(departmentId).build()) <= 0) {
            throw new CompanyException("67007", "删除失败");
        }
        return DepartmentDTO.create(departmentInfoDAO);
    }

    public int deleteDepartment(GetDepartmentCondition condition) {
        return departmentInfoMapper.deleteDepartment(condition);
    }
}
