package com.microservice.user.service;

import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.label.LabelDataListDTO;
import com.microservice.user.entity.dto.ms.label.LabelInfoDTO;
import com.microservice.user.entity.dto.ms.label.LabelMemberDTO;
import com.microservice.user.entity.request.ms.label.*;

import javax.validation.Valid;
import java.util.List;

public interface LabelService {
    /**
     *
     * @param request
     * @return
     */
    LabelInfoDTO addLabel(AddLabelRequest request);

    /**
     *
     * @param request
     * @return
     */
    LabelInfoDTO updateLabel(UpdateLabelRequest request);

    /**
     *
     * @param request
     * @return
     */
    DataListDTO<LabelInfoDTO> getLabelList(LabelQueryRequest request);

    /**
     * 添加标签成员
     * @param request
     */
    void addLabelMembers(AddLabelMembersRequest request);

    /**
     * 获取标签成员
     * @param request
     */
    LabelDataListDTO<LabelMemberDTO> getLabelMembers(GetLabelMembersRequest request);

    /**
     * 更新标签成员
     * @param request
     */
    void updateLabelMember(UpdateLabelMemberRequest request);

    /**
     * 设置标签管理范围
     * @param request
     */
    void setLabelManageScope(SetLabelManageScopeRequest request);

    /**
     * 获取标签审核人id
     * @param request
     */
    List<String> getLabelAuditorId(GetLabelAuditorIdRequest request);

    /**
     * 获取用户标签列表
     * @param request
     */
    DataListDTO<LabelInfoDTO> getLabelListByUser(QueryUserLabelRequest request);

    void addLabelMembersByIdentity(AddLabelMembersRequest request);

}
