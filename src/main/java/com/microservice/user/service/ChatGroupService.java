package com.microservice.user.service;

import cn.hutool.core.collection.CollectionUtil;
import com.microservice.user.entity.condition.GetChatGroupCondition;
import com.microservice.user.entity.condition.GetIdentityChatGroupCondition;
import com.microservice.user.entity.dao.ChatGroupInfoDAO;
import com.microservice.user.entity.dao.IdentityChatGroupRelationDAO;
import com.microservice.user.entity.dto.ms.ChatGroupDTO;
import com.microservice.user.entity.dto.ms.SearchChatGroupDTO;
import com.microservice.user.entity.request.ms.chatGroup.SearchChatGroupRequest;
import com.microservice.user.exception.ChatGroupException;
import com.microservice.user.mapper.ChatGroupInfoMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ChatGroupService {
    @Autowired
    private ChatGroupInfoMapper chatGroupInfoMapper;
    @Autowired
    private IdentityChatGroupService identityChatGroupService;
    @Autowired
    private IdentityService identityService;

    public ChatGroupInfoDAO addChatGroup(String chatGroupName, Map<String, Object> extraContent, String appChannelId, String dataChannelId, List<String> identityIdList) {
        ChatGroupInfoDAO chatGroupInfoDAO = new ChatGroupInfoDAO();
        chatGroupInfoDAO.setChatGroupName(chatGroupName);
        chatGroupInfoDAO.setExtraContent(StringUtil.jsonEncode(extraContent));
        chatGroupInfoDAO.setAppChannelId(appChannelId);
        chatGroupInfoDAO.setDataChannelId(dataChannelId);
        chatGroupInfoDAO.setCreateTime(new Date());
        chatGroupInfoDAO = addChatGroup(chatGroupInfoDAO);
        if (identityIdList != null && !identityIdList.isEmpty()) {
            identityChatGroupService.addICGRList(chatGroupInfoDAO.getChatGroupId(), identityIdList, new HashMap<>());
        }
        return chatGroupInfoDAO;
    }

    public ChatGroupInfoDAO addChatGroup(ChatGroupInfoDAO chatGroupInfoDAO) {
        if (StringUtil.isEmpty(chatGroupInfoDAO.getChatGroupId())) {
            chatGroupInfoDAO.setChatGroupId(StringUtil.createUuid());
        }
        if (chatGroupInfoMapper.addChatGroup(chatGroupInfoDAO) <= 0) {
            throw new ChatGroupException("61401", "添加群聊失败");
        }
        return chatGroupInfoDAO;
    }

    public ChatGroupDTO createChatGroupEntity(String appChannelId, String chatGroupId) {
        ChatGroupInfoDAO chatGroupInfoDAO = chatGroupInfoMapper.getChatGroup(GetChatGroupCondition.builder().chatGroupId(chatGroupId).build());
        ChatGroupDTO chatGroupDTO = ChatGroupDTO.create(chatGroupInfoDAO);
        //群成员
        List<IdentityChatGroupRelationDAO> icgrList = identityChatGroupService.getICGRList(GetIdentityChatGroupCondition.builder().chatGroupId(chatGroupId).build());
        chatGroupDTO.setIdentityList(identityService.createIdentityEntity(
                appChannelId,
                icgrList.stream().map(IdentityChatGroupRelationDAO::getIdentityId).collect(Collectors.toList()))
        );
        return chatGroupDTO;
    }

    public ChatGroupInfoDAO getChatGroup(String chatGroupId) {
        return chatGroupInfoMapper.getChatGroup(GetChatGroupCondition.builder()
                .chatGroupId(chatGroupId)
                .build());
    }

    public void updateChatGroup(ChatGroupInfoDAO updateData) {
        updateData.setUpdateTime(new Date());
        chatGroupInfoMapper.updateChatGroup(
                updateData,
                GetChatGroupCondition.builder()
                        .chatGroupId(updateData.getChatGroupId())
                        .build()
        );
    }

    public void updateChatGroup(ChatGroupInfoDAO updateData, List<String> identityIdList) {
        updateChatGroup(updateData);
        //群成员
        if (identityIdList != null) {
            updateChatGroupIdentity(updateData.getChatGroupId(), identityIdList);
        }
    }

    /**
     * 更新群成员
     *
     * @param chatGroupId
     * @param identityIdList
     */
    public void updateChatGroupIdentity(String chatGroupId, List<String> identityIdList) {
        Set<String> oldIdentityIdSet = identityChatGroupService.getICGRList(GetIdentityChatGroupCondition.builder().chatGroupId(chatGroupId).build())
                .stream().map(IdentityChatGroupRelationDAO::getIdentityId).collect(Collectors.toSet());
        Set<String> addIdentityIdSet = new LinkedHashSet<>();
        Set<String> deleteIdentityIdSet = new LinkedHashSet<>();
        for (String identityId : identityIdList) {
            if (!oldIdentityIdSet.contains(identityId)) {
                addIdentityIdSet.add(identityId);
            }
        }
        for (String oldIdentityId : oldIdentityIdSet) {
            if (!identityIdList.contains(oldIdentityId)) {
                deleteIdentityIdSet.add(oldIdentityId);
            }
        }
        if (!addIdentityIdSet.isEmpty()) {
            identityChatGroupService.addICGRList(chatGroupId, new ArrayList<>(addIdentityIdSet), new HashMap<>());
        }
        if (!deleteIdentityIdSet.isEmpty()) {
            if (identityChatGroupService.deleteICGR(GetIdentityChatGroupCondition.builder()
                    .chatGroupId(chatGroupId)
                    .identityIdList(new ArrayList<>(deleteIdentityIdSet))
                    .build()) <= 0) {
                throw new ChatGroupException("61402", "修改群聊成员失败");
            }
        }
    }

    public List<IdentityChatGroupRelationDAO> addChatGroupIdentity(String chatGroupId, List<String> newIdentityIdList, Map<String, Object> extraContent) {
        Set<String> oldIdentityIdSet = identityChatGroupService.getICGRList(GetIdentityChatGroupCondition.builder().chatGroupId(chatGroupId).build())
                .stream().map(IdentityChatGroupRelationDAO::getIdentityId).collect(Collectors.toSet());
        Set<String> addIdentityIdSet = new LinkedHashSet<>();
        for (String identityId : newIdentityIdList) {
            if (!oldIdentityIdSet.contains(identityId)) {
                addIdentityIdSet.add(identityId);
            }
        }
        if (!addIdentityIdSet.isEmpty()) {
            return identityChatGroupService.addICGRList(chatGroupId, new ArrayList<>(addIdentityIdSet), extraContent);
        }
        return new ArrayList<>();
    }

    public void deleteChatGroup(String chatGroupId) {
        if (StringUtil.isEmpty(chatGroupId)) {
            throw new ChatGroupException("61403", "群聊id错误");
        }
        //删除群聊
        if (chatGroupInfoMapper.deleteChatGroup(GetChatGroupCondition.builder().chatGroupId(chatGroupId).build()) <= 0) {
            throw new ChatGroupException("61404", "删除群聊失败");
        }
        //删除群聊成员
        identityChatGroupService.deleteICGR(GetIdentityChatGroupCondition.builder()
                .chatGroupId(chatGroupId)
                .build());
    }

    public List<ChatGroupInfoDAO> getChatGroupList(GetChatGroupCondition condition, int start, int limit) {
        if (condition.getChatGroupIdList() != null && condition.getChatGroupIdList().isEmpty()) {
            return new ArrayList<>();
        }
        return chatGroupInfoMapper.getChatGroupList(condition, start, limit);
    }

    public int getChatGroupCount(GetChatGroupCondition condition) {
        if (condition.getChatGroupIdList() != null && condition.getChatGroupIdList().isEmpty()) {
            return 0;
        }
        return chatGroupInfoMapper.getChatGroupCount(condition);
    }

    public List<SearchChatGroupDTO> searchChatGroup(SearchChatGroupRequest request) {
        if(StringUtil.isEmpty(request.getKeyword())|| CollectionUtil.isEmpty(request.getGroupIdList())){
            return new ArrayList<>();
        }

        List<String> searchGroupIdList = request.getGroupIdList();
        List<SearchChatGroupDTO> searchChatGroupDTOList = chatGroupInfoMapper.searchChatGroupByName(searchGroupIdList,request.getKeyword());

        List<String> groupIdList = searchChatGroupDTOList.stream().map(SearchChatGroupDTO::getChatGroupId).collect(Collectors.toList());
        //去除通过群聊名称已查询出的群聊
        searchGroupIdList.removeAll(groupIdList);
        List<SearchChatGroupDTO> userRemarkChatGroupDTOList =  chatGroupInfoMapper.searchChatGroupByUserRemarkName(searchGroupIdList,request.getKeyword());

        //去除通过群成员备注已查询出的群聊
        List<String> groupIdList2 = userRemarkChatGroupDTOList.stream().map(SearchChatGroupDTO::getChatGroupId).collect(Collectors.toList());
        searchGroupIdList.removeAll(groupIdList2);
        List<SearchChatGroupDTO> userNameChatGroupDTOList =  chatGroupInfoMapper.searchChatGroupByUserName(searchGroupIdList,request.getKeyword());

        searchChatGroupDTOList.addAll(userRemarkChatGroupDTOList);
        searchChatGroupDTOList.addAll(userNameChatGroupDTOList);
        return searchChatGroupDTOList;
    }
}
