package com.microservice.user.service;

import com.microservice.user.entity.condition.GetDepartmentPositionCondition;
import com.microservice.user.entity.dao.DPRWithPositionDAO;
import com.microservice.user.entity.dao.DepartmentPositionRelationDAO;
import com.microservice.user.exception.DepartmentPositionException;
import com.microservice.user.mapper.DepartmentPositionRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class DepartmentPositionService {

    @Autowired
    private DepartmentPositionRelationMapper dprMapper;

    public List<DepartmentPositionRelationDAO> getDPRListByDepartmentId(String departmentId) {
        if (StringUtil.isEmpty(departmentId)) {
            return new ArrayList<>();
        }
        return dprMapper.getDepartmentPositionRelationList(
                GetDepartmentPositionCondition.builder()
                        .departmentId(departmentId)
                        .build()
        );
    }

    public void deleteDPRByDepartmentId(String departmentId) {
        if (!StringUtil.isEmpty(departmentId)) {
            dprMapper.deleteDepartmentPositionRelation(
                    GetDepartmentPositionCondition.builder()
                            .departmentId(departmentId)
                            .build()
            );
        }
    }

    public List<DPRWithPositionDAO> getDPRWithPositionList(GetDepartmentPositionCondition condition, int start, int limit) {
        if ((condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())
                || (condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return dprMapper.getDPRWithPositionList(condition, start, limit);
    }

    public List<DPRWithPositionDAO> getDPRWithPositionList(GetDepartmentPositionCondition condition) {
        return getDPRWithPositionList(condition, 0, 0);
    }

    public int getDPRWithPositionCount(GetDepartmentPositionCondition condition) {
        if ((condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())
                || (condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())) {
            return 0;
        }
        return dprMapper.getDPRWithPositionCount(condition);
    }

    public int addDepartmentPositionRelation(String departmentId, String positionId) {
        return dprMapper.addDepartmentPositionRelation(
                DepartmentPositionRelationDAO.builder()
                        .departmentId(departmentId)
                        .positionId(positionId)
                        .build()
        );
    }

    public void updateDepartmentPositionRelation(String departmentId, String positionId) {
        dprMapper.deleteDepartmentPositionRelation(GetDepartmentPositionCondition.builder().departmentId(departmentId).build());
    }

    public void updatePositionDepartmentRelation(String positionId, String departmentId) {
        if (StringUtil.isEmpty(positionId)) {
            throw new DepartmentPositionException("69001", "职位id错误");
        }
        //删除所有此职位id的关联关系
        dprMapper.deleteDepartmentPositionRelation(GetDepartmentPositionCondition.builder().positionId(positionId).build());
        //添加新的关系
        if (!StringUtil.isEmpty(departmentId)) {
            if (dprMapper.addDepartmentPositionRelation(
                    DepartmentPositionRelationDAO.builder()
                            .departmentId(departmentId)
                            .positionId(positionId)
                            .build()
            ) <= 0) {
                throw new DepartmentPositionException("69002", "添加部门和职位关系失败");
            }
        }
    }

    public List<DepartmentPositionRelationDAO> getDPRList(GetDepartmentPositionCondition condition) {
        if ((condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())
                || (condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return dprMapper.getDepartmentPositionRelationList(condition);
    }

    public int deleteDPR(GetDepartmentPositionCondition condition) {
        return dprMapper.deleteDepartmentPositionRelation(condition);
    }
}
