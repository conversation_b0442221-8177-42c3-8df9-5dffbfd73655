package com.microservice.user.service;

import com.microservice.user.entity.condition.GetIdentityChatGroupCondition;
import com.microservice.user.entity.dao.IdentityChatGroupRelationDAO;
import com.microservice.user.exception.IdentityChatGroupRelationException;
import com.microservice.user.mapper.IdentityChatGroupRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class IdentityChatGroupService {

    @Autowired
    private IdentityChatGroupRelationMapper icgrMapper;


    public List<IdentityChatGroupRelationDAO> addICGRList(String chatGroupId, List<String> identityIdList, Map<String, Object> extraContent) {
        if (identityIdList == null || identityIdList.isEmpty()) {
            return new ArrayList<>();
        }
        identityIdList = identityIdList.stream().distinct().filter(identityId -> !StringUtil.isEmpty(identityId)).collect(Collectors.toList());
        //查询已存在的关系
        List<String> existIdentityIdList = getICGRList(
                GetIdentityChatGroupCondition.builder()
                        .chatGroupId(chatGroupId)
                        .build()
        ).stream().map(IdentityChatGroupRelationDAO::getIdentityId).collect(Collectors.toList());
        //过滤已存在的identity_id
        identityIdList.removeAll(existIdentityIdList);
        List<IdentityChatGroupRelationDAO> icgrList = new ArrayList<>();
        if (!identityIdList.isEmpty()) {
            icgrList = identityIdList.stream()
                    .map(identityId -> IdentityChatGroupRelationDAO.builder()
                            .identityId(identityId)
                            .chatGroupId(chatGroupId)
                            .createTime(new Date())
                            .extraContent(StringUtil.jsonEncode(extraContent))
                            .build())
                    .collect(Collectors.toList());
            if (icgrMapper.addIdentityChatGroupRelationList(icgrList) <= 0) {
                throw new IdentityChatGroupRelationException("61501", "添加用户和群聊关系失败");
            }
        }
        return icgrList;
    }

    public int getICGRCount(GetIdentityChatGroupCondition condition) {
        if ((condition.getChatGroupIdList() != null && condition.getChatGroupIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return icgrMapper.getIdentityChatGroupRelationCount(condition);
    }

    public List<IdentityChatGroupRelationDAO> getICGRList(GetIdentityChatGroupCondition condition) {
        return getICGRList(condition, 0, 0);
    }

    public List<IdentityChatGroupRelationDAO> getICGRList(GetIdentityChatGroupCondition condition, int start, int limit) {
        if ((condition.getChatGroupIdList() != null && condition.getChatGroupIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return icgrMapper.getIdentityChatGroupRelationList(condition, start, limit);
    }

    public IdentityChatGroupRelationDAO getICGR(GetIdentityChatGroupCondition condition) {
        return icgrMapper.getIdentityChatGroupRelation(condition);
    }

    public int deleteICGR(GetIdentityChatGroupCondition condition) {
        if (condition == null) return 0;
        if ((condition.getChatGroupIdList() != null && condition.getChatGroupIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return icgrMapper.deleteIdentityChatGroupRelation(condition);
    }

    public void updateICGR(IdentityChatGroupRelationDAO updateData) {
        updateData.setUpdateTime(new Date());
        if (icgrMapper.updateIdentityChatGroupRelation(
                updateData,
                GetIdentityChatGroupCondition.builder()
                        .identityId(updateData.getIdentityId())
                        .chatGroupId(updateData.getChatGroupId())
                        .build()
        ) <= 0) {
            throw new IdentityChatGroupRelationException("61502", "更新群成员失败");
        }
    }
}
