package com.microservice.user.service;

import com.microservice.user.entity.condition.GetIdentityPositionCondition;
import com.microservice.user.entity.dao.IPRWithIdentityDAO;
import com.microservice.user.entity.dao.IdentityPositionRelationDAO;
import com.microservice.user.exception.IdentityCompanyException;
import com.microservice.user.exception.IdentityPositionException;
import com.microservice.user.mapper.IdentityPositionRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class IdentityPositionService {

    @Autowired
    private IdentityPositionRelationMapper iprMapper;

    public void deleteIPRByIdentityId(String identityId) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityPositionException("64001", "身份id错误");
        }
        iprMapper.deleteIdentityPositionRelation(GetIdentityPositionCondition.builder().identityId(identityId).build());
    }

    public void addIdentityPositionRelationList(String identityId, List<String> positionIdList, Map<String, Object> extraContent) {
        if (positionIdList != null && !positionIdList.isEmpty()) {
            positionIdList = positionIdList.stream().distinct().filter(positionId -> !StringUtil.isEmpty(positionId)).collect(Collectors.toList());
            //查询已存在的关系
            List<String> existPositionIdList = getIPRList(
                    GetIdentityPositionCondition.builder()
                            .identityId(identityId)
                            .build()
            ).stream().map(IdentityPositionRelationDAO::getPositionId).collect(Collectors.toList());
            //过滤已存在的position_id
            positionIdList.removeAll(existPositionIdList);
            if (!positionIdList.isEmpty()) {
                iprMapper.addIdentityPositionRelationList(
                        positionIdList.stream()
                                .map(positionId -> IdentityPositionRelationDAO.builder()
                                        .positionId(positionId)
                                        .identityId(identityId)
                                        .extraContent(extraContent == null ? "{}" : StringUtil.jsonEncode(extraContent))
                                        .build())
                                .collect(Collectors.toList()));
            }
        }
    }

    public void updateIdentityPositionRelationList(String identityId, List<String> positionIdList, Map<String, Object> extraContent) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityCompanyException("64002", "身份id错误");
        }
        iprMapper.deleteIdentityPositionRelation(GetIdentityPositionCondition.builder()
                .identityId(identityId)
                .build());
        addIdentityPositionRelationList(identityId, positionIdList, extraContent);
    }

    public List<IdentityPositionRelationDAO> getIPRList(GetIdentityPositionCondition condition) {
        return getIPRList(condition, 0, 0);
    }

    public List<IdentityPositionRelationDAO> getIPRList(GetIdentityPositionCondition condition, int start, int limit) {
        if ((condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return iprMapper.getIdentityPositionRelationList(condition, start, limit);
    }

    public void deleteIPRByPositionId(String positionId) {
        if (StringUtil.isEmpty(positionId)) {
            throw new IdentityPositionException("64003", "职位id错误");
        }
        iprMapper.deleteIdentityPositionRelation(GetIdentityPositionCondition.builder().positionId(positionId).build());
    }

    public int getIPRCount(GetIdentityPositionCondition condition) {
        if ((condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())) {
            return 0;
        }
        return iprMapper.getIdentityPositionRelationCount(condition);
    }

    public List<IPRWithIdentityDAO> getIPRWithIdentityList(GetIdentityPositionCondition condition, int start, int limit) {
        if ((condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return iprMapper.getIPRWithIdentityList(condition, start, limit);
    }

    public int getIPRWithIdentityCount(GetIdentityPositionCondition condition) {
        if ((condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())) {
            return 0;
        }
        return iprMapper.getIPRWithIdentityCount(condition);
    }

    public List<IPRWithIdentityDAO> getIPRWithIdentityList(GetIdentityPositionCondition condition) {
        return getIPRWithIdentityList(condition, 0, 0);
    }

    public void deleteIdentityPositionRelation(String identityId, List<String> positionIdList) {
        if (!StringUtil.isEmpty(identityId) && !positionIdList.isEmpty()) {
            iprMapper.deleteIdentityPositionRelation(GetIdentityPositionCondition.builder()
                    .positionIdList(positionIdList)
                    .identityId(identityId)
                    .build());
        }
    }

    public int deleteIPR(GetIdentityPositionCondition condition) {
        return iprMapper.deleteIdentityPositionRelation(condition);
    }
}
