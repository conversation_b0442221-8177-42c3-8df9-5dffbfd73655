package com.microservice.user.service;

import com.microservice.user.entity.condition.GetRosterConfigCondition;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterFieldDTO;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterGroupDTO;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterTeamDTO;
import com.microservice.user.entity.request.ms.rosterconfig.*;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

public interface RosterConfigService {

    /**
     * 获取子集信息列表 关联子集下的分组数据
     * @param condition 请求对象
     * @param isRelatedData 是否关联数据 [0否、1是] 默认为0，关联子集下的分组数据
     * @param page 页码
     * @param limit 每页数量 默认0 查全部数据
     * @return 数据列表
     */
    List<RosterTeamDTO> getRosterTeamList(GetRosterConfigCondition condition, Integer isRelatedData, int page, int limit);

    /**
     * 获取分组信息列表
     * @param condition 请求对象
     * @param isRelatedData 是否关联数据 [0否、1是] 默认为0，关联分组下的字段数据
     * @param page 页码
     * @param limit 每页数量 默认0 查全部数据
     * @return 数据列表
     */
    List<RosterGroupDTO> getRosterGroupList(GetRosterConfigCondition condition, Integer isRelatedData, int page, int limit);

    /**
     * 获取字段列表数据
     * @param condition 添加对象
     * @param page 页码 默认位1
     * @param limit 每页数据量 默认0 返回全部数据
     * @return 数据列表
     */
    List<RosterFieldDTO> getRosterFieldList(GetRosterConfigCondition condition, int page, int limit);

    /**
     * 批量保存子集数据
     * @param request
     */
    void saveBatchRosterTeamInfo(SaveBatchRosterTeamRequest request);


    /**
     * 批量更新子集数据
     * @param rosterTeamRequestList
     */
    void updateBatchRosterTeamInfo(List<@Valid UpdateRosterTeamRequest> rosterTeamRequestList);

    /**
     * 删除子集信息
     * @param rosterTeamIdList
     * @return
     */
    void delRosterTeamInfo(List<String> rosterTeamIdList);

    /**
     * 批量添加分组
     * @param request
     */
    void saveBatchRosterGroupInfo(SaveBatchRosterGroupRequest request);

    /**
     * 编辑分组信息
     * @param updateRosterGroupRequestList 请求参数
     * @return
     */
    void updateBatchRosterGroupInfo(List<UpdateRosterGroupRequest> updateRosterGroupRequestList);

    /**
     * 删除分组信息
     * @param rosterGroupIdList 请求参数
     * @return
     */
    void delRosterGroupInfo(List<String> rosterGroupIdList);

    /**
     * 批量添加字段信息
     * @param request
     * @return
     */
    void addBatchRosterFieldInfo(SaveBatchRosterFieldRequest request);

    /**
     * 更新字段信息
     * @param fieldRequestList
     * @return
     */
    void updateRosterFieldInfo(List<UpdateRosterFieldRequest> fieldRequestList);

    /**
     * 删除字段信息
     * @param request 请求参数
     * @return
     */
    void delRosterFieldInfo(List<String> rosterFieldIdList);

    /**
     * 统计子集数量
     * @param condition 条件参数
     * @return
     */
    int countRosterTeam(GetRosterConfigCondition condition);

    /**
     * 根据条件统计分组数量
     * @param condition 条件对象
     * @return
     */
    int countRosterGroup(GetRosterConfigCondition condition);

    /**
     * 根据条件统计字段数量
     * @param condition 条件对象
     * @return
     */
    int countRosterField(GetRosterConfigCondition condition);

}
