package com.microservice.user.service;

import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.roster.RosterAddListDTO;
import com.microservice.user.entity.dto.ms.roster.RosterQueeryDTO;
import com.microservice.user.entity.dto.ms.roster.RosterUpdateDTO;
import com.microservice.user.entity.vo.RosterVO;

import java.util.List;

/**
 * &#064;DATE: 2024/9/5
 * &#064;AUTHOR: XSL
 *
 */
public interface RosterService {

    /**
     * 分页查询集合
     * @return
     */
    public DataListDTO<RosterVO> findAllByRosterDAO(RosterQueeryDTO rosterQueeryDTO);

    /**
     * 查询数据详情
     * @param id
     * @return
     */
    public RosterVO findByIdentityId(String identityId);

    /**
     * 新增一条数据
     * @param roster
     * @return
     */
    public void addRosterDAO(RosterAddListDTO rosterAddDTOList);

    /**
     * 更新数据
     * @param roster
     * @return
     */
    public void updateRosterDAO(List<RosterUpdateDTO> rosterList);

    /**
     * 删除数据
     * @param id
     * @return
     */
    public int deleteRosterDAO(String identityId);

}
