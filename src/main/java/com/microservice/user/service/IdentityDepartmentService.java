package com.microservice.user.service;

import com.microservice.user.entity.condition.GetIdentityDepartmentCondition;
import com.microservice.user.entity.dao.IDRWithIdentityDAO;
import com.microservice.user.entity.dao.IdentityDepartmentRelationDAO;
import com.microservice.user.entity.dto.ms.DepartmentFatherAndSonDTO;
import com.microservice.user.exception.IdentityCompanyException;
import com.microservice.user.exception.IdentityDepartmentException;
import com.microservice.user.mapper.IdentityDepartmentRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class IdentityDepartmentService {

    @Autowired
    private IdentityDepartmentRelationMapper idrMapper;

    public void deleteIDRByIdentityId(String identityId) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityDepartmentException("63001", "身份id错误");
        }
        idrMapper.deleteIdentityDepartmentRelation(GetIdentityDepartmentCondition.builder().identityId(identityId).build());
    }

    public void addIdentityDepartmentRelationList(String identityId, List<String> departmentIdList, Map<String, Object> extraContent) {
        if (departmentIdList != null && !departmentIdList.isEmpty()) {
            departmentIdList = departmentIdList.stream().distinct().filter(departmentId -> !StringUtil.isEmpty(departmentId)).collect(Collectors.toList());
            //查询已存在的关系
            List<String> existDepartmentIdList = getIDRList(
                    GetIdentityDepartmentCondition.builder()
                            .identityId(identityId)
                            .build()
            ).stream().map(IdentityDepartmentRelationDAO::getDepartmentId).collect(Collectors.toList());
            //过滤已存在的department_id
            departmentIdList.removeAll(existDepartmentIdList);
            if (!departmentIdList.isEmpty()) {
                idrMapper.addIdentityDepartmentRelationList(
                        departmentIdList.stream()
                                .map(departmentId -> IdentityDepartmentRelationDAO.builder()
                                        .departmentId(departmentId)
                                        .identityId(identityId)
                                        .extraContent(extraContent == null ? "{}" : StringUtil.jsonEncode(extraContent))
                                        .build())
                                .collect(Collectors.toList()));
            }
        }
    }

    public void updateIdentityDepartmentRelationList(String identityId, List<String> departmentIdList, Map<String, Object> extraContent) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityCompanyException("63002", "身份id错误");
        }
        idrMapper.deleteIdentityDepartmentRelation(GetIdentityDepartmentCondition.builder()
                .identityId(identityId)
                .build());
        addIdentityDepartmentRelationList(identityId, departmentIdList, extraContent);
    }

    /**
     * 获取身份部门关系列表
     *
     * @param condition
     * @param start
     * @param limit
     * @return
     */
    public List<IdentityDepartmentRelationDAO> getIDRList(GetIdentityDepartmentCondition condition, int start, int limit) {
        if ((condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return idrMapper.getIdentityDepartmentRelationList(condition, start, limit);
    }

    public List<IdentityDepartmentRelationDAO> getIDRList(GetIdentityDepartmentCondition condition) {
        return getIDRList(condition, 0, 0);
    }

    public int getIDRCount(GetIdentityDepartmentCondition condition) {
        if ((condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return idrMapper.getIdentityDepartmentRelationCount(condition);
    }

    public int getIDRDistinctCount(GetIdentityDepartmentCondition condition) {
        if ((condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return idrMapper.getIDRWithIdentityDistinctCount(condition);
    }

    public List<IDRWithIdentityDAO> getIDRWithIdentityList(GetIdentityDepartmentCondition condition, int start, int limit) {
        if ((condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return idrMapper.getIDRWithIdentityList(condition, start, limit);
    }

    public List<IDRWithIdentityDAO> getIDRWithIdentityList(GetIdentityDepartmentCondition condition) {
        return getIDRWithIdentityList(condition, 0, 0);
    }

    public void deleteIdentityDepartmentRelation(String identityId, List<String> departmentIdList) {
        if (!StringUtil.isEmpty(identityId) && !departmentIdList.isEmpty()) {
            idrMapper.deleteIdentityDepartmentRelation(GetIdentityDepartmentCondition.builder()
                    .departmentIdList(departmentIdList)
                    .identityId(identityId)
                    .build());
        }
    }

    public int deleteIDR(GetIdentityDepartmentCondition condition) {
        return idrMapper.deleteIdentityDepartmentRelation(condition);
    }

    public int getIDRWithIdentityCount(GetIdentityDepartmentCondition condition) {
        if ((condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return idrMapper.getIDRWithIdentityCount(condition);
    }

    /**
     * 获取有效身份关联关系
     *
     * @param departmentId
     * @return java.util.List<com.microservice.user.entity.dao.IdentityDepartmentRelationDAO>
     * <AUTHOR>
     * @date 2025/3/31 17:59
     **/
    public List<IdentityDepartmentRelationDAO> getIDRValidIdentityRelation(String departmentId) {
        return idrMapper.getIDRValidIdentityRelation(departmentId);
    }

    /**
     * 批量获取部门下的身份ID
     *
     * @param departmentIds 部门ID列表
     * @return Map<部门ID, 身份ID列表>
     */
    public Map<String, List<String>> batchGetIdentityIdsByDepartmentList(List<String> departmentIds) {
        List<IdentityDepartmentRelationDAO> relations = idrMapper.batchGetIdentityIdsByDepartments(departmentIds);
        return relations.stream()
                .collect(Collectors.groupingBy(
                        IdentityDepartmentRelationDAO::getDepartmentId,
                        Collectors.mapping(IdentityDepartmentRelationDAO::getIdentityId, Collectors.toList())
                ));
    }

    public List<DepartmentFatherAndSonDTO> getDepartmentFatherAndSon(List<String> departmentIdList) {
        return idrMapper.getDepartmentFatherAndSon(departmentIdList);
    }


}
