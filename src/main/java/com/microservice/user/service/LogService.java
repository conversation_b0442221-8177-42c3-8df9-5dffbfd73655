package com.microservice.user.service;

import com.microservice.user.entity.condition.GetLogCondition;
import com.microservice.user.entity.dao.LogInfoDAO;
import com.microservice.user.mapper.LogInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class LogService {

    @Autowired
    private LogInfoMapper logInfoMapper;

    public List<LogInfoDAO> getLogList(GetLogCondition condition, int start, int limit) {
        return logInfoMapper.getLogList(condition, start, limit);
    }

    public int getLogCount(GetLogCondition condition) {
        return logInfoMapper.getLogCount(condition);
    }

    public void addLog(int type, String opt, String content, Map<String, Object> extraContent, String appChannelId, String dataChannelId) {
        if (extraContent == null || extraContent.isEmpty()) {
            return;
        }
        //匹配变量
        String p = "\\{(.+?)\\}";
        Pattern r = Pattern.compile(p);
        Matcher m = r.matcher(content);
        while (m.find()) {
            String value = "";
            Object extraContentValue = extraContent.get(m.group(1));
            value = extraContentValue == null ? "" : extraContentValue.toString();
            content = content.replace(m.group(0), value);
        }
        logInfoMapper.addLog(LogInfoDAO.builder()
                .type(type)
                .opt(opt.substring(0, Math.min(opt.length(), 64)))
                .content(content.substring(0, Math.min(content.length(), 500)))
                .extraContent("{}")
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build());
    }

    public int deleteLog(GetLogCondition condition) {
        return logInfoMapper.deleteLog(condition);
    }
}
