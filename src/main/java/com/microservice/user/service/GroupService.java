package com.microservice.user.service;

import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.GroupDTO;
import com.microservice.user.exception.GroupException;
import com.microservice.user.mapper.GroupDataRelationMapper;
import com.microservice.user.mapper.GroupEventRelationMapper;
import com.microservice.user.mapper.GroupInfoMapper;
import com.microservice.user.mapper.GroupPageRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class GroupService {

    @Autowired
    private GroupInfoMapper groupInfoMapper;
    @Autowired
    private GroupEventRelationMapper gerMapper;
    @Autowired
    private GroupPageRelationMapper gprMapper;
    @Autowired
    private GroupDataRelationMapper gdrMapper;
    @Autowired
    private IdentityGroupService identityGroupService;
    @Autowired
    private GroupEventService groupEventService;
    @Autowired
    private GroupPageService groupPageService;
    @Autowired
    private GroupDataService groupDataService;

    public List<GroupDTO> getGroupList(GetGroupCondition condition) {
        if ((condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getGroupIdList() != null && condition.getGroupIdList().isEmpty())) {
            return new ArrayList<>();
        }
        List<GroupInfoDAO> groupInfoDAOList = groupInfoMapper.getGroupList(condition, 0, 0);
        return groupInfoDAOList.stream().map(GroupDTO::create).collect(Collectors.toList());
    }

    public List<GroupInfoDAO> getGroupList(GetGroupCondition condition, int start, int limit) {
        if ((condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getGroupIdList() != null && condition.getGroupIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return groupInfoMapper.getGroupList(condition, start, limit);
    }

    public int getGroupCount(GetGroupCondition condition) {
        return groupInfoMapper.getGroupCount(condition);
    }

    public GroupInfoDAO addGroup(String groupName, Map<String, Object> extraContent, Integer sort, String appChannelId, String dataChannelId) {
        if (extraContent == null || extraContent.isEmpty()) {
            extraContent = new HashMap<>() {{
                put("groupName", groupName);
            }};
        }
        GroupInfoDAO groupInfoDAO = new GroupInfoDAO();
        groupInfoDAO.setGroupId(StringUtil.createUuid());
        groupInfoDAO.setGroupName(groupName.substring(0, Math.min(groupName.length(), 30)));
        groupInfoDAO.setExtraContent(StringUtil.jsonEncode(extraContent));
        groupInfoDAO.setSort(sort);
        groupInfoDAO.setAppChannelId(appChannelId);
        groupInfoDAO.setDataChannelId(dataChannelId);
        groupInfoDAO.setCreateTime(new Date());
        groupInfoDAO.setUpdateTime(new Date());
        if (groupInfoMapper.addGroup(groupInfoDAO) <= 0) {
            throw new GroupException("61202", "添加用户组失败");
        }
        return groupInfoDAO;
    }

    public GroupInfoDAO getGroup(GetGroupCondition condition) {
        return groupInfoMapper.getGroup(condition);
    }

    public GroupInfoDAO getGroup(String groupId) {
        return groupInfoMapper.getGroup(GetGroupCondition.builder().groupId(groupId).build());
    }

    public void updateGroup(GroupInfoDAO groupInfoDAO) {
        groupInfoDAO.setUpdateTime(new Date());
        if (groupInfoDAO.getGroupName() != null) {
            groupInfoDAO.setGroupName(groupInfoDAO.getGroupName().substring(0, Math.min(groupInfoDAO.getGroupName().length(), 30)));
        }
        groupInfoMapper.updateGroup(groupInfoDAO, GetGroupCondition.builder().groupId(groupInfoDAO.getGroupId()).build());
    }

    public List<GroupDTO> createGroupEntity(List<String> groupIdList) {
        List<GroupDTO> groupDTOList = getGroupList(GetGroupCondition.builder().groupIdList(groupIdList).build());
        //关联事件id
        Map<String, List<String>> groupId2EventIdListMap = groupEventService.getGroupId2EventIdListMap(groupIdList);
        //关联页面id
        Map<String, List<String>> groupId2PageIdListMap = groupPageService.getGroupId2PageIdListMap(groupIdList);
        //关联数据
        Map<String, List<String>> groupId2DataListMap = groupDataService.getGroupId2DataListMap(groupIdList);
        //关联身份id
        Map<String, List<String>> groupId2IdentityIdListMap = identityGroupService.getGroupId2IdentityIdListMap(groupIdList);
        groupDTOList = groupDTOList.stream()
                .peek(groupDTO -> groupDTO.setEventIdList(groupId2EventIdListMap.getOrDefault(groupDTO.getGroupId(), new ArrayList<>())))
                .peek(groupDTO -> groupDTO.setPageIdList(groupId2PageIdListMap.getOrDefault(groupDTO.getGroupId(), new ArrayList<>())))
                .peek(groupDTO -> groupDTO.setDataList(groupId2DataListMap.getOrDefault(groupDTO.getGroupId(), new ArrayList<>())))
                .peek(groupDTO -> groupDTO.setIdentityIdList(groupId2IdentityIdListMap.getOrDefault(groupDTO.getGroupId(), new ArrayList<>())))
                .collect(Collectors.toList());
        return groupDTOList;
    }

    public GroupDTO createGroupEntity(String groupId) {
        List<GroupDTO> groupDTOList = createGroupEntity(new ArrayList<String>() {{
            add(groupId);
        }});
        return groupDTOList.size() > 0 ? groupDTOList.get(0) : null;
    }

    public void deleteGroup(String groupId, int isForce) {
        GroupInfoDAO groupInfoDAO = getGroup(groupId);
        if (groupInfoDAO == null) {
            return;
        }
        //检查用户
        List<IdentityGroupRelationDAO> igrList = identityGroupService.getIGRList(GetIdentityGroupCondition.builder().groupId(groupId).build());
        if (!igrList.isEmpty()) {
            if (isForce <= 0) {
                throw new GroupException("61201", "用户组已关联用户，不能删除");
            }
            identityGroupService.deleteIGRByGroupId(groupId);
        }
        //删除用户组和事件关系
        groupEventService.deleteGERByGroupId(groupId);
        //删除用户组和页面关系
        groupPageService.deleteGPRByGroupId(groupId);
        //删除用户组和数据关系
        groupDataService.deleteGDRByGroupId(groupId);
        //删除用户组
        groupInfoMapper.deleteGroup(GetGroupCondition.builder().groupId(groupId).build());
    }

    public int deleteGroup(GetGroupCondition condition) {
        return groupInfoMapper.deleteGroup(condition);
    }

    public void addGroupList(List<GroupInfoDAO> groupInfoDAOList) {
        if (!groupInfoDAOList.isEmpty()) {
            groupInfoMapper.addGroupList(groupInfoDAOList);
        }
    }
}
