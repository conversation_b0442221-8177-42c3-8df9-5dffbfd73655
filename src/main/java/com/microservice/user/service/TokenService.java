package com.microservice.user.service;

import com.microservice.user.constant.SystemConstant;
import com.microservice.user.constant.TokenConstant;
import com.microservice.user.entity.condition.GetIdentityGroupCondition;
import com.microservice.user.entity.condition.GetTokenCondition;
import com.microservice.user.entity.dao.IdentityGroupRelationDAO;
import com.microservice.user.entity.dao.TokenInfoDAO;
import com.microservice.user.entity.dto.ms.UserDTO;
import com.microservice.user.exception.TokenException;
import com.microservice.user.mapper.TokenInfoMapper;
import com.microservice.user.util.RedisUtil;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TokenService {

    @Autowired
    private UserService userService;
    @Autowired
    private TokenInfoMapper tokenInfoMapper;
    @Autowired
    private IdentityGroupService identityGroupService;
    @Autowired
    private SystemService systemService;


    public TokenInfoDAO getToken(GetTokenCondition condition) {
        return tokenInfoMapper.getToken(condition);
    }

    public TokenInfoDAO addToken(TokenInfoDAO tokenInfoDAO) {
        if (StringUtil.isEmpty(tokenInfoDAO.getTokenId())) {
            tokenInfoDAO.setTokenId(StringUtil.createUuid());
        }
        if (StringUtil.isEmpty(tokenInfoDAO.getToken())) {
            tokenInfoDAO.setToken(StringUtil.randomString(32) + (new Date().getTime() / 1000));
        }
        if (tokenInfoMapper.addToken(tokenInfoDAO) <= 0) {
            throw new TokenException("60001", "添加token失败");
        }
        return tokenInfoDAO;
    }

    public void updateToken(String tokenStatus, List<String> tokenList) {
        if (tokenList.isEmpty()) {
            return;
        }
        tokenInfoMapper.updateToken(
                TokenInfoDAO.builder()
                        .tokenStatus(tokenStatus)
                        .build(),
                GetTokenCondition.builder()
                        .tokenList(tokenList)
                        .build()
        );
    }

    public void updateToken(String tokenStatus, GetTokenCondition condition) {
        if (condition != null) {
            tokenInfoMapper.updateToken(
                    TokenInfoDAO.builder()
                            .tokenStatus(tokenStatus)
                            .updateTime(new Date())
                            .build(),
                    condition
            );
        }
    }

    public TokenInfoDAO checkToken(String token) {
        if (StringUtil.isEmpty(token)) {
            throw new TokenException("60001", "token错误");
        }
        TokenInfoDAO tokenInfoDAO = tokenInfoMapper.getToken(GetTokenCondition.builder()
                .token(token)
                .build());
        if (tokenInfoDAO == null) {
            throw new TokenException("60002", "token不存在");
        }
        if (!StringUtil.isEmpty(tokenInfoDAO.getTokenStatus())) {
            throw new TokenException("60003", "token失效:" + tokenInfoDAO.getTokenStatus());
        }
        long ttl = (tokenInfoDAO.getEndTimestamp() - (new Date().getTime() / 1000));
        if (ttl <= 0) {
            throw new TokenException("60004", "token过期");
        }
        return tokenInfoDAO;
    }

    public void logout(GetTokenCondition condition, String tokenStatus) {
        condition.setTokenStatus("");
        List<TokenInfoDAO> tokenInfoDAOList = tokenInfoMapper.getTokenList(condition, 0, 0);
        if (!tokenInfoDAOList.isEmpty()) {
            //更新token状态
            tokenInfoMapper.updateToken(
                    TokenInfoDAO.builder()
                            .tokenStatus(tokenStatus)
                            .updateTime(new Date())
                            .build(),
                    GetTokenCondition.builder()
                            .tokenIdList(tokenInfoDAOList.stream().map(TokenInfoDAO::getTokenId).collect(Collectors.toList()))
                            .build()
            );
            //清除缓存
            RedisUtil.del(tokenInfoDAOList.stream().map(token -> SystemConstant.REDIS_KEY_USER_TOKEN + token.getToken()).collect(Collectors.toList()));


            RedisUtil.del(tokenInfoDAOList.stream().map(token -> systemService.getUserEntityRedisKey(token.getAppChannelId(), token.getUserId())).collect(Collectors.toList()));
        }
    }

    public void logoutByGroupId(String groupId) {
        List<IdentityGroupRelationDAO> igrList = identityGroupService.getIGRList(GetIdentityGroupCondition.builder().groupId(groupId).build());
        if (!igrList.isEmpty()) {
            logout(
                    GetTokenCondition.builder().identityIdList(igrList.stream().map(IdentityGroupRelationDAO::getIdentityId).collect(Collectors.toList())).build(),
                    TokenConstant.tokenStatus.CHANGE_EVENT.getCode().toString()
            );
        }
    }

    public List<TokenInfoDAO> getLastUserTokenListGroupByIdentityId(List<String> identityIdList) {
        return tokenInfoMapper.getLastUserTokenListGroupByIdentityId(
                GetTokenCondition.builder()
                        .identityIdList(identityIdList)
                        .build()
        );
    }

    public List<TokenInfoDAO> getLastUserTokenListGroupByIdentityIdAndAppChannelId(String appChannelId, List<String> identityIdList) {
        return tokenInfoMapper.getLastUserTokenListGroupByIdentityIdAndAppChannelId(
                GetTokenCondition.builder()
                        .identityIdList(identityIdList)
                        .appChannelId(appChannelId)
                        .build()
        );
    }

    public List<TokenInfoDAO> getTokenList(GetTokenCondition condition) {
        return getTokenList(condition, 0, 0);
    }

    public List<TokenInfoDAO> getTokenList(GetTokenCondition condition, int start, int limit) {
        if ((condition.getTokenList() != null && condition.getTokenList().isEmpty())
                || (condition.getTokenIdList() != null && condition.getTokenIdList().isEmpty())
                || (condition.getUserIdList() != null && condition.getUserIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return tokenInfoMapper.getTokenList(condition, start, limit);
    }

    public void updateTokenData(String appChannelId, String userId) {
        TokenInfoDAO tokenInfoDAO = getToken(
                GetTokenCondition.builder()
                        .userId(userId)
                        .tokenStatus("")
                        .build()
        );
        if (tokenInfoDAO != null) {
            RedisUtil.set(
                    SystemConstant.REDIS_KEY_USER_TOKEN + tokenInfoDAO.getToken(),
                    StringUtil.jsonEncode(userService.createUserEntity(appChannelId, userId)),
                    TokenConstant.USER_LOGIN_EXPIRE
            );
        }
    }

    public int deleteToken(GetTokenCondition condition) {
        return tokenInfoMapper.deleteToken(condition);
    }

    public int getTokenCount(GetTokenCondition condition) {
        return tokenInfoMapper.getTokenCount(condition);
    }
}
