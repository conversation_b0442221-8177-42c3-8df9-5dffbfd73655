package com.microservice.user.service;

import com.microservice.user.entity.condition.GetCompanyDepartmentCondition;
import com.microservice.user.entity.condition.GetCompanyPositionCondition;
import com.microservice.user.entity.dao.CPRWithPositionDAO;
import com.microservice.user.entity.dao.CompanyDepartmentRelationDAO;
import com.microservice.user.entity.dao.CompanyPositionRelationDAO;
import com.microservice.user.mapper.CompanyPositionRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CompanyPositionService {

    @Autowired
    private CompanyPositionRelationMapper cprMapper;

    public List<CompanyPositionRelationDAO> getCPRListByCompanyId(String companyId) {
        return cprMapper.getCompanyPositionRelationList(
                GetCompanyPositionCondition.builder()
                        .companyId(companyId)
                        .build()
        );
    }

    public void deleteCPRByCompanyId(String companyId) {
        cprMapper.deleteCompanyPositionRelation(
                GetCompanyPositionCondition.builder()
                        .companyId(companyId)
                        .build()
        );
    }

    public List<CPRWithPositionDAO> getCPRWithPositionList(GetCompanyPositionCondition condition, int start, int limit) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return cprMapper.getCPRWithPositionList(condition, start, limit);
    }

    public int getCPRWithPositionCount(GetCompanyPositionCondition condition) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())) {
            return 0;
        }
        return cprMapper.getCPRWithPositionCount(condition);
    }

    public int addCompanyPositionRelation(String companyId, String positionId) {
        return cprMapper.addCompanyPositionRelation(
                CompanyPositionRelationDAO.builder()
                        .companyId(companyId)
                        .positionId(positionId)
                        .build()
        );
    }

    public List<CompanyPositionRelationDAO> getCPRList(GetCompanyPositionCondition condition) {
        if ((condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())
                || (condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return cprMapper.getCompanyPositionRelationList(condition);
    }

    public int deleteCPR(GetCompanyPositionCondition condition) {
        return cprMapper.deleteCompanyPositionRelation(condition);
    }
}
