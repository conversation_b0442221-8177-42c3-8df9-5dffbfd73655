package com.microservice.user.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.microservice.user.entity.condition.GetDepartmentCondition;
import com.microservice.user.entity.condition.GetIdentityCondition;
import com.microservice.user.entity.condition.GetUserCondition;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.DepartmentDTO;
import com.microservice.user.entity.dto.ms.IdentityDTO;
import com.microservice.user.entity.dto.ms.label.*;
import com.microservice.user.entity.request.ms.label.*;
import com.microservice.user.exception.CommonException;
import com.microservice.user.mapper.CompanyLabelRelationMapper;
import com.microservice.user.mapper.LabelInfoMapper;
import com.microservice.user.mapper.LabelManageScopeMapper;
import com.microservice.user.mapper.LabelRelationMapper;
import com.microservice.user.service.DepartmentService;
import com.microservice.user.service.IdentityService;
import com.microservice.user.service.LabelService;
import com.microservice.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.checkerframework.checker.units.qual.C;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LabelServiceImpl implements LabelService {
    private static final Logger log = LoggerFactory.getLogger(LabelServiceImpl.class);

    private final LabelInfoMapper labelInfoMapper;

    private final CompanyLabelRelationMapper companyLabelRelationMapper;

    private final LabelRelationMapper labelRelationMapper;

    private final LabelManageScopeMapper labelManageScopeMapper;

    private final IdentityService identityService;

    private final DepartmentService departmentService;

    private final UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LabelInfoDTO addLabel(AddLabelRequest request) {
        LabelInfoDAO labelInfoDAO = LabelInfoDAO.builder()
                .id(UUID.randomUUID().toString())
                .labelName(request.getLabelName())
                .parentId(StringUtils.isEmpty(request.getParentId()) ? "" : request.getParentId())
                .createTime(new Date())
                .dataChannelId(request.getDataChannelId())
                .appChannelId(request.getAppChannelId())
                .build();

        labelInfoMapper.insertSelective(labelInfoDAO);
        if (Objects.nonNull(request.getCompanyId())) {
            CompanyLabelRelationDAO companyLabelRelationDAO = CompanyLabelRelationDAO.builder()
                    .companyId(request.getCompanyId())
                    .labelId(labelInfoDAO.getId())
                    .createTime(new Date())
                    .build();
            companyLabelRelationMapper.insertSelective(companyLabelRelationDAO);
        }

        LabelInfoDTO labelInfoDTO = LabelInfoDTO.builder().build();
        BeanUtils.copyProperties(labelInfoDAO, labelInfoDTO);
        return labelInfoDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LabelInfoDTO updateLabel(UpdateLabelRequest request) {
        LabelInfoDAO labelInfoDAO = labelInfoMapper.selectByPrimaryKey(request.getId());
        if (Objects.isNull(labelInfoDAO)) {
            throw new CommonException("50000", "标签不存在");
        }

        labelInfoDAO.setLabelName(StringUtils.isEmpty(request.getLabelName()) ? labelInfoDAO.getLabelName() : request.getLabelName());
        if (Objects.nonNull(request.getStatus())) {
            labelInfoDAO.setStatus(request.getStatus());

            companyLabelRelationMapper.updateStatusByLabelId(labelInfoDAO.getId(), request.getStatus());
            labelRelationMapper.updateStatusByLabelId(labelInfoDAO.getId(), request.getStatus());
        }
        labelInfoMapper.updateByPrimaryKeySelective(labelInfoDAO);
        LabelInfoDTO labelInfoDTO = LabelInfoDTO.builder().build();
        BeanUtils.copyProperties(labelInfoDAO, labelInfoDTO);
        return labelInfoDTO;
    }

    @Override
    public DataListDTO<LabelInfoDTO> getLabelList(LabelQueryRequest request) {
        PageHelper.startPage(request.getPage(), request.getLimit());
        List<LabelInfoDTO> labelInfoDTOS = labelInfoMapper.getLabelList(request);
        // 如果传入了标签id列表 千万别传page 和limit 否则会影响查询结果
        if (Objects.nonNull(request.getLabelIds())) {
            List<LabelInfoDTO> existLabelIds = labelInfoMapper.getExistLabelIds(request.getLabelIds());
            labelInfoDTOS = existLabelIds;
        }

        PageInfo<LabelInfoDTO> pageInfo = new PageInfo<>(labelInfoDTOS);
        DataListDTO<LabelInfoDTO> dataListDTO = DataListDTO.<LabelInfoDTO>builder()
                .dataCount((int) pageInfo.getTotal())
                .dataList(pageInfo.getList())
                .build();
        return dataListDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addLabelMembers(AddLabelMembersRequest request) {
        LabelInfoDAO labelInfoDAO = labelInfoMapper.selectByPrimaryKey(request.getLabelId());
        if (Objects.isNull(labelInfoDAO)) {
            throw new CommonException("50000", "标签不存在");
        }

        List<LabelRelationDAO> labelRelationDAOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(request.getIdentityId())) {
            for (String identityId : request.getIdentityId()) {
                LabelRelationDAO labelRelationDAO = LabelRelationDAO.builder()
                        .labelId(request.getLabelId())
                        .relationId(identityId)
                        .relationType(2)
                        .status(1)
                        .createTime(new Date())
                        .extraContent(request.getExtraContent())
                        .build();
                List<LabelRelationDAO> relationDAOS = labelRelationMapper.selectByLabelIdAndRelationId(labelRelationDAO.getLabelId(), labelRelationDAO.getRelationId(), labelRelationDAO.getRelationType());
                if (CollectionUtils.isEmpty(relationDAOS)) {
                    labelRelationDAOS.add(labelRelationDAO);
                }
            }
        }

        if (!CollectionUtils.isEmpty(request.getDepartmentId())) {
            for (String departmentId : request.getDepartmentId()) {
                LabelRelationDAO labelRelationDAO = LabelRelationDAO.builder()
                        .labelId(request.getLabelId())
                        .relationId(departmentId)
                        .relationType(1)
                        .status(1)
                        .createTime(new Date())
                        .extraContent(request.getExtraContent())
                        .build();
                List<LabelRelationDAO> relationDAOS = labelRelationMapper.selectByLabelIdAndRelationId(labelRelationDAO.getLabelId(), labelRelationDAO.getRelationId(), labelRelationDAO.getRelationType());
                if (CollectionUtils.isEmpty(relationDAOS)) {
                    labelRelationDAOS.add(labelRelationDAO);
                }
            }
        }

        if (!CollectionUtils.isEmpty(request.getCompanyId())) {
            for (String companyId : request.getCompanyId()) {
                LabelRelationDAO labelRelationDAO = LabelRelationDAO.builder()
                        .labelId(request.getLabelId())
                        .relationId(companyId)
                        .relationType(3)
                        .status(1)
                        .createTime(new Date())
                        .extraContent(request.getExtraContent())
                        .build();
                List<LabelRelationDAO> relationDAOS = labelRelationMapper.selectByLabelIdAndRelationId(labelRelationDAO.getLabelId(), labelRelationDAO.getRelationId(), labelRelationDAO.getRelationType());
                if (CollectionUtils.isEmpty(relationDAOS)) {
                    labelRelationDAOS.add(labelRelationDAO);
                }
            }
        }

        if (!CollectionUtils.isEmpty(labelRelationDAOS)) {
            labelRelationMapper.insertBatch(labelRelationDAOS);
            for (LabelRelationDAO labelRelationDAO : labelRelationDAOS) {
                insertLabelManageScope(request, labelRelationDAO.getId());
            }
        }
    }

    private void insertLabelManageScope(AddLabelMembersRequest request, Integer labelRelationId) {
        LabelManageScopeDAO labelManageScopeDAO = LabelManageScopeDAO.builder()
                .labelId(request.getLabelId())
                .labelRelationId(labelRelationId)
                .relationId(request.getManageCompanyId())
                .relationType(3)
                .status(1)
                .createTime(new Date())
                .extraContent(request.getExtraContent())
                .build();
        labelManageScopeMapper.insertSelective(labelManageScopeDAO);
    }

    @Override
    public LabelDataListDTO<LabelMemberDTO> getLabelMembers(GetLabelMembersRequest request) {
        //获取该标签下的子标签
        if (Objects.nonNull(request.getIsChild()) && Objects.equals(request.getIsChild(), 1)) {
            List<String> childLabelIdList = getChildLabelIdList(request.getLabelId(), request.getLabelIdList());

            if (Objects.nonNull(request.getLabelId())) {
                childLabelIdList.add(request.getLabelId());
            }
            if (!CollectionUtils.isEmpty(request.getLabelIdList())) {
                childLabelIdList.addAll(request.getLabelIdList());
            }
            request.setLabelId(null);
            request.setLabelIdList(childLabelIdList);
        }

        List<LabelMemberDTO> labelRelationDAOS;
        PageInfo<LabelMemberDTO> pageInfo = new PageInfo<>();
        if (request.getLimit() > 0) {
            PageHelper.startPage(request.getPage(), request.getLimit());
            labelRelationDAOS = labelRelationMapper.selectByLabelId(request.getLabelId(), request.getLabelIdList(), request.getCondition());
            pageInfo = new PageInfo<>(labelRelationDAOS);
        } else {
            labelRelationDAOS = labelRelationMapper.selectByLabelId(request.getLabelId(), request.getLabelIdList(), request.getCondition());
        }


        Map<String, LabelMemberDTO> map = new HashMap<>();

        for (LabelMemberDTO labelMemberDTO : labelRelationDAOS) {
            List<LabelManageScopeDTO> labelManageScopeDTOS = labelManageScopeMapper.selectByLabelRelationId(labelMemberDTO.getId());
            Set<String> manageCompanyIds = new LinkedHashSet<>();
            Set<String> manageCompanyNames = new LinkedHashSet<>();
            Set<String> manageIdentityIds = new LinkedHashSet<>();
            Set<String> manageIdentityNames = new LinkedHashSet<>();
            Set<String> manageDepartmentIds = new LinkedHashSet<>();
            Set<String> manageDepartmentNames = new LinkedHashSet<>();
            for (LabelManageScopeDTO labelManageScopeDTO : labelManageScopeDTOS) {
                if (Objects.equals(labelManageScopeDTO.getRelationType(), "1")) {
                    manageDepartmentIds.add(labelManageScopeDTO.getRelationId());
                    manageDepartmentNames.add(labelManageScopeDTO.getManageScope());
                }
                if (Objects.equals(labelManageScopeDTO.getRelationType(), "2")) {
                    manageIdentityIds.add(labelManageScopeDTO.getRelationId());
                    manageIdentityNames.add(labelManageScopeDTO.getManageScope());
                }
                if (Objects.equals(labelManageScopeDTO.getRelationType(), "3")) {
                    manageCompanyIds.add(labelManageScopeDTO.getRelationId());
                    manageCompanyNames.add(labelManageScopeDTO.getManageScope());
                }
            }

            CompanyDepartmentUser companyDepartmentUser = CompanyDepartmentUser.builder()
                    .companyName(manageCompanyNames)
                    .companyId(manageCompanyIds)
                    .departmentId(manageDepartmentIds)
                    .departmentName(manageDepartmentNames)
                    .identityName(manageIdentityNames)
                    .identityId(manageIdentityIds).build();

            labelMemberDTO.setManageMrVisibility(companyDepartmentUser);
            labelMemberDTO.setManageScopes(labelManageScopeDTOS);


            if (!map.containsKey(labelMemberDTO.getRelationId())) {
                map.put(labelMemberDTO.getRelationId(), labelMemberDTO);
            }

        }


        List<LabelMemberDTO> resultList = new ArrayList<>(map.values());

        if (request.getLimit() > 0) {
            CompanyDepartmentUser mrVisibility = converCompanyDepartmentUser(labelRelationDAOS);
            LabelDataListDTO<LabelMemberDTO> dataListDTO = LabelDataListDTO.<LabelMemberDTO>builder()
                    .dataCount((int) pageInfo.getTotal())
                    .dataList(resultList)
                    .mrVisibility(mrVisibility)
                    .build();
            return dataListDTO;
        } else {
            CompanyDepartmentUser mrVisibility = converCompanyDepartmentUser(labelRelationDAOS);
            LabelDataListDTO<LabelMemberDTO> dataListDTO = LabelDataListDTO.<LabelMemberDTO>builder()
                    .dataCount(resultList.size())
                    .dataList(resultList)
                    .mrVisibility(mrVisibility)
                    .build();
            return dataListDTO;
        }
    }

    private List<String> getChildLabelIdList(String labelId, List<String> labelIdList) {
        List<LabelInfoDAO> labelInfoDAOS = labelInfoMapper.getLabelListByParentId(labelId, labelIdList);
        if (CollectionUtils.isEmpty(labelInfoDAOS)) {
            return null;
        } else {
            List<String> childLabelIdList = labelInfoDAOS.stream().map(LabelInfoDAO::getId).collect(Collectors.toList());
            //递归查询子标签
            List<String> subLabelIdList = getChildLabelIdList(null, childLabelIdList);

            if (!CollectionUtils.isEmpty(subLabelIdList)) {
                childLabelIdList.addAll(subLabelIdList);
            }
            return childLabelIdList;
        }
    }

    private CompanyDepartmentUser converCompanyDepartmentUser(List<LabelMemberDTO> labelRelationIds) {
        Set<String> companyIds = labelRelationIds.stream()
                .filter(labelMemberDTO -> Objects.equals(labelMemberDTO.getRelationType(), 3))
                .map(LabelMemberDTO::getRelationId)
                .collect(Collectors.toSet());

        Set<String> companyNames = labelRelationIds.stream()
                .filter(labelMemberDTO -> Objects.equals(labelMemberDTO.getRelationType(), 3))
                .map(LabelMemberDTO::getName)
                .collect(Collectors.toSet());


        Set<String> identityIds = labelRelationIds.stream()
                .filter(labelMemberDTO -> Objects.equals(labelMemberDTO.getRelationType(), 2))
                .map(LabelMemberDTO::getRelationId)
                .collect(Collectors.toSet());


        Set<String> identityNames = labelRelationIds.stream()
                .filter(labelMemberDTO -> Objects.equals(labelMemberDTO.getRelationType(), 2))
                .map(LabelMemberDTO::getName)
                .collect(Collectors.toSet());

        Set<String> departmentIds = labelRelationIds.stream()
                .filter(labelMemberDTO -> Objects.equals(labelMemberDTO.getRelationType(), 1))
                .map(LabelMemberDTO::getRelationId)
                .collect(Collectors.toSet());


        Set<String> departmentNames = labelRelationIds.stream()
                .filter(labelMemberDTO -> Objects.equals(labelMemberDTO.getRelationType(), 1))
                .map(LabelMemberDTO::getName)
                .collect(Collectors.toSet());

        CompanyDepartmentUser companyDepartmentUser = CompanyDepartmentUser.builder()
                .companyName(companyNames)
                .companyId(companyIds)
                .departmentId(departmentIds)
                .departmentName(departmentNames)
                .identityName(identityNames)
                .identityId(identityIds).build();

        return companyDepartmentUser;
    }

    @Override
    public void updateLabelMember(UpdateLabelMemberRequest request) {
        LabelRelationDAO labelRelationDAO = labelRelationMapper.selectByPrimaryKey(request.getId());
        if (Objects.isNull(labelRelationDAO)) {
            throw new CommonException("50000", "标签成员不存在");
        }
        labelRelationDAO.setStatus(Objects.isNull(request.getStatus()) ? labelRelationDAO.getStatus() : request.getStatus());
        labelRelationMapper.updateByPrimaryKeySelective(labelRelationDAO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setLabelManageScope(SetLabelManageScopeRequest request) {
        LabelInfoDAO labelInfoDAO = labelInfoMapper.selectByPrimaryKey(request.getLabelId());
        if (Objects.isNull(labelInfoDAO)) {
            throw new CommonException("50000", "标签不存在");
        }

        LabelRelationDAO labelRelationDAO = labelRelationMapper.selectByPrimaryKey(request.getLabelRelationId());
        if (Objects.isNull(labelRelationDAO)) {
            throw new CommonException("50000", "标签成员不存在");
        }

        List<LabelManageScopeDAO> labelManageScopeDAOS = new ArrayList<>();

        if (!CollectionUtils.isEmpty(request.getIdentityId())) {
            for (String identityId : request.getIdentityId()) {
                LabelManageScopeDAO labelManageScopeDAO = LabelManageScopeDAO.builder()
                        .labelId(request.getLabelId())
                        .labelRelationId(request.getLabelRelationId())
                        .relationId(identityId)
                        .relationType(2)
                        .status(1)
                        .createTime(new Date())
                        .extraContent(request.getExtraContent())
                        .build();
                labelManageScopeDAOS.add(labelManageScopeDAO);
            }
        }
        if (!CollectionUtils.isEmpty(request.getDepartmentId())) {
            for (String departmentId : request.getDepartmentId()) {
                LabelManageScopeDAO labelManageScopeDAO = LabelManageScopeDAO.builder()
                        .labelId(request.getLabelId())
                        .labelRelationId(request.getLabelRelationId())
                        .relationId(departmentId)
                        .relationType(1)
                        .status(1)
                        .createTime(new Date())
                        .extraContent(request.getExtraContent())
                        .build();
                labelManageScopeDAOS.add(labelManageScopeDAO);
            }
        }

        if (!CollectionUtils.isEmpty(request.getCompanyId())) {
            for (String companyId : request.getCompanyId()) {
                LabelManageScopeDAO labelManageScopeDAO = LabelManageScopeDAO.builder()
                        .labelId(request.getLabelId())
                        .labelRelationId(request.getLabelRelationId())
                        .relationId(companyId)
                        .relationType(3)
                        .status(1)
                        .createTime(new Date())
                        .extraContent(request.getExtraContent())
                        .build();
                labelManageScopeDAOS.add(labelManageScopeDAO);
            }
        }

        labelManageScopeMapper.updateStatusByLabelRelationId(request.getLabelId(), request.getLabelRelationId(), 0);

        labelManageScopeMapper.insertBatch(labelManageScopeDAOS);
    }

    @Override
    public List<String> getLabelAuditorId(GetLabelAuditorIdRequest request) {
        //获取该标签下的子标签
        if (Objects.nonNull(request.getIsChild()) && Objects.equals(request.getIsChild(), 1)) {
            List<String> childLabelIdList = getChildLabelIdList(request.getLabelId(), request.getLabelIdList());

            if (Objects.nonNull(request.getLabelId())) {
                childLabelIdList.add(request.getLabelId());
            }
            if (!CollectionUtils.isEmpty(request.getLabelIdList())) {
                childLabelIdList.addAll(request.getLabelIdList());
            }
            request.setLabelId(null);
            request.setLabelIdList(childLabelIdList);
        }

        Set<LabelRelationDTO> allLabelScopeDAOS = new HashSet<>();
        //获取标签管理范围包含该用户企业的标签信息
        Set<LabelRelationDTO> companyLabelScopeDAOS = labelManageScopeMapper.selectByRelationId(request.getLabelId(), request.getLabelIdList(), Arrays.asList(request.getCompanyId()), 3);
        allLabelScopeDAOS.addAll(companyLabelScopeDAOS);
        //获取标签管理范围包含该用户身份的标签信息
        List<IdentityDTO> identityDTOList = identityService.getIdentityList(GetIdentityCondition.builder().userId(request.getUserId()).build());
        List<String> identityIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(identityDTOList)) {
            identityIdList = identityDTOList.stream()
                    .map(IdentityDTO::getIdentityId)
                    .collect(Collectors.toList());
            Set<LabelRelationDTO> identityLabelScopeDAOS = labelManageScopeMapper.selectByRelationId(request.getLabelId(), request.getLabelIdList(), identityIdList, 2);
            allLabelScopeDAOS.addAll(identityLabelScopeDAOS);
        }

        //获取标签管理范围包含该用户部门的标签信息
        List<DepartmentDTO> departmentDTOList = departmentService.getDepartmentList(GetDepartmentCondition.builder().departmentIdList(identityIdList).build());
        if (!CollectionUtils.isEmpty(departmentDTOList)) {
            List<String> departmentIdList = departmentDTOList.stream()
                    .map(DepartmentDTO::getDepartmentId)
                    .collect(Collectors.toList());
            Set<LabelRelationDTO> departmentLabelScopeDAOS = labelManageScopeMapper.selectByRelationId(request.getLabelId(), request.getLabelIdList(), departmentIdList, 1);
            allLabelScopeDAOS.addAll(departmentLabelScopeDAOS);
        }

        if (CollectionUtils.isEmpty(allLabelScopeDAOS)) {
            return Collections.emptyList();
        }

        Map<String, List<LabelRelationDTO>> labelRelationMap = allLabelScopeDAOS.stream()
                .collect(Collectors.groupingBy(LabelRelationDTO::getRelationType));
        List<UserInfoDAO> allUserInfoDAOS = new ArrayList<>();
        for (String relationType : labelRelationMap.keySet()) {
            if (Objects.equals(relationType, "1")) {
                //查询部门下的所有用户
                List<String> departmentIds = labelRelationMap.get(relationType).stream()
                        .map(LabelRelationDTO::getRelationId)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(departmentIds)) {
                    continue;
                }
                List<String> identityIds = identityService.getIdentityByDepartmentIds(departmentIds);
                if (CollectionUtils.isEmpty(identityIds)) {
                    continue;
                }
                List<UserInfoDAO> userInfoDAOList = userService.getUserList(GetUserCondition.builder().identityIdList(identityIds).build());
                allUserInfoDAOS.addAll(userInfoDAOList);
            } else if (Objects.equals(relationType, "2")) {
                //查询身份下的所有用户
                List<String> identityIds = labelRelationMap.get(relationType).stream()
                        .map(LabelRelationDTO::getRelationId)
                        .collect(Collectors.toList());

                identityIds = identityService.getIdentityByEmployeeStatus(identityIds);

                if (CollectionUtils.isEmpty(identityIds)) {
                    continue;
                }
                List<UserInfoDAO> userInfoDAOList = userService.getUserList(GetUserCondition.builder().identityIdList(identityIds).build());
                allUserInfoDAOS.addAll(userInfoDAOList);
            } else if (Objects.equals(relationType, "3")) {
                //查询企业下的所有用户
                for (LabelRelationDTO labelRelationDTO : labelRelationMap.get(relationType)) {
                    List<DepartmentDTO> departmentDTOS = departmentService.getDepartmentList(GetDepartmentCondition.builder().companyId(labelRelationDTO.getRelationId()).build());
                    List<String> departmentIds = departmentDTOS.stream()
                            .map(DepartmentDTO::getDepartmentId)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(departmentDTOS) || CollectionUtils.isEmpty(departmentIds)) {
                        continue;
                    }
                    List<String> identityIds = identityService.getIdentityByDepartmentIds(departmentIds);
                    if (CollectionUtils.isEmpty(identityIds)) {
                        continue;
                    }
                    List<UserInfoDAO> userInfoDAOList = userService.getUserList(GetUserCondition.builder().identityIdList(identityIds).build());
                    allUserInfoDAOS.addAll(userInfoDAOList);
                }
            }
        }

        if (CollectionUtils.isEmpty(allUserInfoDAOS)) {
            return Collections.emptyList();
        }

        List<String> userIdList = allUserInfoDAOS.parallelStream()
                .filter(Objects::nonNull)
                .map(UserInfoDAO::getUserId).distinct()
                .collect(Collectors.toList());
        return userIdList;
    }

    @Override
    public DataListDTO<LabelInfoDTO> getLabelListByUser(QueryUserLabelRequest request) {
        List<LabelInfoDTO> labelInfoDTOList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(request.getIdentityId())) {
            labelInfoDTOList.addAll(labelInfoMapper.getLabelListByUser(request.getIdentityId(), 2));
        }


        if (!CollectionUtils.isEmpty(request.getDepartmentId())) {
            labelInfoDTOList.addAll(labelInfoMapper.getLabelListByUser(request.getIdentityId(), 1));
        }

        if (!CollectionUtils.isEmpty(request.getCompanyId())) {
            labelInfoDTOList.addAll(labelInfoMapper.getLabelListByUser(request.getIdentityId(), 3));
        }


        DataListDTO<LabelInfoDTO> dataListDTO = DataListDTO.<LabelInfoDTO>builder()
                .dataCount(labelInfoDTOList.size())
                .dataList(labelInfoDTOList)
                .build();
        return dataListDTO;
    }

    @Override
    public void addLabelMembersByIdentity(AddLabelMembersRequest request) {
        if (CollectionUtils.isEmpty(request.getLabelIdList())) {
            return;
        }
        for (String labelId : request.getLabelIdList()) {
            request.setLabelId(labelId);
            this.addLabelMembers(request);
        }
    }
}
