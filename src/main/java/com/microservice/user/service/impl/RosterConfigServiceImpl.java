package com.microservice.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.microservice.user.common.Common;
import com.microservice.user.constant.RosterConfigConstant;
import com.microservice.user.entity.condition.GetCompanyCondition;
import com.microservice.user.entity.condition.GetRosterConfigCondition;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterFieldDTO;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterGroupDTO;
import com.microservice.user.entity.dto.ms.rosterconfig.RosterTeamDTO;
import com.microservice.user.entity.request.ms.rosterconfig.*;
import com.microservice.user.exception.CommonException;
import com.microservice.user.exception.CommonException;
import com.microservice.user.mapper.RosterDictMapper;
import com.microservice.user.mapper.RosterFieldMapper;
import com.microservice.user.mapper.RosterGroupMapper;
import com.microservice.user.mapper.RosterTeamMapper;
import com.microservice.user.service.CompanyService;
import com.microservice.user.service.RosterConfigService;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RosterConfigServiceImpl implements RosterConfigService {

    @Autowired
    private RosterTeamMapper rosterTeamMapper;
    @Autowired
    private RosterGroupMapper rosterGroupMapper;
    @Autowired
    private RosterFieldMapper rosterFieldMapper;
    @Autowired
    private RosterDictMapper rosterDictMapper;
    @Autowired
    private CompanyService companyService;

    @Override
    public List<RosterTeamDTO> getRosterTeamList(GetRosterConfigCondition condition, Integer isRelatedData, int page, int limit) {

        /*
         * 获取子集列表
         */
        List<RosterTeamDAO> rosterTeamDAOS = rosterTeamMapper.selectRosterTeamList(condition, (page - 1) * limit, limit);
        if (rosterTeamDAOS == null || rosterTeamDAOS.isEmpty()) {
            return new ArrayList<>();
        }
        List<RosterTeamDTO> rosterTeamDTOList = rosterTeamDAOS.stream().map(rosterTeamDAO -> {
            return RosterTeamDTO.builder()
                    .id(rosterTeamDAO.getId())
                    .field(rosterTeamDAO.getField())
                    .name(rosterTeamDAO.getTitle())
                    .isShow(rosterTeamDAO.getIsShow())
                    .sort(rosterTeamDAO.getSort())
                    .remark(rosterTeamDAO.getRemark())
                    .rosterGroupList(new ArrayList<>())
                    .isSystem(rosterTeamDAO.getIsSystem())
                    .countMax(rosterTeamDAO.getCountMax())
                    .companyId(rosterTeamDAO.getCompanyId())
                    .appChannelId(rosterTeamDAO.getAppChannelId())
                    .dataChannelId(rosterTeamDAO.getDataChannelId())
                    .createTime(TimeUtil.date2String(rosterTeamDAO.getCreateTime()))
                    .updateTime(TimeUtil.date2String(rosterTeamDAO.getUpdateTime()))
                    .build();
        }).collect(Collectors.toList());

        // isRelatedData 设置默认值为0，默认不需要关联数据
        isRelatedData = isRelatedData == null ? 0 : isRelatedData;
        if (isRelatedData > 0) { // 需要关联数据
            /*
             * 获取分组列表
             */
            List<String> rosterTeamIds = rosterTeamDAOS.stream().map(RosterTeamDAO::getId).collect(Collectors.toList());
            condition.setPrimaryKeyIds(rosterTeamIds); // 设置子集主键id列表用于查询关联的分组数据
            List<RosterGroupDTO> rosterGroupDTOS = this.getRosterGroupList(condition, 1, 1, 0);
            if (rosterGroupDTOS != null && !rosterGroupDTOS.isEmpty()) {
                Map<String, List<RosterGroupDTO>> rosterTeamIdRosterGroupDTOMap = rosterGroupDTOS.stream().collect(Collectors.groupingBy(RosterGroupDTO::getRosterRuleTeamId));
                for (RosterTeamDTO rosterTeamDTO : rosterTeamDTOList) {
                    List<RosterGroupDTO> rosterGroupDTOList= rosterTeamIdRosterGroupDTOMap.getOrDefault(rosterTeamDTO.getId(), new ArrayList<>());
                    CollectionUtil.sort(rosterGroupDTOList, Comparator.comparingInt(RosterGroupDTO::getSort));
                    rosterTeamDTO.setRosterGroupList(rosterGroupDTOList);
                }
            }
        }
        return rosterTeamDTOList;
    }

    @Override
    public List<RosterGroupDTO> getRosterGroupList(GetRosterConfigCondition condition, Integer isRelatedData, int page, int limit) {

        /*
         * 获取分组列表
         */
        List<RosterGroupDAO> rosterGroupDAOS = rosterGroupMapper.selectRosterGroupList(condition, (page - 1) * limit, limit);
        if (rosterGroupDAOS == null || rosterGroupDAOS.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> rosterGroupIdList = rosterGroupDAOS.stream().map(RosterGroupDAO::getId).collect(Collectors.toList());
        List<RosterGroupDTO> rosterGroupDTOList = rosterGroupDAOS.stream().map(rosterGroupDAO -> {
            return RosterGroupDTO.builder()
                    .id(rosterGroupDAO.getId())
                    .name(rosterGroupDAO.getName())
                    .isShow(rosterGroupDAO.getIsShow())
                    .sort(rosterGroupDAO.getSort())
                    .field(rosterGroupDAO.getField())
                    .isAdd(rosterGroupDAO.getIsAdd())
                    .rosterFiledList(new ArrayList<>())
                    .isSystem(rosterGroupDAO.getIsSystem())
                    .companyId(rosterGroupDAO.getCompanyId())
                    .countMax(rosterGroupDAO.getCountMax())
                    .remark(rosterGroupDAO.getRemark())
                    .appChannelId(rosterGroupDAO.getAppChannelId())
                    .dataChannelId(rosterGroupDAO.getDataChannelId())
                    .rosterRuleTeamId(rosterGroupDAO.getRosterRuleTeamId())
                    .createTime(TimeUtil.date2String(rosterGroupDAO.getCreateTime()))
                    .updateTime(TimeUtil.date2String(rosterGroupDAO.getUpdateTime()))
                    .build();
        }).collect(Collectors.toList());

        // isRelatedData 设置默认值为0，默认不需要关联数据
        isRelatedData = isRelatedData == null ? 0 : isRelatedData;
        if (isRelatedData > 0) {
            condition.setPrimaryKeyIds(rosterGroupIdList);
            /*
             * 获取分组下字段列表
             */
            List<RosterFieldDTO> rosterFieldDTOS = this.getRosterFieldList(condition, 1, 0);
            if (rosterFieldDTOS != null && !rosterFieldDTOS.isEmpty()) {
                Map<String, List<RosterFieldDTO>> rosterRuleGroupIdRosterFieldMap = rosterFieldDTOS.stream().collect(Collectors.groupingBy(RosterFieldDTO::getRosterRuleGroupId));
                for (RosterGroupDTO rosterGroupDTO : rosterGroupDTOList) {
                    List<RosterFieldDTO> rosterFieldDTOList = rosterRuleGroupIdRosterFieldMap.getOrDefault(rosterGroupDTO.getId(), new ArrayList<>());
                    CollectionUtil.sort(rosterFieldDTOList, Comparator.comparingInt(RosterFieldDTO::getSort));
                    rosterGroupDTO.setRosterFiledList(rosterFieldDTOList);
                }
            }
        }
        return rosterGroupDTOList;
    }

    @Override
    public List<RosterFieldDTO> getRosterFieldList(GetRosterConfigCondition condition, int page, int limit) {

        /*
         * 获取字段列表
         */
        List<RosterFieldDAO> rosterFieldDAOS = rosterFieldMapper.selectRosterFieldList(condition, (page - 1) * limit, limit);
        if (rosterFieldDAOS == null || rosterFieldDAOS.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> fieldList = rosterFieldDAOS.stream().map(RosterFieldDAO::getField).collect(Collectors.toList());
        List<RosterFieldDTO> rosterFieldDTOList = rosterFieldDAOS.stream().map(rosterFieldDAO -> {
            return RosterFieldDTO.builder()
                    .id(rosterFieldDAO.getId())
                    .field(rosterFieldDAO.getField())
                    .name(rosterFieldDAO.getName())
                    .isSystem(rosterFieldDAO.getIsSystem())
                    .rosterRuleTeamId(rosterFieldDAO.getRosterRuleTeamId())
                    .rosterRuleGroupId(rosterFieldDAO.getRosterRuleGroupId())
                    .isNonNull(rosterFieldDAO.getIsNonNull())
                    .type(rosterFieldDAO.getType())
                    .sort(rosterFieldDAO.getSort())
                    .rosterDictList(new ArrayList<>())
                    .length(rosterFieldDAO.getLength())
                    .isShow(rosterFieldDAO.getIsShow())
                    .companyId(rosterFieldDAO.getCompanyId())
                    .appChannelId(rosterFieldDAO.getAppChannelId())
                    .dataChannelId(rosterFieldDAO.getDataChannelId())
                    .createTime(TimeUtil.date2String(rosterFieldDAO.getCreateTime()))
                    .updateTime(TimeUtil.date2String(rosterFieldDAO.getUpdateTime()))
                    .build();
        }).collect(Collectors.toList());

        /*
         * 获取字段下数据字典
         */
        condition.setRosterFields(fieldList); // 设置字段参数id查询条件
        List<RosterDictDAO> rosterDictDAOS = rosterDictMapper.selectRosterDictList(condition);
        if (rosterDictDAOS != null && !rosterDictDAOS.isEmpty()) {
            Map<String, List<RosterDictDAO>> fieldIdRosterDictMap = rosterDictDAOS.stream().collect(Collectors.groupingBy(RosterDictDAO::getRosterRuleFieldId));
            for (RosterFieldDTO rosterFieldDTO : rosterFieldDTOList) {
                rosterFieldDTO.setRosterDictList(fieldIdRosterDictMap.getOrDefault(rosterFieldDTO.getField(), new ArrayList<>()));
            }
        }
        return rosterFieldDTOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchRosterTeamInfo(SaveBatchRosterTeamRequest request) {

        // 如果参数中不传渠道号就在请求头中获取
        String appChannelId = request.getAppChannelId() != null ? request.getAppChannelId() : Common.getAppChannelId();
        String dataChannelId = request.getDataChannelId() != null ? request.getDataChannelId() : Common.getDataChannelId();
        if (StringUtil.isEmpty(request.getCompanyId())) {
            throw new CommonException("50000", "公司id不能为空");
        }
        CompanyInfoDAO company = companyService.getCompany(GetCompanyCondition.builder()
                .companyId(request.getCompanyId()).build());
        if (Objects.isNull(company)) {
            throw new CommonException("50000", "公司不存在");
        }
        // 校验参数是否又不合要求数据
        List<AddRosterTeamRequest> rosterTeamRequestList = request.getRosterTeamRequestList();
        if (rosterTeamRequestList == null || rosterTeamRequestList.isEmpty()) {
            throw new CommonException("50000", "子集列表不能为空");
        }

        // 实体信息数据
        for (AddRosterTeamRequest rosterTeamRequest : rosterTeamRequestList) {

            // 判断名称是否重复
            int countByName = rosterTeamMapper.countRosterTeam(GetRosterConfigCondition.builder()
                    .appChannelId(appChannelId).dataChannelId(dataChannelId)
                    .companyId(request.getCompanyId())
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                    .name(rosterTeamRequest.getName()).build());
            if (countByName > 0) {
                throw new CommonException("50001", "名称重复");
            }
            Integer sortMax = rosterTeamRequest.getSort();
            if (sortMax == null) {
                // 没有传sort 从数据库获取最sort最大数据+1
                sortMax = rosterTeamMapper.getSortMax(GetRosterConfigCondition.builder().appChannelId(appChannelId)
                        .dataChannelId(dataChannelId).companyId(request.getCompanyId()).build());
                if (sortMax == null) { // 数据库中没有数据 默认0
                    sortMax = 0;
                } else {
                    sortMax += 1;
                }
            }
            RosterTeamDAO rosterTeamDAO = RosterTeamDAO.builder()
                    .id(StringUtil.createUuid())
                    .field(!StringUtil.isEmpty(rosterTeamRequest.getField()) ? rosterTeamRequest.getField() : StringUtil.createRandomString(16))
                    .title(rosterTeamRequest.getName())
                    .isShow(rosterTeamRequest.getIsShow() == null ? RosterConfigConstant.IsShow.SHOW.getCode() : rosterTeamRequest.getIsShow()) // 不传默认为显示
                    .companyId(request.getCompanyId())
                    .countMax(rosterTeamRequest.getGroupCountMax() == null ? RosterConfigConstant.ROSTER_GROUP_MAX_COUNT : rosterTeamRequest.getGroupCountMax()) // 子集下分组上限限制
                    .sort(sortMax) // 设置默认显示顺序
                    .remark(rosterTeamRequest.getRemark() != null ? rosterTeamRequest.getRemark() : "")
                    .isSystem(rosterTeamRequest.getIsSystem() != null ? rosterTeamRequest.getIsSystem() : RosterConfigConstant.IsSystem.NO_SYSTEM.getCode()) // 设置是否系统内置参数为0
                    .createTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .updateTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .appChannelId(appChannelId)
                    .dataChannelId(dataChannelId)
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                    .build();
            rosterTeamMapper.insertRosterTeam(rosterTeamDAO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchRosterTeamInfo(List<UpdateRosterTeamRequest> rosterTeamRequestList) {

        if (rosterTeamRequestList == null || rosterTeamRequestList.isEmpty()) {
            throw new CommonException("50000", "子集列表不能为空");
        }
        for (UpdateRosterTeamRequest updateRosterTeamRequest : rosterTeamRequestList) {

            if (StringUtil.isEmpty(updateRosterTeamRequest.getRosterTeamId())) {
                throw new CommonException("50000", "子集信息id不能为空");
            }
            if (Objects.nonNull(updateRosterTeamRequest.getSort()) && updateRosterTeamRequest.getSort() < 0) {
                throw new CommonException("50002", "参数错误，不能为负数");
            }

            if (Objects.nonNull(updateRosterTeamRequest.getGroupCountMax()) && updateRosterTeamRequest.getGroupCountMax() < 0) {
                throw new CommonException("50002", "参数错误，不能为负数");
            }

            if (Objects.nonNull(updateRosterTeamRequest.getIsShow()) &&
                    (updateRosterTeamRequest.getIsShow() < 0 || updateRosterTeamRequest.getIsShow() > 1)) {
                throw new CommonException("50002", "参数错误");
            }

            /*
             * 判断子集信息是否存在
             */
            RosterTeamDAO dbRosterTeamDAO = rosterTeamMapper.selectByPrimaryKey(updateRosterTeamRequest.getRosterTeamId());
            if (Objects.isNull(dbRosterTeamDAO)) {
                throw new CommonException("50000", "子集信息不存在");
            }

            /*
             * 判断是否是系统内部字段，系统内部字段不能编辑，isSystem为1 为系统能不字段
             */
            if (dbRosterTeamDAO.getIsSystem().equals(RosterConfigConstant.IsSystem.IS_SYSTEM.getCode())) {
                throw new CommonException("50001", "系统内置数据不能编辑");
            }
            // 编辑名称判断名称是否重复
            if (!StringUtil.isEmpty(updateRosterTeamRequest.getName())) {
                // 判断名称是否重复
                List<RosterTeamDAO> rosterTeamDAOS = rosterTeamMapper.selectRosterTeamList(GetRosterConfigCondition.builder()
                        .appChannelId(dbRosterTeamDAO.getAppChannelId()).dataChannelId(dbRosterTeamDAO.getDataChannelId())
                        .companyId(dbRosterTeamDAO.getCompanyId())
                        .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                        .name(updateRosterTeamRequest.getName()).build(), 1, 0);
                if (rosterTeamDAOS != null && !rosterTeamDAOS.isEmpty()) {
                    // 列表非空最多一条同名数据
                    if (!rosterTeamDAOS.get(0).getId().equals(dbRosterTeamDAO.getId())) {
                        throw new CommonException("50001", "名称重复");
                    }
                }
            }


            /*
             * 创建子集信息并保存信息
             */
            RosterTeamDAO rosterTeamDAO = RosterTeamDAO.builder()
                    .id(updateRosterTeamRequest.getRosterTeamId())
                    .title(updateRosterTeamRequest.getName())
                    .remark(updateRosterTeamRequest.getRemark())
                    .countMax(updateRosterTeamRequest.getGroupCountMax())
                    .isShow(updateRosterTeamRequest.getIsShow())
                    .sort(updateRosterTeamRequest.getSort())
                    .updateTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .build();
            int update = rosterTeamMapper.updateRosterTeam(rosterTeamDAO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delRosterTeamInfo(List<String> rosterTeamIdList) {

        /*
         * 判断是否满足删除条件
         */
        for (String rosterTeamId : rosterTeamIdList) {
            if (StringUtil.isEmpty(rosterTeamId)) {
                throw new CommonException("50000", "子集信息id不能为空");
            }
            RosterTeamDAO dbRosterTeamDAO = rosterTeamMapper.selectByPrimaryKey(rosterTeamId);
            if (Objects.isNull(dbRosterTeamDAO)) {
                throw new CommonException("50000", "子集信息不存在");
            }
            if (dbRosterTeamDAO.getIsSystem().equals(RosterConfigConstant.IsSystem.IS_SYSTEM.getCode())) {
                throw new CommonException("50001", "系统内置数据不能删除");
            }
            int rosterGroupCount = rosterGroupMapper.countRosterGroup(GetRosterConfigCondition.builder()
                    .rosterTeamIds(Collections.singletonList(dbRosterTeamDAO.getId()))
                    .companyId(dbRosterTeamDAO.getCompanyId())
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                    .appChannelId(dbRosterTeamDAO.getAppChannelId())
                    .dataChannelId(dbRosterTeamDAO.getDataChannelId()).build());
            if (rosterGroupCount > 0) {
                throw new CommonException("50001", "子集信息下存在分组信息不能删除");
            }

            RosterTeamDAO rosterTeamDAO = RosterTeamDAO.builder()
                    .id(rosterTeamId)
                    .isDelete(RosterConfigConstant.IsDeleted.DELETED.getCode())
                    .updateTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .build();
            rosterTeamMapper.updateRosterTeam(rosterTeamDAO);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchRosterGroupInfo(SaveBatchRosterGroupRequest request) {

        String appChannelId = request.getAppChannelId() != null ? request.getAppChannelId() : Common.getAppChannelId();
        String dataChannelId = request.getDataChannelId() != null ? request.getDataChannelId() : Common.getDataChannelId();
        if (StringUtil.isEmpty(request.getCompanyId())) {
            throw new CommonException("50000", "公司id不能为空");
        }
        /*
         * 判断公司id子集是否有效
         */
        CompanyInfoDAO company = companyService.getCompany(GetCompanyCondition.builder()
                .companyId(request.getCompanyId()).build());
        if (Objects.isNull(company)) {
            throw new CommonException("50000", "公司不存在");
        }
        List<AddRosterGroupRequest> rosterGroupRequestList = request.getRosterGroupRequestList();
        if (rosterGroupRequestList == null || rosterGroupRequestList.isEmpty()) {
            throw new CommonException("50000", "分组数据列表不能为空");
        }
        for (AddRosterGroupRequest addRosterGroupRequest : rosterGroupRequestList) {
            RosterTeamDAO rosterTeamDAO = rosterTeamMapper.selectByPrimaryKey(addRosterGroupRequest.getRosterTeamId());
            if (Objects.isNull(rosterTeamDAO)) {
                throw new CommonException("50000", "子集信息不存在");
            }
            /*
             * 子集信息下分组信息是否达到上限
             */
            int GroupCountMax = rosterGroupMapper.countRosterGroup(GetRosterConfigCondition.builder()
                    .dataChannelId(dataChannelId)
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                    .appChannelId(appChannelId)
                    .companyId(request.getCompanyId())
                    .rosterTeamIds(Collections.singletonList(rosterTeamDAO.getId())).build());
            if (GroupCountMax >= rosterTeamDAO.getCountMax() && rosterTeamDAO.getCountMax() != 0) { // 0表示不限制
                throw new CommonException("50001", "分组数达到上限");
            }
            // 判断名称是否重复
            int countByName = rosterGroupMapper.countRosterGroup(GetRosterConfigCondition.builder()
                    .appChannelId(appChannelId).dataChannelId(dataChannelId)
                    .name(addRosterGroupRequest.getName())
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                    .companyId(request.getCompanyId())
                    .rosterTeamIds(Collections.singletonList(rosterTeamDAO.getId())).build());
            if (countByName > 0) {
                throw new CommonException("50001", "名称重复");
            }
            // 设置排序字段
            Integer sortMax = addRosterGroupRequest.getSort();
            if (sortMax == null) {
                // 没有传sort 从数据库获取最sort最大数据+1
                sortMax = rosterGroupMapper.getSortMax(GetRosterConfigCondition.builder().appChannelId(appChannelId)
                        .dataChannelId(dataChannelId).companyId(request.getCompanyId()).rosterTeamIds(List.of(addRosterGroupRequest.getRosterTeamId()))
                        .build());
                if (sortMax == null) { // 数据库中没有数据 默认0
                    sortMax = 0;
                } else {
                    sortMax += 1;
                }
            }
            RosterGroupDAO rosterGroupDAO = RosterGroupDAO.builder()
                    .id(StringUtil.createUuid())
                    .field(!StringUtil.isEmpty(addRosterGroupRequest.getField()) ? addRosterGroupRequest.getField() : StringUtil.createRandomString(16))
                    .rosterRuleTeamId(addRosterGroupRequest.getRosterTeamId())
                    .name(addRosterGroupRequest.getName())
                    .isShow(addRosterGroupRequest.getIsShow() == null ? RosterConfigConstant.IsShow.SHOW.getCode() : addRosterGroupRequest.getIsShow())
                    .countMax(addRosterGroupRequest.getFieldCountMax() != null ? addRosterGroupRequest.getFieldCountMax() : RosterConfigConstant.ROSTER_FIELD_MAX_COUNT) // 分组先字段若不传设置默认值
                    .isAdd(addRosterGroupRequest.getIsAdd() != null ? addRosterGroupRequest.getIsAdd() : 0) // 默认为不能复制分组
                    .isSystem(addRosterGroupRequest.getIsSystem() == null ? RosterConfigConstant.IsSystem.NO_SYSTEM.getCode() : addRosterGroupRequest.getIsSystem())
                    .sort(sortMax)
                    .remark(addRosterGroupRequest.getRemark() != null ? addRosterGroupRequest.getRemark() : "")
                    .companyId(request.getCompanyId())
                    .createTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .updateTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .appChannelId(appChannelId)
                    .dataChannelId(dataChannelId)
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode()).build();
            rosterGroupMapper.insertRosterGroup(rosterGroupDAO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchRosterGroupInfo(List<UpdateRosterGroupRequest> updateRosterGroupRequestList) {

        if (updateRosterGroupRequestList == null || updateRosterGroupRequestList.isEmpty()) {
            throw new CommonException("50000", "分组信息列表不能为空");
        }
        for (UpdateRosterGroupRequest request : updateRosterGroupRequestList) {

            if (StringUtil.isEmpty(request.getRosterGroupId())) {
                throw new CommonException("50000", "分组信息id不能为空");
            }

            if (Objects.nonNull(request.getSort()) && request.getSort() < 0) {
                throw new CommonException("50002", "参数错误，不能为负数");
            }

            if (Objects.nonNull(request.getFieldCountMax()) && request.getFieldCountMax() < 0) {
                throw new CommonException("50002", "参数错误，不能为负数");
            }

            if (Objects.nonNull(request.getIsShow()) &&
                    (request.getIsShow() < 0 || request.getIsShow() > 1)) {
                throw new CommonException("50002", "参数错误");
            }
            /*
             * 判断公司id子集是否有效
             */
            RosterGroupDAO dbRosterGroupDAO = rosterGroupMapper.selectByPrimaryKey(request.getRosterGroupId());
            if (Objects.isNull(dbRosterGroupDAO)) {
                throw new CommonException("50000", "分组信息不存在");
            }
            if (dbRosterGroupDAO.getIsSystem().equals(RosterConfigConstant.IsSystem.IS_SYSTEM.getCode())) {
                throw new CommonException("50001", "系统内置数据不能编辑");
            }
            // 编辑名称判断名称是否重复
            if (!StringUtil.isEmpty(request.getName())) {
                // 判断名称是否重复
                List<RosterGroupDAO> rosterGroupDAOS = rosterGroupMapper.selectRosterGroupList(GetRosterConfigCondition.builder()
                        .appChannelId(dbRosterGroupDAO.getAppChannelId()).dataChannelId(dbRosterGroupDAO.getDataChannelId())
                        .name(request.getName())
                        .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                        .companyId(dbRosterGroupDAO.getCompanyId())
                        .rosterTeamIds(Collections.singletonList(dbRosterGroupDAO.getRosterRuleTeamId())).build(), 1, 0);
                if (rosterGroupDAOS != null && !rosterGroupDAOS.isEmpty()) {
                    // 列表最多有一个相同名称
                    if (!rosterGroupDAOS.get(0).getId().equals(dbRosterGroupDAO.getId())) {
                        throw new CommonException("50001", "名称重复");
                    }
                }

            }

            RosterGroupDAO rosterGroupDAO = RosterGroupDAO.builder()
                    .id(dbRosterGroupDAO.getId())
                    .sort(request.getSort())
                    .isShow(request.getIsShow())
                    .name(request.getName())
                    .remark(request.getRemark())
                    .countMax(request.getFieldCountMax())
                    .updateTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .build();
            rosterGroupMapper.updateRosterGroup(rosterGroupDAO);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delRosterGroupInfo(List<String> rosterGroupIdList) {

        /*
         * 判断是否满足删除条件
         */
        for (String rosterGroupId : rosterGroupIdList) {
            if (StringUtil.isEmpty(rosterGroupId)) {
                throw new CommonException("50000", "分组信息id不能为空");
            }
            RosterGroupDAO dbRosterGroupDAO = rosterGroupMapper.selectByPrimaryKey(rosterGroupId);
            if (Objects.isNull(dbRosterGroupDAO)) {
                throw new CommonException("50000", "分组信息不存在");
            }
            if (dbRosterGroupDAO.getIsSystem().equals(RosterConfigConstant.IsSystem.IS_SYSTEM.getCode())) {
                throw new CommonException("50001", "系统内置数据不能删除");
            }
            int fieldCount = rosterFieldMapper.countRosterField(GetRosterConfigCondition.builder()
                    .appChannelId(dbRosterGroupDAO.getAppChannelId())
                    .dataChannelId(dbRosterGroupDAO.getDataChannelId())
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                    .companyId(dbRosterGroupDAO.getCompanyId())
                    .rosterGroupIds(Collections.singletonList(rosterGroupId)).build());
            if (fieldCount > 0) {
                throw new CommonException("50001", "分组下已关联字段不能删除");
            }

            /*
             * 逻辑删除的修改删除表示字段信息
             */
            RosterGroupDAO rosterGroupDAO = RosterGroupDAO.builder()
                    .id(rosterGroupId)
                    .isDelete(RosterConfigConstant.IsDeleted.DELETED.getCode())
                    .updateTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .build();
            rosterGroupMapper.updateRosterGroup(rosterGroupDAO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBatchRosterFieldInfo(SaveBatchRosterFieldRequest request) {

        /*
         * 判断数据是否有效
         */
        String appChannelId = request.getAppChannelId() != null ? request.getAppChannelId() : Common.getAppChannelId();
        String dataChannelId = request.getDataChannelId() != null ? request.getDataChannelId() : Common.getDataChannelId();
        if (StringUtil.isEmpty(request.getCompanyId())) {
            throw new CommonException("50000", "公司id不能为空");
        }
        CompanyInfoDAO company = companyService.getCompany(GetCompanyCondition.builder()
                .companyId(request.getCompanyId()).build());
        if (Objects.isNull(company)) {
            throw new CommonException("50000", "公司不存在");
        }
        List<AddRosterFieldRequest> fieldRequestList = request.getFieldRequestList();
        if (fieldRequestList == null || fieldRequestList.isEmpty()) {
            throw new CommonException("50000", "字段参数列表不能为空");
        }

        for (AddRosterFieldRequest addRosterFieldRequest : fieldRequestList) {
            RosterGroupDAO rosterGroupDAO = rosterGroupMapper.selectByPrimaryKey(addRosterFieldRequest.getRosterGroupId());
            if (Objects.isNull(rosterGroupDAO)) {
                throw new CommonException("50000", "分组信息不存在");
            }
            // 判断分组信息下字段是否达上限
            int fieldCountMax = rosterFieldMapper.countRosterField(GetRosterConfigCondition.builder()
                    .appChannelId(appChannelId).dataChannelId(dataChannelId)
                    .companyId(request.getCompanyId())
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                    .rosterGroupIds(Collections.singletonList(addRosterFieldRequest.getRosterGroupId())).build());
            if (fieldCountMax >= rosterGroupDAO.getCountMax() && rosterGroupDAO.getCountMax() != 0) { // 0 默认不限制
                throw new CommonException("50001", "字段数达到上限");
            }
            // 判断名称是否有重复
            int countByName = rosterFieldMapper.countRosterField(GetRosterConfigCondition.builder()
                    .appChannelId(appChannelId).dataChannelId(dataChannelId)
                    .companyId(request.getCompanyId())
                    .name(addRosterFieldRequest.getName())
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                    .rosterGroupIds(Collections.singletonList(addRosterFieldRequest.getRosterGroupId())).build());
            if (countByName > 0) {
                throw new CommonException("50001", "名称重复");
            }
            // 设置排序字段
            Integer sortMax = addRosterFieldRequest.getSort();
            if (sortMax == null) {
                // 没有传sort 从数据库获取最sort最大数据+1
                sortMax = rosterFieldMapper.getSortMax(GetRosterConfigCondition.builder().appChannelId(appChannelId)
                        .dataChannelId(dataChannelId).companyId(request.getCompanyId())
                        .rosterGroupIds(List.of(addRosterFieldRequest.getRosterGroupId())).build());
                if (sortMax == null) { // 数据库中没有数据 默认0
                    sortMax = 0;
                } else {
                    sortMax += 1;
                }
            }

            /*
             * 创建DAO并保存数据
             */
            RosterFieldDAO rosterFieldDAO = RosterFieldDAO.builder()
                    .id(StringUtil.createUuid())
                    .field(!StringUtil.isEmpty(addRosterFieldRequest.getField()) ? addRosterFieldRequest.getField() : StringUtil.createRandomString(16)) // 生成字段参数id
                    .name(addRosterFieldRequest.getName())
                    .isNonNull(addRosterFieldRequest.getIsNonNull())
                    .isShow(addRosterFieldRequest.getIsShow() != null ? addRosterFieldRequest.getIsShow() : RosterConfigConstant.IsShow.SHOW.getCode())
                    .type(addRosterFieldRequest.getType())
                    .sort(sortMax)
                    .length(addRosterFieldRequest.getLength() != null ? addRosterFieldRequest.getLength() : 0) // 不传length默认为0
                    .rosterRuleTeamId(rosterGroupDAO.getRosterRuleTeamId())
                    .rosterRuleGroupId(rosterGroupDAO.getId())
                    .companyId(request.getCompanyId())
                    .isSystem(addRosterFieldRequest.getIsSystem() != null ? addRosterFieldRequest.getIsSystem() : RosterConfigConstant.IsSystem.NO_SYSTEM.getCode())
                    .appChannelId(appChannelId)
                    .dataChannelId(dataChannelId)
                    .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                    .createTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .updateTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .build();
            rosterFieldMapper.insertRosterField(rosterFieldDAO);

            /*
             * 创建数据字典关联关系
             */
            List<RosterDictRequest> dictList = addRosterFieldRequest.getDictList();
            if (dictList != null && !dictList.isEmpty()) {
                for (RosterDictRequest dictRequest : dictList) {
                    RosterDictDAO rosterDictDAO = RosterDictDAO.builder()
                            .id(StringUtil.createUuid())
                            .name(dictRequest.getName())
                            .value(dictRequest.getValue())
                            .companyId(request.getCompanyId())
                            .appChannelId(appChannelId)
                            .dataChannelId(dataChannelId)
                            .rosterRuleTeamId(rosterFieldDAO.getRosterRuleTeamId())
                            .rosterRuleGroupId(rosterFieldDAO.getRosterRuleGroupId())
                            .rosterRuleFieldId(rosterFieldDAO.getField())
                            .build();
                    rosterDictMapper.insertDic(rosterDictDAO);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRosterFieldInfo(List<UpdateRosterFieldRequest> fieldRequestList) {

        if (fieldRequestList == null || fieldRequestList.isEmpty()) {
            throw new CommonException("50000", "字段参数列表不能为空");
        }

        for (UpdateRosterFieldRequest updateRosterFieldRequest : fieldRequestList) {

            if (StringUtil.isEmpty(updateRosterFieldRequest.getRosterFieldId())) {
                throw new CommonException("50000", "字段信息id不能为空");
            }
            if (Objects.nonNull(updateRosterFieldRequest.getSort()) && updateRosterFieldRequest.getSort() < 0) {
                throw new CommonException("50002", "参数错误，不能为负数");
            }
            if (Objects.nonNull(updateRosterFieldRequest.getIsShow()) &&
                    (updateRosterFieldRequest.getIsShow() < 0 || updateRosterFieldRequest.getIsShow() > 1)) {
                throw new CommonException("50002", "参数错误");
            }
            if (Objects.nonNull(updateRosterFieldRequest.getType()) &&
                    (updateRosterFieldRequest.getType() < 0 || updateRosterFieldRequest.getType() > 8)) {
                throw new CommonException("50002", "参数错误");
            }
            if (Objects.nonNull(updateRosterFieldRequest.getIsNonNull()) &&
                    (updateRosterFieldRequest.getIsNonNull() < 0 || updateRosterFieldRequest.getIsNonNull() > 1)) {
                throw new CommonException("50002", "参数错误");
            }
            if (Objects.nonNull(updateRosterFieldRequest.getLength()) && updateRosterFieldRequest.getLength() < 0 ) {
                throw new CommonException("50002", "参数错误，不能为负数");
            }

            /*
             * 判断数据是否有效
             */
            RosterFieldDAO dbRosterFieldDAO = rosterFieldMapper.selectByPrimaryKey(updateRosterFieldRequest.getRosterFieldId());
            if (Objects.isNull(dbRosterFieldDAO)) {
                throw new CommonException("50000", "字段信息不存在");
            }
            if (dbRosterFieldDAO.getIsSystem().equals(RosterConfigConstant.IsSystem.IS_SYSTEM.getCode())) {
                throw new CommonException("50001", "系统内置数据不能编辑");
            }
            // 修改名称时需要校验名称唯一
            if (!StringUtil.isEmpty(updateRosterFieldRequest.getName())) {
                // 判断名称是否有重复
                List<RosterFieldDAO> rosterFieldDAOS = rosterFieldMapper.selectRosterFieldList(GetRosterConfigCondition.builder()
                        .appChannelId(dbRosterFieldDAO.getAppChannelId()).dataChannelId(dbRosterFieldDAO.getDataChannelId())
                        .companyId(dbRosterFieldDAO.getCompanyId())
                        .name(updateRosterFieldRequest.getName())
                        .isDelete(RosterConfigConstant.IsDeleted.NOT_DELETED.getCode())
                        .rosterGroupIds(Collections.singletonList(dbRosterFieldDAO.getRosterRuleGroupId())).build(), 1, 0);
                if (rosterFieldDAOS != null && !rosterFieldDAOS.isEmpty()) {
                    // 列表非空最多一条数据
                    if (!rosterFieldDAOS.get(0).getId().equals(dbRosterFieldDAO.getId())) {
                        throw new CommonException("50001", "名称重复");
                    }
                }
            }

            /*
             * 创建DAO并更新信息
             */
            RosterFieldDAO rosterFieldDAO = RosterFieldDAO.builder()
                    .id(updateRosterFieldRequest.getRosterFieldId())
                    .name(updateRosterFieldRequest.getName())
                    .sort(updateRosterFieldRequest.getSort())
                    .isNonNull(updateRosterFieldRequest.getIsNonNull())
                    .isShow(updateRosterFieldRequest.getIsShow())
                    .type(updateRosterFieldRequest.getType())
                    .length(updateRosterFieldRequest.getLength())
                    .updateTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .build();
            rosterFieldMapper.updateRosterField(rosterFieldDAO);

            /*
             * 删除关联关系并重新创建关联关系
             */
            List<RosterDictRequest> dictList = updateRosterFieldRequest.getDictList();
            if (dictList != null) {
                rosterDictMapper.deleteByField(dbRosterFieldDAO.getField(), dbRosterFieldDAO.getCompanyId(), dbRosterFieldDAO.getAppChannelId(), dbRosterFieldDAO.getDataChannelId());
                if (!dictList.isEmpty()) {
                    for (RosterDictRequest dictRequest : dictList) {
                        RosterDictDAO rosterDictDAO = RosterDictDAO.builder()
                                .id(StringUtil.createUuid())
                                .name(dictRequest.getName())
                                .value(dictRequest.getValue())
                                .companyId(dbRosterFieldDAO.getCompanyId())
                                .appChannelId(dbRosterFieldDAO.getAppChannelId())
                                .dataChannelId(dbRosterFieldDAO.getDataChannelId())
                                .rosterRuleTeamId(dbRosterFieldDAO.getRosterRuleTeamId())
                                .rosterRuleGroupId(dbRosterFieldDAO.getRosterRuleGroupId())
                                .rosterRuleFieldId(dbRosterFieldDAO.getField())
                                .build();
                        rosterDictMapper.insertDic(rosterDictDAO);
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delRosterFieldInfo(List<String> rosterFieldIdList) {

        for (String rosterFieldId : rosterFieldIdList) {
            RosterFieldDAO dbRosterFieldDAO = rosterFieldMapper.selectByPrimaryKey(rosterFieldId);
            if (Objects.isNull(dbRosterFieldDAO)) {
                throw new CommonException("50000", "该字段信息不存在");
            }
            if (dbRosterFieldDAO.getIsSystem().equals(RosterConfigConstant.IsSystem.IS_SYSTEM.getCode())) {
                throw new CommonException("50001", "系统内置数据不能删除");
            }

            RosterFieldDAO rosterFieldDAO = RosterFieldDAO.builder()
                    .id(rosterFieldId)
                    .isDelete(RosterConfigConstant.IsDeleted.DELETED.getCode())
                    .updateTime(TimeUtil.string2Date(TimeUtil.getNowDateTime()))
                    .build();
            rosterFieldMapper.updateRosterField(rosterFieldDAO);
            rosterDictMapper.deleteByField(dbRosterFieldDAO.getField(), dbRosterFieldDAO.getCompanyId(),
                    dbRosterFieldDAO.getAppChannelId(), dbRosterFieldDAO.getDataChannelId());
        }
    }

    @Override
    public int countRosterTeam(GetRosterConfigCondition condition) {
        return rosterTeamMapper.countRosterTeam(condition);
    }

    @Override
    public int countRosterGroup(GetRosterConfigCondition condition) {
        return rosterGroupMapper.countRosterGroup(condition);
    }

    @Override
    public int countRosterField(GetRosterConfigCondition condition) {
        return rosterFieldMapper.countRosterField(condition);
    }
}
