package com.microservice.user.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.microservice.user.common.Common;
import com.microservice.user.constant.TokenConstant;
import com.microservice.user.entity.condition.GetIdentityCompanyCondition;
import com.microservice.user.entity.condition.GetRosterCondition;
import com.microservice.user.entity.condition.GetTokenCondition;
import com.microservice.user.entity.condition.GetUserCondition;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.DataListDTO;
import com.microservice.user.entity.dto.ms.IdentityDTO;
import com.microservice.user.entity.dto.ms.UserDTO;
import com.microservice.user.entity.dto.ms.roster.IdentityRosterAddDTO;
import com.microservice.user.entity.dto.ms.roster.RosterAddListDTO;
import com.microservice.user.entity.dto.ms.roster.RosterQueeryDTO;
import com.microservice.user.entity.dto.ms.roster.RosterUpdateDTO;
import com.microservice.user.entity.request.ms.item.RelationItem;
import com.microservice.user.entity.vo.RosterVO;
import com.microservice.user.exception.CommonException;
import com.microservice.user.mapper.IdentityRosterMapper;
import com.microservice.user.service.*;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * &#064;DATE: 2024/9/5
 * &#064;AUTHOR: XSL
 *
 */
@Service
public class RosterServiceImpl implements RosterService {

    @Autowired
    private IdentityRosterMapper identityRosterMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private IdentityCompanyService identityCompanyService;

    /**
     * 分页查询集合
     * @return
     */
    @Override
    public DataListDTO<RosterVO> findAllByRosterDAO(RosterQueeryDTO rosterQueeryDTO) {
        GetRosterCondition build = new GetRosterCondition();
        BeanUtils.copyProperties(rosterQueeryDTO, build);
//        if (StrUtil.isEmpty(build.getAppChannelId())) {
//            build.setAppChannelId(Common.getAppChannelId());
//        }
        if (StrUtil.isEmpty(build.getDataChannelId())) {
            build.setDataChannelId(Common.getDataChannelId());
        }
        List<String> identityIdList = build.getIdentityIdList();
        if (Objects.isNull(identityIdList) || identityIdList.isEmpty()) {
            build.setIdentityIdList(null);
        }

        /*
         * 查询数量
         */
        long count = identityRosterMapper.findAllByRosterDAOCount(build);

        /*
         * 查询数据
         */
        Integer page = rosterQueeryDTO.getPage();
        Integer limit = rosterQueeryDTO.getLimit();
        if (Objects.nonNull(page) && Objects.nonNull(limit) && limit > 0) {
            PageHelper.startPage(page, limit);    //分页
        }
        List<IdentityRosterDAO> identityRosterDAOList = identityRosterMapper.findAllByRosterDAO(build);

        return DataListDTO.create(identityRosterDAOList.stream().map(RosterVO::create).collect(Collectors.toList()), count);
    }

    /**
     * 查询数据详情
     * @param identityId
     * @return
     */
    @Override
    public RosterVO findByIdentityId(String identityId) {
        IdentityRosterDAO identityRosterDAO = identityRosterMapper.findByIdentityId(identityId);

        return RosterVO.create(identityRosterDAO);
    }

    /**
     * 新增一条数据
     * @param rosterAddDTOList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRosterDAO(RosterAddListDTO rosterAddDTOList) {
        List<IdentityRosterAddDTO> identityRosterList = rosterAddDTOList.getIdentityRosterList();
        if (Objects.isNull(identityRosterList) || identityRosterList.isEmpty()) {
            return;
        }

        String appChannelId = rosterAddDTOList.getAppChannelId();
        String dataChannelId = rosterAddDTOList.getDataChannelId();
        String companyId = rosterAddDTOList.getCompanyId();
        LocalDateTime now = LocalDateTime.now();
        String nowSTR = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        for (IdentityRosterAddDTO item : identityRosterList) {
            String userName = item.getUserName();   //手机号 (账号)
            String name = item.getIdentityName();
            Map<String, Object> userMap = new HashMap<>();
            userMap.put("realName", name);
            userMap.put("stageName", name);
            userMap.put("phone", userName);
            userMap.put("createTime", nowSTR);
            userMap.put("updateTime", nowSTR);

            String userId;
            /*
             * 检查是否拥有账户
             */
            UserInfoDAO userInfoDAO = userService.getUser(GetUserCondition.builder().userName(userName).dataChannelId(dataChannelId).build());   //获取用户数据
            if (Objects.isNull(userInfoDAO)) {   //没有当前用户,初始化用户
                UserDTO userDTO = userService.initUser(userName, "123456", userMap, appChannelId, dataChannelId); //初始化用户及默认身份
                userId = userDTO.getUserId();
            } else {
                userId = userInfoDAO.getUserId();
            }

            /*
             * 添加身份
             */
            Map<String, Object> identityMap = new HashMap<>();
            identityMap.put("realName", name);
            identityMap.put("stageName", name);
            identityMap.put("phone", userName);
            identityMap.put("createTime", nowSTR);  //创建时间
            identityMap.put("updateTime", nowSTR);  //更新时间
            if (Objects.nonNull(item.getJoinTime())) {
                identityMap.put("joinTime", item.getJoinTime());    //加入时间
            }
            if (Objects.nonNull(item.getEmployeeStatus())) {
                identityMap.put("employeeStatus", item.getEmployeeStatus());    //员工状态  1在职、2离职
            }
            if (Objects.nonNull(item.getEmployeeType())) {
                identityMap.put("employeeType", item.getEmployeeType());    //员工类型： 1全职、2实习、3外派
            }
            if (Objects.nonNull(item.getSeniority())) {
                identityMap.put("seniority", item.getSeniority());    //历史工龄
            }
            if (Objects.nonNull(item.getJobNumber())) {
                identityMap.put("jobNumber", item.getJobNumber());    //工号
            }
            if (Objects.nonNull(item.getGender())) {
                identityMap.put("gender", item.getGender());
            }
            if (Objects.nonNull(item.getPost())) {
                identityMap.put("position", item.getPost());
            }

            IdentityInfoDAO identityInfoDAO = identityService.addIdentity(userId, StringUtil.createUuid(), name, identityMap, appChannelId, dataChannelId);
            String identityId = identityInfoDAO.getIdentityId();    //身份ID

            List<RelationItem> relationItemList = new ArrayList<>();
            /*
             * 生成公司关系
             */
            RelationItem companyRelation = new RelationItem();
            companyRelation.setRelateType("company");
            companyRelation.setRelateIdList(Collections.singletonList(companyId));
            relationItemList.add(companyRelation);

            /*
             * 生成部门关系
             */
            List<String> deptIdList = item.getDeptIdList();
            if (Objects.nonNull(deptIdList) && !deptIdList.isEmpty()) {
                RelationItem deptRelationItem = new RelationItem();
                deptRelationItem.setRelateType("department");
                deptRelationItem.setRelateIdList(deptIdList);
                relationItemList.add(deptRelationItem);
            }

            identityService.addIdentityRelationList(identityId, relationItemList, appChannelId);   //持久化关系

            /*
             * 持久化花名册数据
             */
            identityRosterMapper.addRosterDAO(
                    RosterDAO.builder()
                            .identityId(identityId)
                            .userName(userName)
                            .post(item.getPost())
                            .jobNumber(item.getJobNumber())
                            .idNumber(item.getIdNumber())
                            .idNumberType(item.getIdNumberType())
                            .idPhotoFront(item.getIdPhotoFront())
                            .idPhotoSide(item.getIdPhotoSide())
                            .birthDate(item.getBirthDate())
                            .birthType(item.getBirthType())
                            .extend(JSONUtil.toJsonStr(item.getExtend()))
                            .companyId(companyId)
                            .createTime(DateUtil.now())
                            .updateTime(DateUtil.now())
                            .appChannelId(appChannelId)
                            .dataChannelId(dataChannelId)
                            .build()
            );
        }
    }

    /**
     * 更新数据
     * @param rosterList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRosterDAO(List<RosterUpdateDTO> rosterList) {
        LocalDateTime now = LocalDateTime.now();
        String nowSTR = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        for (RosterUpdateDTO item : rosterList) {
            String identityId = item.getIdentityId();
            String name = item.getIdentityName();
            if (StrUtil.isEmpty(identityId)) {
                throw new CommonException("60000", name + " 的身份ID不能为空");
            }
            IdentityInfoDAO identity = identityService.getIdentity(identityId); //获取之前的身份数据

            if (Objects.isNull(identity)) {
                throw new CommonException("60000", name + "的身份ID不存在");
            }

            String appChannelId = StrUtil.isEmpty(item.getAppChannelId()) ? Common.getAppChannelId() : item.getAppChannelId();
            String dataChannelId = StrUtil.isEmpty(item.getDataChannelId()) ? Common.getDataChannelId() : item.getDataChannelId();

            /*
             * 变动部门关系
             */
            List<String> deptIdList = item.getDeptIdList();
            if (Objects.nonNull(deptIdList) && !deptIdList.isEmpty()) {
                List<RelationItem> relationItemList = new ArrayList<>();
                RelationItem deptRelationItem = new RelationItem();
                deptRelationItem.setRelateType("department");
                deptRelationItem.setRelateIdList(deptIdList);
                relationItemList.add(deptRelationItem);
                identityService.updateIdentityRelationList(identityId, relationItemList, appChannelId);
            }

            String extraContent = identity.getExtraContent();   //获取扩展字段

            JSONObject entries = JSONUtil.parseObj(extraContent);

            /*
             * 变动身份
             */
            entries.set("realName", name);
            entries.set("stageName", name);
            entries.set("updateTime", nowSTR);  //更新时间
            if (Objects.nonNull(item.getJoinTime())) {
                entries.set("joinTime", item.getJoinTime());    //加入时间
            } else {
                entries.set("joinTime", "");    //加入时间
            }
            if (Objects.nonNull(item.getEmployeeStatus())) {
                entries.set("employeeStatus", item.getEmployeeStatus());    //员工状态:1在职、2离职
            }
            if (Objects.nonNull(item.getEmployeeType())) {
                entries.set("employeeType", item.getEmployeeType());    //员工类型： 1全职、2实习、3外派
            }
            if (Objects.nonNull(item.getSeniority())) {
                entries.set("seniority", item.getSeniority());    //历史工龄
            }
            if (Objects.nonNull(item.getJobNumber())) { //工号
                entries.set("jobNumber", item.getJobNumber());
            }
            if (Objects.nonNull(item.getGender())) { //性别
                entries.set("gender", item.getGender());
            }
            if (Objects.nonNull(item.getPost())) {  //职位
                entries.set("position", item.getPost());
            }

            identityService.updateIdentity(
                    IdentityInfoDAO.builder()
                            .identityId(identityId)
                            .identityName(name)
                            .extraContent(JSONUtil.toJsonStr(entries))
                            .build()
            );

            /*
             * 获取花名册信息,判断时候存在,如果有就修改,没有就新增
             */
            RosterDAO rosterByIdentityId = identityRosterMapper.getRosterByIdentityId(identityId);
            if (Objects.isNull(rosterByIdentityId)) {
                GetIdentityCompanyCondition getIdentityCompanyCondition = new GetIdentityCompanyCondition();
                getIdentityCompanyCondition.setIdentityId(identityId);
                List<ICRWithIdentityDAO> icrWithIdentityList = identityCompanyService.getICRWithIdentityList(getIdentityCompanyCondition);
                if (Objects.isNull(icrWithIdentityList) || icrWithIdentityList.isEmpty()) {
                    throw new CommonException("60000", "未查询到身份与公司关联");
                }
                ICRWithIdentityDAO icrWithIdentityDAO = icrWithIdentityList.get(0);
                /*
                 * 持久化花名册数据
                 */
                identityRosterMapper.addRosterDAO(
                        RosterDAO.builder()
                                .identityId(identityId)
                                .userName(item.getUserName())
                                .post(item.getPost())
                                .jobNumber(item.getJobNumber())
                                .idNumber(item.getIdNumber())
                                .idNumberType(item.getIdNumberType())
                                .idPhotoFront(item.getIdPhotoFront())
                                .idPhotoSide(item.getIdPhotoSide())
                                .birthDate(item.getBirthDate())
                                .birthType(item.getBirthType())
                                .extend(JSONUtil.toJsonStr(item.getExtend()))
                                .companyId(icrWithIdentityDAO.getCompanyId())
                                .createTime(DateUtil.now())
                                .updateTime(DateUtil.now())
                                .appChannelId(appChannelId)
                                .dataChannelId(dataChannelId)
                                .build()
                );
            } else {
                /*
                 * 更新花名册
                 */
                identityRosterMapper.updateRosterDAO(
                        RosterDAO.builder()
                                .identityId(identityId)
                                .post(item.getPost())
                                .jobNumber(item.getJobNumber())
                                .idNumber(item.getIdNumber())
                                .idNumberType(item.getIdNumberType())
                                .idPhotoFront(item.getIdPhotoFront())
                                .idPhotoSide(item.getIdPhotoSide())
                                .birthDate(item.getBirthDate())
                                .birthType(item.getBirthType())
                                .extend(JSONUtil.toJsonStr(item.getExtend()))
                                .updateTime(DateUtil.formatLocalDateTime(now))
                                .build()
                );
            }
            //清除身份缓存
            identityService.clearIdentityCache(item.getIdentityId());
        }
    }

    /**
     * 删除数据
     * @param identityId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRosterDAO(String identityId) {
        IdentityDTO identityDTO = identityService.createIdentityEntity(Common.getAppChannelId(), identityId);

//        Map<String, Object> extraContent = identityDTO.getExtraContent();
//        extraContent.put("employeeStatus", 2);
        //更新身份为离职状态
//        identityService.updateIdentity(IdentityInfoDAO.builder().extraContent(JSONUtil.toJsonStr(extraContent)).identityId(identityId).build());
        //删除身份
        identityService.deleteIdentity(identityId);
        //退出登录
        tokenService.logout(GetTokenCondition.builder().identityId(identityId).build(), TokenConstant.tokenStatus.CHANGE_EVENT.getCode().toString());
        //清除用户缓存
        userService.clearUserCache(identityDTO.getUserId());
        //清除身份缓存
        identityService.clearIdentityCache(identityId);
        return identityRosterMapper.deleteRosterDAO(identityId);
    }


}
