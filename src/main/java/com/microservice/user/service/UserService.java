package com.microservice.user.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microservice.user.constant.IdentityConstant;
import com.microservice.user.constant.SystemConstant;
import com.microservice.user.constant.UserConstant;
import com.microservice.user.entity.condition.*;
import com.microservice.user.entity.dao.*;
import com.microservice.user.entity.dto.ms.*;
import com.microservice.user.entity.request.ms.user.GetSimpleCompanyUserListRequest;
import com.microservice.user.entity.request.ms.user.UserKeyFilter;
import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.exception.UserException;
import com.microservice.user.mapper.*;
import com.microservice.user.util.RedisUtil;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class UserService {

    @Autowired
    private UserInfoMapper userInfoMapper;
    @Autowired
    private TokenInfoMapper tokenInfoMapper;
    @Autowired
    private IdentityInfoMapper identityInfoMapper;
    @Autowired
    private IdentityDepartmentRelationMapper idrMapper;
    @Autowired
    private IdentityPositionRelationMapper iprMapper;
    @Autowired
    private IdentityGroupRelationMapper igrMapper;
    @Autowired
    private GroupEventRelationMapper gerMapper;
    @Autowired
    private GroupPageRelationMapper gprMapper;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private SystemService systemService;
    @Autowired
    private UserInfoPreMapper userInfoPreMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private CompanyDepartmentRelationMapper companyDepartmentRelationMapper;


    public UserInfoDAO getUser(GetUserCondition condition) {
        return userInfoMapper.getUser(condition);
    }

    public UserInfoDAO getUserByIdentityId(String identityId) {
        return userInfoMapper.getUserByIdentityId(identityId);
    }

    public List<UserInfoDAO> getUserList(GetUserCondition condition) {
        return getUserList(condition, 0, 0);
    }

    public List<UserInfoDAO> getUserList(GetUserCondition condition, int start, int limit) {
        if ((condition.getUserIdList() != null && condition.getUserIdList().isEmpty())
                || (condition.getUserNameList() != null && condition.getUserNameList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getCompanyUserIdList() != null && condition.getCompanyUserIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return userInfoMapper.getUserList(condition, start, limit);
    }

    public List<UserDTO> createUserSimpleEntity(List<String> userIdList) {
        return createUserSimpleEntity(userIdList, "");
    }

    public List<UserDTO> createUserSimpleEntity(List<String> userIdList, String companyId) {
        List<UserDTO> userEntityList = new LinkedList<>();
        if (userIdList.isEmpty()) return userEntityList;
        //用户列表
        GetUserCondition condition = GetUserCondition.builder().userIdList(userIdList).build();
        if(!StringUtil.isEmpty(companyId)) {
            condition.setCompanyId(companyId);
        }
        List<UserInfoDAO> userInfoDAOList = getUserList(condition);
        return userInfoDAOList.stream().map(UserDTO::create).collect(Collectors.toList());
    }

    public UserDTO createUserEntity(String appChannelId, String userId) {
        List<UserDTO> userDTOList = createUserEntity(
                appChannelId,
                new ArrayList<String>() {{
                    add(userId);
                }}
        );
        return !userDTOList.isEmpty() ? userDTOList.get(0) : new UserDTO();
    }

    public List<UserDTO> createSimpleUserEntity(List<UserDTO> userEntityList) {
        if (CollectionUtils.isEmpty(userEntityList)) {
            return userEntityList;
        }

        List<IdentityInfoWithCompanyInfoDAO> identityInfoWithCompanyInfoDAOList = identityInfoMapper
                .getIdentityWithCompanyList(GetIdentityCondition.builder()
                .userIdList(userEntityList.stream().map(UserDTO::getUserId).distinct().collect(Collectors.toList()))
                .build());
        Map<String, TokenInfoDAO> userId2TokenMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(identityInfoWithCompanyInfoDAOList)) {
            List<String> identityIdList = identityInfoWithCompanyInfoDAOList.stream().map(IdentityInfoWithCompanyInfoDAO::getIdentityId).collect(Collectors.toList());
            List<TokenInfoDAO> tokenInfoDAOList = tokenInfoMapper.getLastUserTokenListGroupByIdentityId(GetTokenCondition.builder()
                    .identityIdList(identityIdList)
                    .build());
            if (!CollectionUtils.isEmpty(tokenInfoDAOList)) {
                userId2TokenMap = tokenInfoDAOList.stream().collect(Collectors.toMap(TokenInfoDAO::getUserId, dao -> dao, (t1, t2) -> t1));
            }
        }

        Map<String, List<IdentityInfoWithCompanyInfoDAO>> userId2IdentityListMap = identityInfoWithCompanyInfoDAOList
                .stream().collect(Collectors.groupingBy(IdentityInfoWithCompanyInfoDAO::getUserId));

        for (UserDTO userDTO : userEntityList) {
            TokenInfoDAO tokenInfoDAO = userId2TokenMap.get(userDTO.getUserId());
            if (tokenInfoDAO != null) {
                userDTO.setToken(StringUtil.isEmpty(tokenInfoDAO.getTokenStatus()) ? tokenInfoDAO.getToken() : "");
                userDTO.setLastLoginIp(tokenInfoDAO.getIp());
                userDTO.setLastLoginTime(TimeUtil.date2String(tokenInfoDAO.getCreateTime()));
            }
            boolean isSetSelected = false;
            List<IdentityInfoWithCompanyInfoDAO> companyInfoList = userId2IdentityListMap
                    .getOrDefault(userDTO.getUserId(), new ArrayList<>());
            if (!CollectionUtils.isEmpty(companyInfoList)) {
                List<IdentityDTO> identityDTOList = new LinkedList<>();
                for (IdentityInfoWithCompanyInfoDAO identityInfoWithCompanyInfoDAO : companyInfoList) {
                    IdentityDTO identityDTO = IdentityDTO.create(identityInfoWithCompanyInfoDAO);
                    if (tokenInfoDAO != null && tokenInfoDAO.getIdentityId().equals(identityInfoWithCompanyInfoDAO.getIdentityId())) {
                        userDTO.setIdentityId(identityDTO.getIdentityId());
                        isSetSelected = true;
                        identityDTO.setIsSelected(1);
                    }
                    identityDTOList.add(identityDTO);
                }
                userDTO.setIdentityList(identityDTOList);
            }

            //如果用户没有登录过，把最近的身份置为选中状态
            if (!CollectionUtils.isEmpty(userDTO.getIdentityList()) && !isSetSelected) {
                IdentityDTO lastIdentity = userDTO.getIdentityList().get(0);
                lastIdentity.setIsSelected(1);
                userDTO.getIdentityList().set(0, lastIdentity);
                userDTO.setIdentityId(lastIdentity.getIdentityId());
            }
        }
        return userEntityList;
    }


    public List<UserDTO> createUserEntity(String appChannelId, List<String> userIdList) {
        List<UserDTO> userEntityList = new LinkedList<>();
        if (userIdList.isEmpty()) return userEntityList;
        //用户列表
        List<UserInfoDAO> userInfoDAOList = getUserList(GetUserCondition.builder().userIdList(userIdList).build());
        //token
//        Map<String, TokenInfoDAO> userId2TokenMap = tokenInfoMapper.getLastUserTokenListGroupByUserId(GetTokenCondition.builder()
//                .userIdList(userIdList)
//                .build()).stream().collect(Collectors.toMap(TokenInfoDAO::getUserId, dao -> dao, (t1, t2) -> t2));
        //身份 todo slow
        List<IdentityInfoWithCompanyInfoDAO> identityInfoWithCompanyInfoDAOList = identityInfoMapper.getIdentityWithCompanyList(GetIdentityCondition.builder()
                .userIdList(userIdList)
                .build());
        Map<String, List<IdentityInfoWithCompanyInfoDAO>> userId2IdentityListMap = identityInfoWithCompanyInfoDAOList.stream().collect(Collectors.groupingBy(IdentityInfoWithCompanyInfoDAO::getUserId));
        //身份id列表
        List<String> identityIdList = identityInfoWithCompanyInfoDAOList.stream().map(IdentityInfoWithCompanyInfoDAO::getIdentityId).collect(Collectors.toList());
        //token
        List<TokenInfoDAO> tokenInfoDAOList = tokenInfoMapper.getLastUserTokenListGroupByIdentityId(GetTokenCondition.builder()
                .identityIdList(identityIdList)
                .build());
//        List<TokenInfoDAO> tokenInfoDAOList = tokenInfoMapper.getLastUserTokenListGroupByUserIdAndAppChannelId(GetTokenCondition.builder()
//                .userIdList(userIdList)
//                .appChannelId(appChannelId)
//                .build());
        Map<String, TokenInfoDAO> userId2TokenMap = tokenInfoDAOList.stream().collect(Collectors.toMap(TokenInfoDAO::getUserId, dao -> dao, (t1, t2) -> t1));
        //部门
        List<IDRWithDepartmentDAO> idrWithDepartmentDAOList = idrMapper.getIDRWithDepartmentList(GetDepartmentCondition.builder()
                .identityIdList(identityIdList)
                .build());
        Map<String, List<IDRWithDepartmentDAO>> identityId2IDRMap = idrWithDepartmentDAOList.stream().collect(Collectors.groupingBy(IDRWithDepartmentDAO::getIdentityId));
        //职位
        List<IPRWithPositionDAO> iprWithPositionDAOList = iprMapper.getIPRWithPositionList(GetPositionCondition.builder()
                .identityIdList(identityIdList)
                .build());
        Map<String, List<IPRWithPositionDAO>> identityId2IPRMap = iprWithPositionDAOList.stream().collect(Collectors.groupingBy(IPRWithPositionDAO::getIdentityId));
        //用户组
        List<IGRWithGroupDAO> igrWithGroupDAOList = igrMapper.getIGRWithGroupList(GetGroupCondition.builder()
                .identityIdList(identityIdList)
//                .appChannelId(appChannelId)
                .build());
        Map<String, List<IGRWithGroupDAO>> identityId2IGRMap = igrWithGroupDAOList.stream().collect(Collectors.groupingBy(IGRWithGroupDAO::getIdentityId));
        //权限 todo slow 116868
        List<GroupEventRelationDAO> gerList = gerMapper.getGERList(GetGroupEventCondition.builder()
                .identityIdList(identityIdList)
                .build());
        Map<String, List<GroupEventRelationDAO>> identityId2GERMap = gerList.stream().collect(Collectors.groupingBy(GroupEventRelationDAO::getIdentityId));
        //页面
        List<GroupPageRelationDAO> gprList = gprMapper.getGPRList(GetGroupPageCondition.builder()
                .identityIdList(identityIdList)
                .build());
        Map<String, List<GroupPageRelationDAO>> identityId2GPRMap = gprList.stream().collect(Collectors.groupingBy(GroupPageRelationDAO::getIdentityId));
        //开始创建用户实体
        for (UserInfoDAO userInfoDAO : userInfoDAOList) {
            UserDTO userDTO = UserDTO.create(userInfoDAO);
            TokenInfoDAO tokenInfoDAO = userId2TokenMap.get(userInfoDAO.getUserId());
            if (tokenInfoDAO != null) {
                userDTO.setToken(StringUtil.isEmpty(tokenInfoDAO.getTokenStatus()) ? tokenInfoDAO.getToken() : "");
                userDTO.setLastLoginIp(tokenInfoDAO.getIp());
                userDTO.setLastLoginTime(TimeUtil.date2String(tokenInfoDAO.getCreateTime()));
            }
            //身份列表
            List<IdentityDTO> identityDTOList = new LinkedList<>();
            boolean isSetSelected = false;
            List<IdentityInfoWithCompanyInfoDAO> iterIdentityInfoWithCompanyInfoDAOList = userId2IdentityListMap.getOrDefault(userInfoDAO.getUserId(), new ArrayList<>());
            for (IdentityInfoWithCompanyInfoDAO identityInfoWithCompanyInfoDAO : iterIdentityInfoWithCompanyInfoDAOList) {
                //部门
                List<IDRWithDepartmentDAO> idrDAOList = identityId2IDRMap.getOrDefault(identityInfoWithCompanyInfoDAO.getIdentityId(), new ArrayList<>());
                //职位
                List<IPRWithPositionDAO> iprDAOList = identityId2IPRMap.getOrDefault(identityInfoWithCompanyInfoDAO.getIdentityId(), new ArrayList<>());
                //用户组
                List<IGRWithGroupDAO> igrDAOList = identityId2IGRMap.getOrDefault(identityInfoWithCompanyInfoDAO.getIdentityId(), new ArrayList<>());
                //权限
                List<GroupEventRelationDAO> gerDAOList = identityId2GERMap.getOrDefault(identityInfoWithCompanyInfoDAO.getIdentityId(), new ArrayList<>());
                //页面
                List<GroupPageRelationDAO> gprDAOList = identityId2GPRMap.getOrDefault(identityInfoWithCompanyInfoDAO.getIdentityId(), new ArrayList<>());
                //创建身份实体
                IdentityDTO identityDTO = IdentityDTO.create(identityInfoWithCompanyInfoDAO);
                identityDTO.setDepartmentList(idrDAOList.stream().map(DepartmentDTO::create).collect(Collectors.toList()));
                identityDTO.setDepartmentIdList(idrDAOList.stream().map(IDRWithDepartmentDAO::getDepartmentId).filter(dao -> !dao.isEmpty()).collect(Collectors.toList()));
                identityDTO.setPositionList(iprDAOList.stream().map(PositionDTO::create).collect(Collectors.toList()));
                identityDTO.setPositionIdList(iprDAOList.stream().map(IPRWithPositionDAO::getPositionId).filter(dao -> !dao.isEmpty()).collect(Collectors.toList()));
                List<String> groupIdList = igrDAOList.stream().map(IGRWithGroupDAO::getGroupId).filter(dao -> !dao.isEmpty()).collect(Collectors.toList());
                List<String> eventIdList = gerDAOList.stream().map(GroupEventRelationDAO::getEventId).filter(dao -> !dao.isEmpty()).collect(Collectors.toList());
                List<String> pageIdList = gprDAOList.stream().map(GroupPageRelationDAO::getPageId).filter(dao -> !dao.isEmpty()).collect(Collectors.toList());
                identityDTO.setGroupList(igrDAOList.stream().map(GroupDTO::create).collect(Collectors.toList()));
                identityDTO.setEventIdList(eventIdList);
                identityDTO.setPageIdList(pageIdList);
                identityDTO.setGroupIdList(groupIdList);
                //身份和用户组关系
                identityDTO.setIgrList(igrDAOList.stream().map(IdentityGroupRelationDTO::new).collect(Collectors.toList()));
//                token
//                identityDTO.setToken(TokenDTO.create(identityId2TokenMap.get(identityDTO.getIdentityId())));
                if (tokenInfoDAO != null && tokenInfoDAO.getIdentityId().equals(identityInfoWithCompanyInfoDAO.getIdentityId())) {
                    userDTO.setIdentityId(identityDTO.getIdentityId());
                    isSetSelected = true;
                    identityDTO.setIsSelected(1);
                }
                identityDTOList.add(identityDTO);
            }
            //如果用户没有登录过，把最近的身份置为选中状态
            if (identityDTOList.size() > 0 && !isSetSelected) {
                IdentityDTO lastIdentity = identityDTOList.get(0);
                lastIdentity.setIsSelected(1);
                identityDTOList.set(0, lastIdentity);
                userDTO.setIdentityId(lastIdentity.getIdentityId());
            }
            userDTO.setIdentityList(identityDTOList);
            userEntityList.add(userDTO);
        }
        return userEntityList;
    }

    public UserDTO initUser(String userName, String password, Map<String, Object> extraContent, String appChannelId, String dataChannelId) {
        //添加用户
        UserInfoDAO userInfoDAO = UserInfoDAO.builder()
                .userId(StringUtil.createUuid())
                .userName(userName)
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .build();
        if (extraContent == null) {
            extraContent = new HashMap<String, Object>() {{
                put("nickname", UserConstant.USER_NICKNAME_PREFIX + StringUtil.createRandomString(8));
            }};
        }
        if (StringUtil.isEmpty(password)) {
            password = StringUtil.md5(StringUtil.createRandomString(8));
        } else if (password.length() != 32) {
            password = StringUtil.md5(password);
        }
        userInfoDAO.setPassword(StringUtil.myMd5(password));
        userInfoDAO.setExtraContent(StringUtil.jsonEncode(extraContent));
        if (userInfoMapper.addUser(userInfoDAO) <= 0) {
            throw new UserException("60001", "添加用户失败");
        }
        //添加默认身份
        IdentityInfoDAO identityInfoDAO = identityService.addIdentity(
                userInfoDAO.getUserId(),
                userInfoDAO.getUserId(),//默认身份的identityId和userId一致
                IdentityConstant.IDENTITY_DEFAULT_NAME,
                extraContent,
                appChannelId,
                dataChannelId
        );
        UserDTO userDTO = UserDTO.create(userInfoDAO);
        userDTO.setIdentityList(new ArrayList<IdentityDTO>() {{
            IdentityDTO identityDTO = IdentityDTO.create(identityInfoDAO);
            identityDTO.setIsSelected(1);
            add(identityDTO);
        }});
        return userDTO;
    }

    public void updateUser(UserInfoDAO userInfoDAO) {
        userInfoDAO.setUpdateTime(new Date());
        if (userInfoMapper.updateUser(
                userInfoDAO,
                GetUserCondition.builder()
                        .userId(userInfoDAO.getUserId())
                        .build()
        ) <= 0) {
            throw new UserException("60002", "更新用户失败");
        }
    }

    public void deleteUser(String userId) {
        //检查是否还有身份
        List<IdentityDTO> identityDTOList = identityService.getIdentityList(GetIdentityCondition.builder()
                .userId(userId)
                .build());
        if (!identityDTOList.isEmpty()) {
            throw new UserException("60003", "此用户已关联身份，不能删除");
        }
        if (userInfoMapper.deleteUser(GetUserCondition.builder().userId(userId).build()) <= 0) {
            throw new UserException("60004", "删除用户失败");
        }
    }

    public Integer getUserCount(GetUserCondition condition) {
        if ((condition.getUserIdList() != null && condition.getUserIdList().isEmpty())
                || (condition.getUserNameList() != null && condition.getUserNameList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return userInfoMapper.getUserCount(condition);
    }

    public List<IGERWithIdentityDAO> getEventUserList(String eventId, String companyId) {
        return userInfoMapper.getIGERWithIdentityList(eventId, companyId);
    }

    public List<UserDTO> getUserEntityFromCache(String appChannelId, List<String> userIdList) {
        //先从缓存获取
        List<UserDTO> cacheUserDTOList = new LinkedList<>();
        List<String> redisUserIdList = new LinkedList<>();//redis中存在的userId
        List<String> dbUserIdList = new LinkedList<>();//要从数据库查询的userId
        //批量获取多个key
        List<String> redisKeyList = userIdList.stream().map(userId -> systemService.getUserEntityRedisKey(appChannelId, userId)).collect(Collectors.toList());
        List<String> redisValueList = RedisUtil.mGet(redisKeyList);
        for (String redisValue : redisValueList) {
            if (redisValue == null) continue;
            UserDTO userDTO = StringUtil.jsonDecode(redisValue, UserDTO.class);
            if (userDTO != null) {
                redisUserIdList.add(userDTO.getIdentityId());
                cacheUserDTOList.add(userDTO);
            }
        }
        //过滤出要从数据库查的userId
        for (String userId : userIdList) {
            if (!redisUserIdList.contains(userId)) {
                dbUserIdList.add(userId);
            }
        }
        List<UserDTO> dbUserDTOList = createUserEntity(appChannelId, dbUserIdList);
        setUserEntityToCache(appChannelId, dbUserDTOList);
        Map<String, UserDTO> userMap = cacheUserDTOList.stream().collect(Collectors.toMap(UserDTO::getUserId, userDTO -> userDTO, (user1, user2) -> user2));
        userMap.putAll(dbUserDTOList.stream().collect(Collectors.toMap(UserDTO::getUserId, userDTO -> userDTO, (user1, user2) -> user2)));
        return userIdList.stream().map(userMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public void setUserEntityToCache(String appChannelId, List<UserDTO> userDTOList) {
        for (UserDTO userDTO : userDTOList) {
            RedisUtil.set(
                    systemService.getUserEntityRedisKey(appChannelId, userDTO.getUserId()),
                    StringUtil.jsonEncode(userDTO),
                    SystemConstant.REDIS_USER_ENTITY_EXPIRE
            );
        }
    }

    public UserInfoPreDAO addUserPre(String userName, Map<String, Object> extraContent, String appChannelId, String dataChannelId) {
        if (StringUtil.isEmpty(userName)) {
            throw new UserException("60005", "用户名错误");
        }
        //检测user_info表是否以存在当前用户
//        UserInfoDAO userInfoDAO = getUser(GetUserCondition.builder()
//                .userName(userName)
//                .dataChannelId(dataChannelId)
//                .build());
//        if (userInfoDAO != null) {
//            throw new UserException("60012", "用户已存在");
//        }
//        UserInfoPreDAO userInfoPreDAO = userInfoPreMapper.getUserPre(GetUserPreCondition.builder()
//                .userName(userName)
//                .appChannelId(appChannelId)
//                .dataChannelId(dataChannelId)
//                .build());
//        if (userInfoPreDAO != null) {
//            throw new UserException("60006", "预注册用户已存在");
//        }
        UserInfoPreDAO userInfoPreDAO = UserInfoPreDAO.builder()
                .preUserId(StringUtil.createUuid())
                .userName(userName)
                .extraContent(StringUtil.jsonEncode(extraContent))
                .appChannelId(appChannelId)
                .dataChannelId(dataChannelId)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        if (userInfoPreMapper.addUserPre(userInfoPreDAO) <= 0) {
            throw new UserException("60007", "添加预注册用户失败");
        }
        return userInfoPreDAO;
    }

    public UserInfoPreDAO updateUserPre(String preUserId, String userName, Map<String, Object> extraContent) {
        if (StringUtil.isEmpty(preUserId)) {
            throw new UserException("60011", "用户id错误");
        }
        UserInfoPreDAO userInfoPreDAO = userInfoPreMapper.getUserPre(GetUserPreCondition.builder()
                .preUserId(preUserId)
                .build());
        if (userInfoPreDAO == null) {
            throw new UserException("60009", "用户不存在");
        }
        userInfoPreDAO.setUpdateTime(new Date());
        if (userName != null) {
            userInfoPreDAO.setUserName(userName);
        }
        if (extraContent != null) {
            userInfoPreDAO.setExtraContent(StringUtil.jsonEncode(extraContent));
        }
        if (userInfoPreMapper.updateUserPre(userInfoPreDAO) <= 0) {
            throw new UserException("60010", "修改预注册用户失败");
        }
        return userInfoPreDAO;
    }

    public int getUserPreCount(GetUserPreCondition condition) {
        return userInfoPreMapper.getUserPreCount(condition);
    }

    public List<UserInfoPreDAO> getUserPreList(GetUserPreCondition condition, int start, int limit) {
        return userInfoPreMapper.getUserPreList(condition, start, limit);
    }

    public List<UserInfoPreDAO> getUserPreList(GetUserPreCondition condition) {
        return userInfoPreMapper.getUserPreList(condition, 0, 0);
    }

    public void deleteUserPre(String preUserId) {
        if (!StringUtil.isEmpty(preUserId)) {
            UserInfoPreDAO userInfoPreDAO = userInfoPreMapper.getUserPre(GetUserPreCondition.builder()
                    .preUserId(preUserId)
                    .build());
            if (userInfoPreDAO != null) {
                userInfoPreMapper.deleteUserPre(GetUserPreCondition.builder()
                        .preUserId(preUserId)
                        .build());
            }
        }
    }

    public int deleteUser(GetUserCondition condition) {
        return userInfoMapper.deleteUser(condition);
    }

    public int deleteUserPre(GetUserPreCondition condition) {
        return userInfoPreMapper.deleteUserPre(condition);
    }

    public void clearUserCache(String userId) {
        Set<String> keySet = RedisUtil.keys(systemService.getUserEntityRedisKey("*", userId));
        if (!keySet.isEmpty()) {
            RedisUtil.del(new ArrayList<>(keySet));
        }
        //清除身份相关的token缓存
        List<String> tokenList = tokenService.getTokenList(GetTokenCondition.builder()
                .userId(userId)
                .build()).stream().map(TokenInfoDAO::getToken).collect(Collectors.toList());
        if (!tokenList.isEmpty()) {
            RedisUtil.del(new ArrayList<>(tokenList.stream().map(token -> SystemConstant.REDIS_KEY_USER_TOKEN + token).collect(Collectors.toList())));
        }
    }

    public void clearUserCache(List<String> userIdList){
        if (CollectionUtils.isEmpty(userIdList)){
            return;
        }
        List<String> userKeyList = userIdList.stream().map(userId -> systemService.getUserEntityRedisKey("*", userId)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(userKeyList)){
            RedisUtil.del(userKeyList);
        }
        //清除身份相关的token缓存
        List<String> tokenList = tokenService.getTokenList(GetTokenCondition.builder().userIdList(userIdList).build())
                .stream().map(TokenInfoDAO::getToken).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tokenList)){
            RedisUtil.del(tokenList.stream().map(token -> SystemConstant.REDIS_KEY_USER_TOKEN + token).collect(Collectors.toList()));
        }
    }

    public List<UserDTO> doUserKeyFilter(List<UserDTO> userDTOList, UserKeyFilter userKeyFilter) {
        if (userDTOList != null) {
            for (UserDTO userDTO : userDTOList) {
                doUserKeyFilter(userDTO, userKeyFilter);
            }
        }
        return userDTOList;
    }

    public UserDTO doUserKeyFilter(UserDTO userDTO, UserKeyFilter userKeyFilter) {
        //过滤扩展字段的key
        if (userKeyFilter.getExtraContentKeyList() != null && !userKeyFilter.getExtraContentKeyList().isEmpty()) {
            Map<String, Object> newExtraContent = new HashMap<>();
            for (Map.Entry<String, Object> entry : userDTO.getExtraContent().entrySet()) {
                if (userKeyFilter.getExtraContentKeyList().contains(entry.getKey())) {
                    newExtraContent.put(entry.getKey(), entry.getValue());
                }
            }
            userDTO.setExtraContent(newExtraContent);
        }
        //过滤用户的身份列表属性
        if (userKeyFilter.getIsGetIdentityList() <= 0) {
            userDTO.setIdentityList(null);
        } else {
            if (userKeyFilter.getIdentityKeyFilter() != null) {
                userDTO.setIdentityList(identityService.doIdentityKeyFilter(userDTO.getIdentityList(), userKeyFilter.getIdentityKeyFilter()));
            }
        }
        return userDTO;
    }

    public void delUserCache(List<String> identityIdList){
        GetIdentityCondition getIdentityCondition = new GetIdentityCondition();
        getIdentityCondition.setIdentityIdList(identityIdList);
        List<IdentityDTO> userInfoDAOList = identityService.getIdentityList(getIdentityCondition);
        if (!CollectionUtils.isEmpty(userInfoDAOList)) {
            //清除用户缓存
            clearUserCache(userInfoDAOList.stream().map(IdentityDTO::getUserId).distinct().collect(Collectors.toList()));
        }

        //清除身份缓存
        identityService.clearIdentityCache(identityIdList);

    }

    public void updateUserCache(String appChannelId, List<String> identityIdList) {
        //更新用户缓存
        if (!CollectionUtils.isEmpty(identityIdList)) {
            identityIdList.forEach(identityId -> {
                tokenService.updateTokenData(appChannelId, identityId);
            });
        }
    }

    /**
     * 获取简单的公司用户列表
     *
     * @param request
     * @return com.microservice.user.entity.response.ms.MsResponse
     * <AUTHOR>
     * @date 2024/12/11 16:08
     **/
    public MsResponse getSimpleCompanyUserList(GetSimpleCompanyUserListRequest request) {
        // 获取部门及子部门
        List<String> departmentIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(request.getDepartmentId())) {
            List<DepartmentDTO> departmentDTOList = companyDepartmentRelationMapper.queryByCompanyId(request.getCompanyId());
            if (CollectionUtils.isEmpty(departmentDTOList)) {
                return MsResponse.success(DataListDTO.create(Collections.emptyList(), 0));
            }
            Map<String, List<DepartmentDTO>> departmentDTOMap = departmentDTOList.stream().collect(Collectors.groupingBy(DepartmentDTO::getParentId));
            departmentIdList = getDepartmentIdList(departmentDTOMap, request.getDepartmentId(), departmentIdList);
        }
        long dataCount = userInfoMapper.getUserAndIdentityCount(request.getCompanyId(), departmentIdList, request.getUserName());
        // 用户 + 部门
        if (dataCount < 1) {
            return MsResponse.success(DataListDTO.create(Collections.emptyList(), dataCount));
        }
        List<UserInfoWithIdentityDAO> userList = userInfoMapper.getUserAndIdentityList(request.getCompanyId(), departmentIdList
                , request.getUserName(), (request.getPage() - 1) * request.getLimit(), request.getLimit());
        // 公司
        List<String> companyIdList = userList.stream().map(UserInfoWithIdentityDAO::getIdentityList).flatMap(List::stream).map(IdentityInfoWithCompanyInfoDAO::getCompanyId).distinct().collect(Collectors.toList());
        List<CompanyInfoDAO> companyList = companyService.getCompanyList(GetCompanyCondition.builder().companyIdList(companyIdList).build());
        Map<String, CompanyInfoDAO> companyMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(companyList)) {
            companyMap = companyList.stream().collect(Collectors.toMap(CompanyInfoDAO::getCompanyId, Function.identity()));
        }
        List<String> identityIdList = null;
        if (!StringUtils.isEmpty(request.getUserName()) || !StringUtils.isEmpty(request.getDepartmentId()) || request.getLimit() > 0) {
            identityIdList = userList.stream().map(UserInfoWithIdentityDAO::getIdentityList).flatMap(List::stream).map(IdentityInfoWithCompanyInfoDAO::getIdentityId).distinct().collect(Collectors.toList());
        }
        //职位
        List<IPRWithPositionDAO> positionList = iprMapper.getPositionList(request.getCompanyId(), identityIdList);
        Map<String, List<IPRWithPositionDAO>> positionMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(positionList)) {
            positionMap = positionList.stream().collect(Collectors.groupingBy(IPRWithPositionDAO::getIdentityId));
        }
        //用户组
        List<IGRWithGroupDAO> groupList = igrMapper.getGroupList(request.getCompanyId(), identityIdList);
        Map<String, List<IGRWithGroupDAO>> groupMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(groupList)) {
            groupMap = groupList.stream().collect(Collectors.groupingBy(IGRWithGroupDAO::getIdentityId));
        }
        List<UserDTO> userDTOList = new ArrayList<>(userList.size());
        for (UserInfoWithIdentityDAO userInfoWithIdentityDAO : userList) {
            UserDTO userDTO = UserDTO.create(userInfoWithIdentityDAO);
            List<IdentityInfoWithCompanyInfoDAO> userIdentityList = userInfoWithIdentityDAO.getIdentityList();
            List<IdentityDTO> identityDTOList = new ArrayList<>(userIdentityList.size());
            for (IdentityInfoWithCompanyInfoDAO identityInfoWithCompanyInfoDAO : userInfoWithIdentityDAO.getIdentityList()) {
                CompanyInfoDAO companyInfoDAO = companyMap.get(identityInfoWithCompanyInfoDAO.getCompanyId());
                if (Objects.nonNull(companyInfoDAO)) {
                    identityInfoWithCompanyInfoDAO.setCompanyName(companyInfoDAO.getCompanyName());
                    identityInfoWithCompanyInfoDAO.setCompanyExtraContent(companyInfoDAO.getExtraContent());
                }
                IdentityDTO identityDTO = IdentityDTO.create(identityInfoWithCompanyInfoDAO);
                // 部门
                identityDTO.setDepartmentList(identityInfoWithCompanyInfoDAO.getDepartmentList());
                identityDTO.setDepartmentIdList(CollectionUtils.isEmpty(identityInfoWithCompanyInfoDAO.getDepartmentList())
                        ? Collections.emptyList() : identityInfoWithCompanyInfoDAO.getDepartmentList().stream().map(DepartmentDTO::getDepartmentId).collect(Collectors.toList()));
                // 职位
                List<IPRWithPositionDAO> iprDAOList = positionMap.getOrDefault(identityDTO.getIdentityId(), Collections.emptyList());
                identityDTO.setPositionList(iprDAOList.stream().map(PositionDTO::create).collect(Collectors.toList()));
                identityDTO.setPositionIdList(iprDAOList.stream().map(IPRWithPositionDAO::getPositionId).filter(dao -> !dao.isEmpty()).collect(Collectors.toList()));
                //用户组
                List<IGRWithGroupDAO> igrDAOList = groupMap.getOrDefault(identityInfoWithCompanyInfoDAO.getIdentityId(), new ArrayList<>());
                identityDTO.setGroupList(igrDAOList.stream().map(GroupDTO::create).collect(Collectors.toList()));
                identityDTO.setGroupIdList(igrDAOList.stream().map(IGRWithGroupDAO::getGroupId).filter(dao -> !dao.isEmpty()).collect(Collectors.toList()));
                if (request.getCompanyId().equals(identityInfoWithCompanyInfoDAO.getCompanyId())) {
                    identityDTO.setIsSelected(1);
                    userDTO.setIdentityId(identityInfoWithCompanyInfoDAO.getIdentityId());
                }
                identityDTOList.add(identityDTO);
            }
            userDTO.setIdentityList(identityDTOList);
            userDTOList.add(userDTO);
        }
        return MsResponse.success(DataListDTO.create(userDTOList, dataCount));
    }

    /**
     * 获取部门及子部门列表
     *
     * @param departmentDTOMap
     * @param departmentId
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/12/18 18:05
     **/
    private List<String> getDepartmentIdList(Map<String, List<DepartmentDTO>> departmentDTOMap, String departmentId, List<String> departmentIdList) {
        departmentIdList.add(departmentId);
        List<DepartmentDTO> subList = departmentDTOMap.get(departmentId);
        if (CollectionUtils.isEmpty(subList)) {
            return departmentIdList;
        }
        for (DepartmentDTO departmentDTO : subList) {
            getDepartmentIdList(departmentDTOMap, departmentDTO.getDepartmentId(), departmentIdList);
        }
        return departmentIdList;
    }
}
