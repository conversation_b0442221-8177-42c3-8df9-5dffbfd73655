package com.microservice.user.service;

import com.microservice.user.entity.condition.GetCompanyPositionCondition;
import com.microservice.user.entity.condition.GetDepartmentPositionCondition;
import com.microservice.user.entity.condition.GetIdentityPositionCondition;
import com.microservice.user.entity.condition.GetPositionCondition;
import com.microservice.user.entity.dao.CPRWithPositionDAO;
import com.microservice.user.entity.dao.DPRWithPositionDAO;
import com.microservice.user.entity.dao.IdentityPositionRelationDAO;
import com.microservice.user.entity.dao.PositionInfoDAO;
import com.microservice.user.entity.dto.ms.PositionDTO;
import com.microservice.user.exception.PositionException;
import com.microservice.user.mapper.PositionInfoMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PositionService {

    @Autowired
    private PositionInfoMapper positionInfoMapper;
    @Autowired
    private CompanyPositionService companyPositionService;
    @Autowired
    private DepartmentPositionService departmentPositionService;
    @Autowired
    private IdentityPositionService identityPositionService;

    public List<PositionDTO> getPositionList(GetPositionCondition condition) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return getPositionList(condition, 0, 0);
    }

    public List<PositionDTO> getPositionList(GetPositionCondition condition, int start, int limit) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        if (!StringUtil.isEmpty(condition.getDepartmentId())
                || (condition.getDepartmentIdList() != null && !condition.getDepartmentIdList().isEmpty())) {
            return departmentPositionService.getDPRWithPositionList(
                            GetDepartmentPositionCondition.builder()
                                    .departmentId(condition.getDepartmentId())
                                    .departmentIdList(condition.getDepartmentIdList())
                                    .positionIdList(condition.getPositionIdList())
                                    .positionId(condition.getPositionId())
                                    .build(), start, limit)
                    .stream().map(PositionDTO::create).collect(Collectors.toList());
        } else if (!StringUtil.isEmpty(condition.getCompanyId())
                || (condition.getCompanyIdList() != null && !condition.getCompanyIdList().isEmpty())) {
            List<PositionDTO> positionDTOList = companyPositionService.getCPRWithPositionList(
                            GetCompanyPositionCondition.builder()
                                    .companyId(condition.getCompanyId())
                                    .companyIdList(condition.getCompanyIdList())
                                    .positionId(condition.getPositionId())
                                    .positionIdList(condition.getPositionIdList())
                                    .build(), start, limit)
                    .stream().map(PositionDTO::create).collect(Collectors.toList());
            Map<String, String> positionId2DepartmentIdMap = departmentPositionService.getDPRWithPositionList(
                    GetDepartmentPositionCondition.builder()
                            .positionIdList(positionDTOList.stream().map(PositionDTO::getPositionId).collect(Collectors.toList()))
                            .build()).stream().collect(Collectors.toMap(DPRWithPositionDAO::getPositionId, DPRWithPositionDAO::getDepartmentId, (dao1, dao2) -> dao2));
            positionDTOList = positionDTOList.stream().peek(positionDTO -> positionDTO.setDepartmentId(positionId2DepartmentIdMap.getOrDefault(positionDTO.getPositionId(), ""))).collect(Collectors.toList());
            return positionDTOList;
        } else {
            return positionInfoMapper.getPositionList(condition, start, limit)
                    .stream()
                    .map(PositionDTO::create)
                    .collect(Collectors.toList());
        }
    }

    public int getPositionCount(GetPositionCondition condition) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getPositionIdList() != null && condition.getPositionIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        if (!StringUtil.isEmpty(condition.getDepartmentId())
                || (condition.getDepartmentIdList() != null && !condition.getDepartmentIdList().isEmpty())) {
            return departmentPositionService.getDPRWithPositionCount(
                    GetDepartmentPositionCondition.builder()
                            .departmentId(condition.getDepartmentId())
                            .departmentIdList(condition.getDepartmentIdList())
                            .positionIdList(condition.getPositionIdList())
                            .positionId(condition.getPositionId())
                            .build());
        } else if (!StringUtil.isEmpty(condition.getCompanyId())
                || (condition.getCompanyIdList() != null && !condition.getCompanyIdList().isEmpty())) {
            return companyPositionService.getCPRWithPositionCount(
                    GetCompanyPositionCondition.builder()
                            .companyId(condition.getCompanyId())
                            .companyIdList(condition.getCompanyIdList())
                            .positionId(condition.getPositionId())
                            .positionIdList(condition.getPositionIdList())
                            .build());
        } else {
            return positionInfoMapper.getPositionCount(condition);
        }
    }

    public PositionInfoDAO addPosition(String positionName, String parentId, Map<String, Object> extraContent) {
        if (extraContent == null || extraContent.isEmpty()) {
            extraContent = new HashMap<>() {{
                put("positionName", positionName);
            }};
        }
        PositionInfoDAO positionInfoDAO = new PositionInfoDAO();
        positionInfoDAO.setPositionId(StringUtil.createUuid());
        positionInfoDAO.setPositionName(positionName);
        positionInfoDAO.setParentId(parentId);
        positionInfoDAO.setExtraContent(StringUtil.jsonEncode(extraContent));
        if (positionInfoMapper.addPosition(positionInfoDAO) <= 0) {
            throw new PositionException("68001", "添加部门失败");
        }
        return positionInfoDAO;
    }

    public PositionInfoDAO getPosition(String positionId) {
        return getPosition(GetPositionCondition.builder()
                .positionId(positionId)
                .build());
    }

    public PositionInfoDAO getPosition(GetPositionCondition condition) {
        return positionInfoMapper.getPosition(condition);
    }

    public void updatePosition(PositionInfoDAO positionInfoDAO) {
        positionInfoDAO.setUpdateTime(new Date());
        if (positionInfoMapper.updatePosition(
                positionInfoDAO,
                GetPositionCondition.builder()
                        .positionId(positionInfoDAO.getPositionId())
                        .build()
        ) <= 0) {
            throw new PositionException("68002", "更新部门失败");
        }
    }

    public PositionDTO deletePosition(String positionId, int isForce) {
        PositionInfoDAO positionInfoDAO = getPosition(positionId);
        if (positionInfoDAO == null) {
            return null;
        }
        //检查下级职位
        PositionInfoDAO child = positionInfoMapper.getPosition(GetPositionCondition.builder()
                .parentId(positionId)
                .build());
        if (child != null) {
            if (isForce <= 0) {
                throw new PositionException("68003", "职位下存在下级职位，不能删除");
            }
            positionInfoMapper.deletePosition(GetPositionCondition.builder()
                    .parentId(positionId)
                    .build());
        }
        //检查用户
        List<IdentityPositionRelationDAO> idrList = identityPositionService.getIPRList(GetIdentityPositionCondition.builder().positionId(positionId).build());
        if (!idrList.isEmpty()) {
            if (isForce <= 0) {
                throw new PositionException("68004", "职位下存在用户，不能删除");
            }
            identityPositionService.deleteIPRByPositionId(positionId);
        }
        //删除职位
        if (positionInfoMapper.deletePosition(GetPositionCondition.builder().positionId(positionId).build()) <= 0) {
            throw new PositionException("68005", "删除失败");
        }
        return PositionDTO.create(positionInfoDAO);
    }

    public int deletePosition(GetPositionCondition condition) {
        return positionInfoMapper.deletePosition(condition);
    }
}
