package com.microservice.user.service;

import com.microservice.user.entity.condition.GetIdentityFriendCondition;
import com.microservice.user.entity.dao.IdentityFriendRelationDAO;
import com.microservice.user.exception.IdentityFriendException;
import com.microservice.user.mapper.IdentityFriendRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class IdentityFriendService {

    @Autowired
    private IdentityFriendRelationMapper ifrMapper;

    public List<IdentityFriendRelationDAO> addIdentityFriend(String identityId, List<String> targetIdentityIdList, Map<String, Object> extraContent, String appChannelId, String dataChannelId) {
        //当前身份所有的好友列表
        List<IdentityFriendRelationDAO> identityFriendList = ifrMapper.getIdentityFriendRelationList(GetIdentityFriendCondition.builder().identityId(identityId).build(), 0, 0);
        //目标身份为当前身份的所有的好友列表
        List<IdentityFriendRelationDAO> targetIdentityFriendList = ifrMapper.getIdentityFriendRelationList(GetIdentityFriendCondition.builder().targetIdentityId(identityId).build(), 0, 0);
        //所有的好友关系 关系标识: identityId_targetIdentityId
        Set<String> relationSet = identityFriendList.stream().map(dao -> dao.getIdentityId() + "_" + dao.getTargetIdentityId()).collect(Collectors.toSet());
        relationSet.addAll(targetIdentityFriendList.stream().map(dao -> dao.getIdentityId() + "_" + dao.getTargetIdentityId()).collect(Collectors.toSet()));
        //添加好友关系
        List<IdentityFriendRelationDAO> dataList = new LinkedList<>();
        for (String targetIdentityId : targetIdentityIdList) {
            if (!relationSet.contains(identityId + "_" + targetIdentityId)) {
                dataList.add(IdentityFriendRelationDAO.builder()
                        .identityId(identityId)
                        .targetIdentityId(targetIdentityId)
                        .extraContent(StringUtil.jsonEncode(extraContent))
                        .appChannelId(appChannelId)
                        .dataChannelId(dataChannelId)
                        .build());
            }
            if (!relationSet.contains(targetIdentityId + "_" + identityId)) {
                dataList.add(IdentityFriendRelationDAO.builder()
                        .identityId(targetIdentityId)
                        .targetIdentityId(identityId)
                        .extraContent("{}")
                        .appChannelId(appChannelId)
                        .dataChannelId(dataChannelId)
                        .build());
            }
        }
        if (dataList.size() > 0) {
            ifrMapper.addIdentityFriendRelationList(dataList);
        }
        return dataList;
    }

    public IdentityFriendRelationDAO getIdentityFriendRelation(String identityId, String targetIdentityId) {
        return ifrMapper.getIdentityFriendRelation(GetIdentityFriendCondition.builder()
                .identityId(identityId)
                .targetIdentityId(targetIdentityId)
                .build());
    }

    public void updateIdentityFriendRelation(IdentityFriendRelationDAO identityFriendRelationDAO) {
        identityFriendRelationDAO.setUpdateTime(new Date());
        ifrMapper.updateIdentityFriendRelation(
                identityFriendRelationDAO,
                GetIdentityFriendCondition.builder()
                        .identityId(identityFriendRelationDAO.getIdentityId())
                        .targetIdentityId(identityFriendRelationDAO.getTargetIdentityId())
                        .build());
    }

    public List<IdentityFriendRelationDAO> getIdentityFriendRelationList(GetIdentityFriendCondition condition, int start, int limit) {
        if ((condition.getTargetIdentityIdList() != null && condition.getTargetIdentityIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return ifrMapper.getIdentityFriendRelationList(condition, start, limit);
    }

    public List<IdentityFriendRelationDAO> getIdentityFriendRelationList(GetIdentityFriendCondition condition) {
        return getIdentityFriendRelationList(condition, 0, 0);
    }

    public void deleteIdentityFriendRelation(String identityId, String targetIdentityId) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityFriendException("61301", "身份id错误");
        }
        if (StringUtil.isEmpty(targetIdentityId)) {
            throw new IdentityFriendException("61302", "目标身份id错误");
        }
        ifrMapper.deleteIdentityFriendRelation(GetIdentityFriendCondition.builder()
                .identityId(identityId)
                .targetIdentityId(targetIdentityId)
                .build());
    }

    public int getIdentityFriendRelationCount(GetIdentityFriendCondition condition) {
        if ((condition.getTargetIdentityIdList() != null && condition.getTargetIdentityIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return ifrMapper.getIdentityFriendRelationCount(condition);
    }
}
