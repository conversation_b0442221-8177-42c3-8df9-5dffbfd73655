package com.microservice.user.service;

import com.microservice.user.entity.condition.GetCompanyDepartmentCondition;
import com.microservice.user.entity.dao.CDRWithDepartmentDAO;
import com.microservice.user.entity.dao.CompanyDepartmentRelationDAO;
import com.microservice.user.mapper.CompanyDepartmentRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CompanyDepartmentService {

    @Autowired
    private CompanyDepartmentRelationMapper cdrMapper;

    public List<CompanyDepartmentRelationDAO> getCDRListByCompanyId(String companyId) {
        return cdrMapper.getCompanyDepartmentRelationList(
                GetCompanyDepartmentCondition.builder()
                        .companyId(companyId)
                        .build()
        );
    }

    public void deleteCDRByCompanyId(String companyId) {
        cdrMapper.deleteCompanyDepartmentRelation(
                GetCompanyDepartmentCondition.builder()
                        .companyId(companyId)
                        .build()
        );
    }

    public List<CDRWithDepartmentDAO> getCDRWithDepartmentList(GetCompanyDepartmentCondition condition, int start, int limit) {
        if ((condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return cdrMapper.getCDRWithDepartmentList(condition, start, limit);
    }

    public int getCDRWithDepartmentCount(GetCompanyDepartmentCondition condition) {
        if ((condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())) {
            return 0;
        }
        return cdrMapper.getCDRWithDepartmentCount(condition);
    }

    public int addCompanyDepartmentRelation(String companyId, String departmentId) {
        return cdrMapper.addCompanyDepartmentRelation(
                CompanyDepartmentRelationDAO.builder()
                        .companyId(companyId)
                        .departmentId(departmentId)
                        .build()
        );
    }

    public List<CompanyDepartmentRelationDAO> getCDRList(GetCompanyDepartmentCondition condition) {
        if ((condition.getDepartmentIdList() != null && condition.getDepartmentIdList().isEmpty())
                || (condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return cdrMapper.getCompanyDepartmentRelationList(condition);
    }

    public int deleteCDR(GetCompanyDepartmentCondition condition) {
        return cdrMapper.deleteCompanyDepartmentRelation(condition);
    }
}
