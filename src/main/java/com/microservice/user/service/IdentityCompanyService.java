package com.microservice.user.service;

import com.microservice.user.entity.condition.GetIdentityCompanyCondition;
import com.microservice.user.entity.dao.ICRWithIdentityDAO;
import com.microservice.user.entity.dao.IdentityCompanyCountDAO;
import com.microservice.user.entity.dao.IdentityCompanyRelationDAO;
import com.microservice.user.exception.IdentityCompanyException;
import com.microservice.user.mapper.IdentityCompanyRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class IdentityCompanyService {

    @Autowired
    private IdentityCompanyRelationMapper icrMapper;

    public void deleteICRByIdentityId(String identityId) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityCompanyException("62001", "身份id错误");
        }
        icrMapper.deleteIdentityCompanyRelation(GetIdentityCompanyCondition.builder().identityId(identityId).build());
    }

    public void addIdentityCompanyRelationList(String identityId, List<String> companyIdList, Map<String, Object> extraContent) {
        if (companyIdList != null && companyIdList.size() > 0) {
            //验重复
            List<String> existCompanyIdList = icrMapper.getIdentityCompanyRelationList(GetIdentityCompanyCondition.builder()
                    .companyIdList(companyIdList)
                    .identityId(identityId)
                    .build(), 0, 0).stream().map(IdentityCompanyRelationDAO::getCompanyId).collect(Collectors.toList());
            companyIdList = companyIdList.stream().filter(companyId -> !StringUtil.isEmpty(companyId) && !existCompanyIdList.contains(companyId)).collect(Collectors.toList());
            if (!companyIdList.isEmpty()) {
                icrMapper.addIdentityCompanyRelationList(
                        companyIdList.stream()
                                .map(companyId -> IdentityCompanyRelationDAO.builder()
                                        .companyId(companyId)
                                        .identityId(identityId)
                                        .extraContent(extraContent == null ? "{}" : StringUtil.jsonEncode(extraContent))
                                        .build())
                                .collect(Collectors.toList()));
            }
        }
    }

    public void addIdentityCompanyRelationList(List<String> identityIdList, String companyId) {
        if (identityIdList != null && identityIdList.size() > 0) {
            icrMapper.addIdentityCompanyRelationList(
                    identityIdList.stream()
                            .map(identityId -> IdentityCompanyRelationDAO.builder()
                                    .companyId(companyId)
                                    .identityId(identityId)
                                    .build())
                            .collect(Collectors.toList()));
        }
    }

    public void addIdentityCompanyRelation(String identityId, String companyId) {
        IdentityCompanyRelationDAO identityCompanyRelationDAO = icrMapper.getIdentityCompanyRelation(
                GetIdentityCompanyCondition.builder()
                        .identityId(identityId)
                        .companyId(companyId)
                        .build()
        );
        if (identityCompanyRelationDAO == null) {
            if (icrMapper.addIdentityCompanyRelation(IdentityCompanyRelationDAO.builder()
                    .identityId(identityId)
                    .companyId(companyId)
                    .build()) <= 0) {
                throw new IdentityCompanyException("62002", "添加身份和公司关系失败");
            }
        }
    }

    public void updateIdentityCompanyRelationList(String identityId, List<String> companyIdList, Map<String, Object> extraContent) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityCompanyException("62003", "身份id错误");
        }
        icrMapper.deleteIdentityCompanyRelation(GetIdentityCompanyCondition.builder()
                .identityId(identityId)
                .build());
        addIdentityCompanyRelationList(identityId, companyIdList, extraContent);
    }

    public List<IdentityCompanyRelationDAO> getICRListByCompanyId(String companyId) {
        return icrMapper.getIdentityCompanyRelationList(
                GetIdentityCompanyCondition.builder()
                        .companyId(companyId)
                        .build(), 0, 0
        );
    }

    public int getICRCountByCompanyId(String companyId) {
        return icrMapper.getIdentityCompanyRelationCount(
                GetIdentityCompanyCondition.builder()
                        .companyId(companyId)
                        .build()
        );
    }

    public int getICRCountByCompanyId(GetIdentityCompanyCondition condition) {
        return icrMapper.getIdentityCompanyRelationCount(condition);
    }

    public List<IdentityCompanyCountDAO> getICRCountGroupByCompanyId(GetIdentityCompanyCondition condition) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return icrMapper.getICRCountGroupByCompanyId(condition);
    }

    public List<ICRWithIdentityDAO> getICRWithIdentityList(GetIdentityCompanyCondition condition, int start, int limit) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return icrMapper.getICRWithIdentityList(condition, start, limit);
    }

    public List<ICRWithIdentityDAO> getICRWithIdentityList(GetIdentityCompanyCondition condition) {
        return getICRWithIdentityList(condition, 0, 0);
    }

    public void deleteIdentityCompanyRelation(String identityId, List<String> companyIdList) {
        if (!StringUtil.isEmpty(identityId) && !companyIdList.isEmpty()) {
            icrMapper.deleteIdentityCompanyRelation(GetIdentityCompanyCondition.builder()
                    .companyIdList(companyIdList)
                    .identityId(identityId)
                    .build());
        }
    }

    public int deleteICR(GetIdentityCompanyCondition condition) {
        return icrMapper.deleteIdentityCompanyRelation(condition);
    }

    public int getICRWithIdentityCount(GetIdentityCompanyCondition condition) {
        if ((condition.getCompanyIdList() != null && condition.getCompanyIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())) {
            return 0;
        }
        return icrMapper.getICRWithIdentityCount(condition);
    }
}
