package com.microservice.user.service;

import com.microservice.user.entity.condition.GetIdentityDepartmentCondition;
import com.microservice.user.entity.condition.GetIdentityEntityCondition;
import com.microservice.user.entity.dao.IdentityDepartmentRelationDAO;
import com.microservice.user.entity.dao.IdentityEntityRelationDAO;
import com.microservice.user.exception.IdentityEntityException;
import com.microservice.user.mapper.IdentityEntityRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class IdentityEntityService {

    @Autowired
    private IdentityEntityRelationMapper ierMapper;

    public void addIdentityEntityRelationList(String identityId, List<String> entityIdList, Map<String, Object> extraContent) {
        if (entityIdList != null && !entityIdList.isEmpty()) {
            entityIdList = entityIdList.stream().distinct().filter(entityId -> !StringUtil.isEmpty(entityId)).collect(Collectors.toList());
            //查询已存在的关系
            List<String> existEntityIdList = getIERList(
                    GetIdentityEntityCondition.builder()
                            .identityId(identityId)
                            .build()
            ).stream().map(IdentityEntityRelationDAO::getEntityId).collect(Collectors.toList());
            //过滤已存在的entity_id
            entityIdList.removeAll(existEntityIdList);
            if (!entityIdList.isEmpty()) {
                ierMapper.addIdentityEntityRelationList(
                        entityIdList.stream()
                                .map(entityId -> IdentityEntityRelationDAO.builder()
                                        .entityId(entityId)
                                        .identityId(identityId)
                                        .extraContent(extraContent == null ? "{}" : StringUtil.jsonEncode(extraContent))
                                        .build())
                                .collect(Collectors.toList()));
            }
        }
    }

    public void deleteIdentityEntityRelation(String identityId, List<String> entityIdList) {
        if (!StringUtil.isEmpty(identityId) && !entityIdList.isEmpty()) {
            ierMapper.deleteIdentityEntityRelation(GetIdentityEntityCondition.builder()
                    .entityIdList(entityIdList)
                    .identityId(identityId)
                    .build());
        }
    }

    public void updateIdentityEntityRelationList(String identityId, List<String> entityIdList, Map<String, Object> extraContent) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityEntityException("61701", "身份id错误");
        }
        ierMapper.deleteIdentityEntityRelation(GetIdentityEntityCondition.builder()
                .identityId(identityId)
                .build());
        addIdentityEntityRelationList(identityId, entityIdList, extraContent);
    }

    public void deleteIERByIdentityId(String identityId) {
        if (StringUtil.isEmpty(identityId)) {
            throw new IdentityEntityException("61702", "身份id错误");
        }
        ierMapper.deleteIdentityEntityRelation(GetIdentityEntityCondition.builder().identityId(identityId).build());
    }

    public List<IdentityEntityRelationDAO> getIERList(GetIdentityEntityCondition condition) {
        return ierMapper.getIdentityEntityRelationList(condition, 0, 0);
    }

    public int deleteIER(GetIdentityEntityCondition condition) {
        return ierMapper.deleteIdentityEntityRelation(condition);
    }
}
