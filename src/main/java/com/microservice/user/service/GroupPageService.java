package com.microservice.user.service;

import com.microservice.user.entity.condition.GetGroupPageCondition;
import com.microservice.user.entity.dao.GroupPageRelationDAO;
import com.microservice.user.exception.GroupPageException;
import com.microservice.user.mapper.GroupPageRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class GroupPageService {

    @Autowired
    private GroupPageRelationMapper gprMapper;

    public List<GroupPageRelationDAO> getGPRList(GetGroupPageCondition condition) {
        if ((condition.getPageIdList() != null && condition.getPageIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getGroupIdList() != null && condition.getGroupIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return gprMapper.getGPRList(condition);
    }

    public Map<String, List<String>> getGroupId2PageIdListMap(List<String> groupIdList) {
        if (groupIdList.isEmpty()) {
            return new HashMap<>();
        }
        List<GroupPageRelationDAO> gerDAOList = gprMapper.getGroupPageRelationList(
                GetGroupPageCondition.builder()
                        .groupIdList(groupIdList)
                        .build()
        );
        return gerDAOList.stream().collect(
                Collectors.groupingBy(
                        GroupPageRelationDAO::getGroupId,
                        Collectors.mapping(GroupPageRelationDAO::getPageId, Collectors.toList())
                )
        );
    }

    public void addGPRList(String groupId, List<String> pageIdList) {
        if (pageIdList != null && !pageIdList.isEmpty()) {
            gprMapper.addGroupPageRelationList(
                    pageIdList.stream()
                            .map(pageId -> GroupPageRelationDAO.builder()
                                    .pageId(pageId)
                                    .groupId(groupId)
                                    .build())
                            .collect(Collectors.toList())
            );
        }
    }

    public void updateGroupPageRelation(String groupId, List<String> pageIdList) {
        if (StringUtil.isEmpty(groupId)) {
            throw new GroupPageException("61101", "用户组id错误");
        }
        gprMapper.deleteGroupPageRelation(GetGroupPageCondition.builder().groupId(groupId).build());
        addGPRList(groupId, pageIdList);
    }

    public void deleteGPRByGroupId(String groupId) {
        if (StringUtil.isEmpty(groupId)) {
            throw new GroupPageException("61102", "用户组id错误");
        }
        gprMapper.deleteGroupPageRelation(GetGroupPageCondition.builder().groupId(groupId).build());
    }

    public int deleteGPR(GetGroupPageCondition condition) {
        return gprMapper.deleteGroupPageRelation(condition);
    }


    public void addGPRList(List<GroupPageRelationDAO> groupPageRelationDAOList) {
        if (!groupPageRelationDAOList.isEmpty()) {
            gprMapper.addGroupPageRelationList(groupPageRelationDAOList);
        }
    }
}
