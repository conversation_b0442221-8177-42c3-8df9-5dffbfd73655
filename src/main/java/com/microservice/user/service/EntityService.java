package com.microservice.user.service;

import com.microservice.user.entity.condition.GetEntityCondition;
import com.microservice.user.entity.dao.EntityInfoDAO;
import com.microservice.user.exception.EntityException;
import com.microservice.user.mapper.EntityInfoMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class EntityService {

    @Autowired
    private EntityInfoMapper entityInfoMapper;

    public EntityInfoDAO addEntity(String entityName, Integer type, Map<String, Object> extraContent, Integer sort, String appChannelId, String dataChannelId) {
        if (extraContent == null || extraContent.isEmpty()) {
            extraContent = new HashMap<>() {{
                put("entityName", entityName);
            }};
        }
        EntityInfoDAO entityInfoDAO = new EntityInfoDAO();
        entityInfoDAO.setEntityId(StringUtil.createUuid());
        entityInfoDAO.setEntityName(entityName);
        entityInfoDAO.setType(type);
        entityInfoDAO.setExtraContent(StringUtil.jsonEncode(extraContent));
        entityInfoDAO.setSort(sort);
        entityInfoDAO.setAppChannelId(appChannelId);
        entityInfoDAO.setDataChannelId(dataChannelId);
        entityInfoDAO.setCreateTime(new Date());
        if (entityInfoMapper.addEntity(entityInfoDAO) <= 0) {
            throw new EntityException("61601", "添加标签失败");
        }
        return entityInfoDAO;
    }

    public EntityInfoDAO getEntity(String entityId) {
        return entityInfoMapper.getEntity(GetEntityCondition.builder().entityId(entityId).build());
    }

    public EntityInfoDAO getEntity(GetEntityCondition condition) {
        return entityInfoMapper.getEntity(condition);
    }


    public void updateEntity(EntityInfoDAO updateData) {
        updateData.setUpdateTime(new Date());
        if (entityInfoMapper.updateEntity(
                updateData,
                GetEntityCondition.builder()
                        .entityId(updateData.getEntityId())
                        .build()
        ) <= 0) {
            throw new EntityException("61602", "修改标签失败");
        }
    }

    public void deleteEntity(String entityId) {
        if (StringUtil.isEmpty(entityId)) {
            throw new EntityException("61603", "标签id错误");
        }
        entityInfoMapper.deleteEntity(GetEntityCondition.builder()
                .entityId(entityId)
                .build());
    }

    public List<EntityInfoDAO> getEntityList(GetEntityCondition condition, int start, int limit) {
        if ((condition.getEntityIdList() != null && condition.getEntityIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return entityInfoMapper.getEntityList(condition, start, limit);
    }

    public int getEntityCount(GetEntityCondition condition) {
        if ((condition.getEntityIdList() != null && condition.getEntityIdList().isEmpty())) {
            return 0;
        }
        return entityInfoMapper.getEntityCount(condition);
    }

    public List<EntityInfoDAO> getEntityList(GetEntityCondition condition) {
        return getEntityList(condition, 0, 0);
    }
}
