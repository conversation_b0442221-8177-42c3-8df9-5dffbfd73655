package com.microservice.user.service;

import com.microservice.user.entity.condition.GetGroupEventCondition;
import com.microservice.user.entity.dao.GroupEventRelationDAO;
import com.microservice.user.exception.GroupEventException;
import com.microservice.user.mapper.GroupEventRelationMapper;
import com.microservice.user.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class GroupEventService {

    @Autowired
    private GroupEventRelationMapper gerMapper;

    public List<GroupEventRelationDAO> getGERList(GetGroupEventCondition condition) {
        if ((condition.getEventIdList() != null && condition.getEventIdList().isEmpty())
                || (condition.getIdentityIdList() != null && condition.getIdentityIdList().isEmpty())
                || (condition.getGroupIdList() != null && condition.getGroupIdList().isEmpty())) {
            return new ArrayList<>();
        }
        return gerMapper.getGERList(condition);
    }

    public Map<String, List<String>> getGroupId2EventIdListMap(List<String> groupIdList) {
        if (groupIdList.isEmpty()) {
            return new HashMap<>();
        }
        List<GroupEventRelationDAO> gerDAOList = gerMapper.getGroupEventRelationList(
                GetGroupEventCondition.builder()
                        .groupIdList(groupIdList)
                        .build()
        );
        return gerDAOList.stream().collect(
                Collectors.groupingBy(
                        GroupEventRelationDAO::getGroupId,
                        Collectors.mapping(GroupEventRelationDAO::getEventId, Collectors.toList())
                )
        );
    }

    public void addGERList(String groupId, List<String> eventIdList) {
        if (eventIdList != null && !eventIdList.isEmpty()) {
            gerMapper.addGroupEventRelationList(
                    eventIdList.stream()
                            .map(eventId -> GroupEventRelationDAO.builder()
                                    .eventId(eventId)
                                    .groupId(groupId)
                                    .build())
                            .collect(Collectors.toList())
            );
        }
    }

    public void updateGroupEventRelation(String groupId, List<String> eventIdList) {
        if (StringUtil.isEmpty(groupId)) {
            throw new GroupEventException("61101", "用户组id错误");
        }
        gerMapper.deleteGroupEventRelation(GetGroupEventCondition.builder().groupId(groupId).build());
        addGERList(groupId, eventIdList);
    }

    public void deleteGERByGroupId(String groupId) {
        if (StringUtil.isEmpty(groupId)) {
            throw new GroupEventException("61102", "用户组id错误");
        }
        gerMapper.deleteGroupEventRelation(GetGroupEventCondition.builder().groupId(groupId).build());
    }

    public int deleteGER(GetGroupEventCondition condition) {
        return gerMapper.deleteGroupEventRelation(condition);
    }

    public void addGERList(List<GroupEventRelationDAO> groupEventRelationDAOList) {
        if (!groupEventRelationDAOList.isEmpty()) {
            gerMapper.addGroupEventRelationList(groupEventRelationDAOList);
        }
    }

    public List<GroupEventRelationDAO> getGroupEventRelationList(GetGroupEventCondition condition) {
        return gerMapper.getGroupEventRelationList(condition);
    }
}
