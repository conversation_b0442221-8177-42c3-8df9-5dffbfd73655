package com.microservice.user.common;

import com.microservice.user.entity.response.ms.MsResponse;
import com.microservice.user.exception.*;
import com.microservice.user.util.LogUtil;
import com.microservice.user.util.StringUtil;
import com.mssdk.log.entity.LogEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Objects;

@RestControllerAdvice
public class MsExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public MsResponse handler(MethodArgumentNotValidException e) {
        String errorMessage = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
        LogUtil.error(errorMessage);
        return MsResponse.fail("50000", errorMessage);
    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public MsResponse handler(HttpMediaTypeNotSupportedException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail("61000", "请求参数格式错误:" + e.getContentType());
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public MsResponse handler(HttpMessageNotReadableException e) {
        LogUtil.error(e.getMessage());
        e.printStackTrace();
        return MsResponse.fail("62000", "请求参数错误");
    }

    @ExceptionHandler(HttpHeaderEmptyException.class)
    public MsResponse handler(HttpHeaderEmptyException e) {
        LogUtil.error("[" + e.getMessage() + "][" + StringUtil.jsonEncode(e.getHeader()) + "]");
        return MsResponse.fail("63000", "请求头错误:" + e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public MsResponse handler(Exception e) {
        LogUtil.error(e.getMessage());
        e.printStackTrace();
        return MsResponse.fail("60000", "用户微服务系统繁忙，请稍后再试");
    }

    @ExceptionHandler(TokenException.class)
    public MsResponse handler(TokenException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(UserException.class)
    public MsResponse handler(UserException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(IdentityException.class)
    public MsResponse handler(IdentityException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(CompanyException.class)
    public MsResponse handler(CompanyException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(PositionException.class)
    public MsResponse handler(PositionException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(DepartmentException.class)
    public MsResponse handler(DepartmentException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(GroupException.class)
    public MsResponse handler(GroupException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(EntityException.class)
    public MsResponse handler(EntityException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(ChatGroupException.class)
    public MsResponse handler(ChatGroupException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(DepartmentPositionException.class)
    public MsResponse handler(DepartmentPositionException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(EntityRelationException.class)
    public MsResponse handler(EntityRelationException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(GroupEventException.class)
    public MsResponse handler(GroupEventException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(IdentityChatGroupRelationException.class)
    public MsResponse handler(IdentityChatGroupRelationException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(IdentityCompanyException.class)
    public MsResponse handler(IdentityCompanyException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(IdentityDepartmentException.class)
    public MsResponse handler(IdentityDepartmentException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(IdentityEntityException.class)
    public MsResponse handler(IdentityEntityException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(IdentityFriendException.class)
    public MsResponse handler(IdentityFriendException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(IdentityGroupException.class)
    public MsResponse handler(IdentityGroupException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(IdentityPositionException.class)
    public MsResponse handler(IdentityPositionException e) {
        LogUtil.error(e.getMessage());
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(CommonException.class)
    public MsResponse handleBadRequestException(CommonException e) {
        LogUtil.error(e.getMessage());
        e.printStackTrace();
        return MsResponse.fail(e.getCode(), e.getMessage());
    }

}
