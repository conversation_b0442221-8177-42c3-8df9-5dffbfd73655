package com.microservice.user.common;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.microservice.user.entity.condition.QueryCondition;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import com.microservice.user.util.StringUtil;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class Common {

    private static final ThreadLocal<String> appChannelId = new ThreadLocal<>();
    private static final ThreadLocal<String> dataChannelId = new ThreadLocal<>();
    private static final TransmittableThreadLocal<String> traceId = new TransmittableThreadLocal<>();
    private static final ThreadLocal<String> ip = new ThreadLocal<>();

    public static void setAppChannelId(String appChannelId) {
        Common.appChannelId.set(appChannelId);
    }

    public static String getAppChannelId() {
        return Common.appChannelId.get();
    }

    public static void setDataChannelId(String dataChannelId) {
        Common.dataChannelId.set(dataChannelId);
    }

    public static String getDataChannelId() {
        return Common.dataChannelId.get();
    }

    public static void setIp(String ip) {
        Common.ip.set(ip);
    }

    public static String getIp() {
        return Common.ip.get();
    }

    public static void setTraceId(String traceId) {
        Common.traceId.set(traceId);
    }

    public static String getTraceId() {
        return Common.traceId.get();
    }

    public static String getAppChannelId(MsBaseRequest request) {
        return StringUtil.isEmpty(request.getAppChannelId()) ? Common.appChannelId.get() : request.getAppChannelId();
    }

    public static String getDataChannelId(MsBaseRequest request) {
        return StringUtil.isEmpty(request.getDataChannelId()) ? Common.dataChannelId.get() : request.getDataChannelId();
    }

    public static Map<String, Object> createRequestConditionMap(Map<String, Object> conditionMap, Map<String, Object> requestConditionMap) {
        if (requestConditionMap != null && !requestConditionMap.isEmpty()) {
            Map<String, Object> extraContentCondition = new HashMap<>();
            Map<String, List<Object>> extraContentInCondition = new HashMap<>();
            for (Map.Entry<String, Object> entry : requestConditionMap.entrySet()) {
                if (entry.getValue() != null) {
                    if (entry.getValue() instanceof List) {
                        List<Object> conditionList = StringUtil.getListFromString(StringUtil.jsonEncode(entry.getValue()), Object.class);
                        if (!conditionList.isEmpty()) {
                            extraContentInCondition.put(entry.getKey(), conditionList);
                        }
                    } else {
                        extraContentCondition.put(entry.getKey(), entry.getValue());
                    }
                }
            }
            conditionMap.put("extraContentCondition", extraContentCondition);
            conditionMap.put("extraContentInCondition", extraContentInCondition);
        }
        return conditionMap;
    }


    public static Map<String, Object> createRequestConditionMap(Map<String, Object> conditionMap, Map<String, Object> requestConditionMap, String[] mainColumns) {
        if (requestConditionMap != null && !requestConditionMap.isEmpty()) {
            Map<String, Object> extraContentCondition = new HashMap<>();
            Map<String, List<Object>> extraContentInCondition = new HashMap<>();
            List<String> mainColumnList = Arrays.asList(mainColumns);
            for (Map.Entry<String, Object> entry : requestConditionMap.entrySet()) {
                if (entry.getValue() != null) {
                    if (mainColumnList.contains(entry.getKey())) {
                        conditionMap.put(entry.getKey(), entry.getValue());
                    } else {
                        if (entry.getValue() instanceof List) {
                            List<Object> conditionList = StringUtil.getListFromString(StringUtil.jsonEncode(entry.getValue()), Object.class);
                            if (!conditionList.isEmpty()) {
                                extraContentInCondition.put(entry.getKey(), conditionList);
                            }
                        } else {
                            extraContentCondition.put(entry.getKey(), entry.getValue());
                        }
                    }
                }
            }
            conditionMap.put("extraContentCondition", extraContentCondition);
            conditionMap.put("extraContentInCondition", extraContentInCondition);
        }
        return conditionMap;
    }

    /**
     * 构建查询SQL
     *
     * @param queryCondition 查询条件
     * @return 查询SQL
     */
    public static String buildQuerySql(QueryCondition queryCondition) {
        if (queryCondition == null || (StringUtil.isEmpty(queryCondition.getCo()) && StringUtil.isEmpty(queryCondition.getLo()) )) {
            return "";
        }
        StringBuilder sqlBuilder = new StringBuilder();
        if (queryCondition.getConditionList() != null && !queryCondition.getConditionList().isEmpty()) {
            String subConditions = queryCondition.getConditionList().stream().map(Common::buildQuerySql).collect(Collectors.joining(" " + queryCondition.getLo() + " "));
            sqlBuilder.append("(").append(subConditions).append(")");
        } else {
            sqlBuilder.append(" ").append(" (").append(parseSingleCondition(queryCondition)).append(")");
        }
        return sqlBuilder.toString();
    }

    /**
     * 解析单个扩展字段条件
     * TODO 此处需优化,目前只支持简单的 > < = >= <= like !=
     */
    private static String parseSingleCondition(QueryCondition queryCondition) {
        String key = queryCondition.getKey();
        String value = queryCondition.getValue().toString();
        String co = queryCondition.getCo();
        String[] keyArr = key.split("\\.");
        String sql = "";
        if (keyArr.length == 1) {
            // name
            String field = StringUtil.camelToUnderscore(keyArr[0]);
            String sqlFormat = "%s %s '%s'";
            return String.format(sqlFormat, field, co, value);
        } else if (keyArr.length == 2) {
            // extraContent.name
            String firstKey = StringUtil.camelToUnderscore(keyArr[0]);
            String field = keyArr[1];
            String sqlFormat = "%s ->> '$.%s' %s '%s'";
            return String.format(sqlFormat, firstKey, field, co, value);
        } else if (keyArr.length == 3) {
            // extraContent.extraContent.name
            String firstKey = StringUtil.camelToUnderscore(keyArr[0]);
            String secondKey = keyArr[1];
            String field = keyArr[2];
            String sqlFormat = " JSON_EXTRACT(%s, '$.%s') IS NOT NULL \n" +
                    "      AND JSON_EXTRACT(%s, '$.%s') != '' \n" +
                    "      AND JSON_UNQUOTE(JSON_EXTRACT(%s ->> '$.%s', '$.%s')) %s '%s'";
            return String.format(sqlFormat, firstKey, secondKey, firstKey, secondKey, firstKey, secondKey, field, co, value);
        } else {
            return sql;
        }
    }
}
