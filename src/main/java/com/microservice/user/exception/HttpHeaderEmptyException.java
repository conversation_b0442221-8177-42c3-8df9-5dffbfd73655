package com.microservice.user.exception;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HttpHeaderEmptyException extends RuntimeException {

    private String message;
    private String code;
    private Map<String, String> header;

    public HttpHeaderEmptyException(String message, Map<String, String> header) {
        this.message = message;
        this.header = header;
    }

}
