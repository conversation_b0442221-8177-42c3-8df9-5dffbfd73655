package com.microservice.user.exception;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TokenException extends RuntimeException {
    private String code;
    private String message;
    private String tokenStatus;

    public TokenException(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
