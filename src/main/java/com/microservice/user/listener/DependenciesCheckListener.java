package com.microservice.user.listener;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.web.client.RestTemplate;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/1 15:26
 **/
public class DependenciesCheckListener implements ApplicationListener<ApplicationEnvironmentPreparedEvent> {

    @Override
    public void onApplicationEvent(ApplicationEnvironmentPreparedEvent event) {
        // 从环境中获取数据库配置
        ConfigurableEnvironment env = event.getEnvironment();
        String url = env.getProperty("spring.datasource.url");
        String username = env.getProperty("spring.datasource.username");
        String password = env.getProperty("spring.datasource.password");
        if (url == null || username == null || password == null) {
            System.out.println("❌ 数据库配置缺失: 请检查 spring.datasource.url/username/password");
        }

        StringBuilder sb = new StringBuilder();
        sb.append("MySQL/");
        // 尝试建立MySQL连接
        try (Connection conn = DriverManager.getConnection(url, username, password)) {
            sb.append("1");
        } catch (SQLException e) {
            sb.append("0");
        }
        sb.append(",");
        sb.append("MS-LOG/");
        // 尝试建立日志微服务连接
        sb.append(connectionMsLog(env.getProperty("msLogDomain")));
        System.out.println("DEPENDENCIES: " + sb);

        // 如果之后还引入其他服务继续添加连接状态

        System.out.println("------------------");
    }

    private Integer connectionMsLog(String serverUrl) {

        RestTemplate restTemplate = new RestTemplate();
        String url = serverUrl + "ping";
        try {
            String s = restTemplate.postForObject(url, "ping", String.class);
            if (StrUtil.isBlank(s)) {
                return 0;
            } else {
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    Map<String, Object> map = objectMapper.readValue(s, new TypeReference<Map<String, Object>>() {
                    });
                    String code = (String) map.get("code");
                    Map<String, Object> data = (Map<String, Object>) map.get("data");

                    Object health = data.get("health");
                    int healthNum = 1;
                    if (health != null) {
                        healthNum = ((Number) health).intValue();
                    }
                    if ("20000".equals(code) && healthNum == 1) {
                        return 1;
                    }
                } catch (JsonProcessingException e) {
                    return 0;
                }
            }
        } catch (Exception e) {
            return 0;
        }
        return 0;
    }

}
