package com.microservice.user;

import com.microservice.user.config.SystemConfig;
import com.microservice.user.util.DatabaseHealthUtil;
import com.microservice.user.util.LogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;


import java.net.Inet4Address;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class ApplicationReadyEventListener implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private DatabaseHealthUtil databaseHealthUtil;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
// DEPENDENCIES
//        String checkHealthStatus = "DEPENDENCIES: MYSQL/" + databaseHealthUtil.checkDatabaseConnection()+ ",MS-LOG/" + LogUtil.ping();
        String checkHealthStatus = "Dependencies : MYSQL/" + "1"+ ",MS-LOG/" + LogUtil.ping();

        System.out.println("-----------项目初始化并启动完成----------");
        System.out.println("Name : " + systemConfig.getSpringApplicationName());
        System.out.println("Time : " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("Version : " + MsUserApplication.loadVersion() +"-RELEASES");
        try {
            System.out.println("Ip : " + Inet4Address.getLocalHost().getHostAddress());
        } catch (UnknownHostException e) {
            LogUtil.error("ip地址解析错误" + e.getMessage());
        }
        System.out.println("Port : " + systemConfig.getServerPort());
        System.out.println("AppChannelId : " + systemConfig.getAppChannelId());
        System.out.println("DataChannelId : " + systemConfig.getDataChannelId());
        System.out.println(checkHealthStatus);
        System.out.println("Health : MYSQL&&MS-LOG");
        System.out.println("------------------");
    }
}