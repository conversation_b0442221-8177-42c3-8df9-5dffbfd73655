package com.microservice.user.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import com.microservice.user.common.Common;
import com.mssdk.log.Log;
import com.mssdk.log.entity.LogEntity;

import java.util.UUID;
import java.util.concurrent.ExecutorService;

public class LogUtil {

    private static final ExecutorService executorService = (ExecutorService) BeanUtil.getBean("ttlExecutorService");

    private static Log getMsLogInstance() {
        return new Log(ConfigUtil.getSystemConfig().getMsLogDomain(), ConfigUtil.getSystemConfig().getAppChannelId(), ConfigUtil.getSystemConfig().getDataChannelId(), ConfigUtil.getSystemConfig().getApiKey(), false, Common.getTraceId());
    }

    public static void error(String content) {
        if (ConfigUtil.getSystemConfig().getLogLevel() <= 4) {
            executorService.submit(() -> getMsLogInstance().error(content));
        }
    }

    public static void error(LogEntity logEntity) {
        if (ConfigUtil.getSystemConfig().getLogLevel() <= 4) {
            executorService.submit(() -> getMsLogInstance().error(logEntity));
        }
    }

    public static void warn(String content) {
        if (ConfigUtil.getSystemConfig().getLogLevel() <= 3) {
            executorService.submit(() -> getMsLogInstance().warn(content));
        }
    }

    public static void warn(LogEntity logEntity) {
        if (ConfigUtil.getSystemConfig().getLogLevel() <= 3) {
            executorService.submit(() -> getMsLogInstance().warn(logEntity));
        }
    }

    public static void info(String content) {
        if (ConfigUtil.getSystemConfig().getLogLevel() <= 2) {
            executorService.submit(() -> getMsLogInstance().info(content));
        }
    }

    public static void info(LogEntity logEntity) {
        if (ConfigUtil.getSystemConfig().getLogLevel() <= 2) {
            executorService.submit(() -> getMsLogInstance().info(logEntity));
        }
    }

    public static void debug(String content) {
        if (ConfigUtil.getSystemConfig().getLogLevel() <= 1) {
            executorService.submit(() -> getMsLogInstance().debug(content));
        }
    }

    public static void debug(LogEntity logEntity) {
        if (ConfigUtil.getSystemConfig().getLogLevel() <= 1) {
            executorService.submit(() -> getMsLogInstance().debug(logEntity));
        }
    }

    public static void flush() {
        getMsLogInstance().flush();
    }

    /**
     * 调用ping接口
     */
    public static String ping() {

        String url = UrlUtil.createUrl(ConfigUtil.getSystemConfig().getMsLogDomain(), "/ping");
        String body = StringUtil.jsonEncode(new JSONObject());

        HttpRequest request  = HttpRequest.post(url)
                .header("x-app_channel_id", ConfigUtil.getSystemConfig().getAppChannelId())
                .header("x-data_channel_id", ConfigUtil.getSystemConfig().getDataChannelId())
                .header("x-trace_id", ConfigUtil.getSystemConfig().getApiKey())
                .header("x-api_key", UUID.randomUUID().toString())
                .header("Content-Type", "application/json;charset=UTF-8")
                .body(body)
                .timeout(2000); // 2秒

        int healthStatus = 0;
        try {
            // 发起POST请求
            HttpResponse response = request.execute();
            JSONObject jObject = new JSONObject(response.body());
            healthStatus = ObjectUtil.isNotEmpty(jObject.getJSONObject("data")) ? 1 : 0;
        } catch (Exception ignored) {
            return "0";
        }
        return healthStatus == 1 ? "1" : "0";
    }
}
