package com.microservice.user.util;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

@Component
public class BeanUtil implements ApplicationContextAware {

    protected static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext a) {
        if (applicationContext == null) {
            BeanUtil.applicationContext = a;
        }
    }

    public static Object getBean(String name) {
        return applicationContext.getBean(name);
    }

    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    @Bean
    public ExecutorService executorService() {
        return Executors.newFixedThreadPool(4);
    }

    @Bean(name = "singleThreadExecutor")
    public ExecutorService singleThreadExecutorService() {
        return Executors.newSingleThreadExecutor();
    }

    @Bean("ttlExecutorService")
    public ExecutorService ttlExecutorService() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(50,
                500,
                10,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(1000)));
    }

    @Bean(name = "redisTemplate")
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, String> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        RedisSerializer stringSerializer = new StringRedisSerializer();
        redisTemplate.setDefaultSerializer(stringSerializer);
        return redisTemplate;
    }
}

