package com.microservice.user.util;

import com.microservice.user.config.ApplicationConfig;
import com.microservice.user.config.SystemConfig;
import com.mssdk.configuration.Config;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ConfigUtil {

    private static ApplicationConfig applicationConfig;
    @Getter
    private static SystemConfig systemConfig;


    public ConfigUtil(ApplicationConfig applicationConfig, SystemConfig systemConfig) {
        ConfigUtil.applicationConfig = applicationConfig;
        ConfigUtil.systemConfig = systemConfig;
    }

    private static Config getMsConfigInstance(String msConfigDomain, String appChannelId, String dataChannelId) {
        return new Config(msConfigDomain, appChannelId, dataChannelId, "", "");
    }

    public static Map<String, Object> getConfig(String msConfigDomain, String appChannelId, String dataChannelId) {
        Map<String, Object> configMap = getMsConfigInstance(msConfigDomain, appChannelId, dataChannelId).getConfig(appChannelId);
        return StringUtil.getMapFromString(StringUtil.jsonEncode(configMap.get("config")));
    }

    public static ApplicationConfig getApplicationConfig() {
        return applicationConfig;
    }

    public static void setApplicationConfig(Map<String, Object> applicationConfigMap) {
        applicationConfig = StringUtil.jsonDecode(StringUtil.jsonEncode(applicationConfigMap), ApplicationConfig.class);
    }

    public static void setSystemConfig(Map<String, Object> systemConfigMap) {
        systemConfig = StringUtil.jsonDecode(StringUtil.jsonEncode(systemConfigMap), SystemConfig.class);
    }

    public static Map<String, Object> updateConfig(String msConfigDomains, String appChannelId, String dataChannelId) {
        String msConfigDomain = msConfigDomains;
        if (msConfigDomains.contains(",")) {
            String[] msConfigDomainArr = msConfigDomains.split(",");
            msConfigDomain = msConfigDomainArr[0];
        }
        Map<String, Object> configMap = ConfigUtil.getConfig(msConfigDomain, appChannelId, dataChannelId);
        //系统配置
        Map<String, Object> systemConfigMap = StringUtil.getMapFromString(StringUtil.jsonEncode(configMap.get("system")));
        //配置文件中的配置项覆盖系统配置微服务配置项
        systemConfigMap.put("appChannelId", appChannelId);
        systemConfigMap.put("dataChannelId", dataChannelId);
        systemConfigMap.put("msConfigDomain", msConfigDomains);
        ConfigUtil.setSystemConfig(systemConfigMap);
        //返回配置对象
        return configMap;
    }
}
