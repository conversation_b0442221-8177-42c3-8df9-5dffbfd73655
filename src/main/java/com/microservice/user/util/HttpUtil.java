package com.microservice.user.util;

import com.microservice.user.common.Common;
import com.mssdk.log.entity.LogEntity;
import okhttp3.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class HttpUtil {

    public static String post(String url, RequestBody requestBody, Map<String, String> header) {
        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(header))
                .post(requestBody)
                .build();
        LogUtil.info(LogEntity.builder()
                .bizParams(StringUtil.jsonEncode(header))
                .bizModule("server-header")
                .bizOper(url)
                .traceId(Common.getTraceId())
                .build());
        LogUtil.info(LogEntity.builder()
                .bizParams(StringUtil.jsonEncode(requestBody))
                .bizModule("server-request")
                .bizOper(url)
                .traceId(Common.getTraceId())
                .build());
        String response = handlerResponse(request);
        LogUtil.info(LogEntity.builder()
                .rspResult(StringUtil.jsonEncode(response))
                .bizModule("server-response")
                .bizOper(url)
                .traceId(Common.getTraceId())
                .build());
        return response;
    }

    public static String post(String url, String requestBody, Map<String, String> header) {
        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(header))
                .post(RequestBody.create(requestBody, MediaType.parse("application/json;charset=UTF-8")))
                .build();
        LogUtil.info(LogEntity.builder()
                .bizParams(StringUtil.jsonEncode(header))
                .bizModule("server-header")
                .bizOper(url)
                .traceId(Common.getTraceId())
                .build());
        LogUtil.info(LogEntity.builder()
                .bizParams(StringUtil.jsonEncode(requestBody))
                .bizModule("server-request")
                .bizOper(url)
                .traceId(Common.getTraceId())
                .build());
        String response = handlerResponse(request);
        LogUtil.info(LogEntity.builder()
                .rspResult(StringUtil.jsonEncode(response))
                .bizModule("server-response")
                .bizOper(url)
                .traceId(Common.getTraceId())
                .build());
        return response;
    }

    private static String handlerResponse(Request request) {
        OkHttpClient okHttpClient = new OkHttpClient();
        try (Response response = okHttpClient.newCall(request).execute()) {
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                return responseBody.string();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public static Map<String, String> getHeader(HttpServletRequest request) {
        Map<String, String> headerMap = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement().toLowerCase();
            headerMap.put(name, request.getHeader(name));
        }
        return headerMap;
    }

}
