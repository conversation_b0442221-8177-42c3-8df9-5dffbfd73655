package com.microservice.user.util;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class RedisUtil {

    private static RedisTemplate<String, String> redisTemplate;

    private static RedisTemplate<String, String> getRedisTemplate() {
        if (redisTemplate == null) {
            redisTemplate = (RedisTemplate<String, String>) BeanUtil.getBean("redisTemplate");
        }
        return redisTemplate;
    }

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public static String get(String key) {
        return key == null ? null : getRedisTemplate().opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean set(String key, String value) {
        try {
            getRedisTemplate().opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean set(String key, String value, long expire) {
        try {
            getRedisTemplate().opsForValue().set(key, value, expire, TimeUnit.SECONDS);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static Long del(List<String> keyList) {
        return getRedisTemplate().delete(keyList);
    }

    public static Boolean del(String key) {
        return getRedisTemplate().delete(key);
    }

    public static Set<String> keys(String pattern) {
        return getRedisTemplate().keys(pattern);
    }

    public static List<String> mGet(List<String> keyList) {
        return getRedisTemplate().opsForValue().multiGet(keyList);
    }
}
