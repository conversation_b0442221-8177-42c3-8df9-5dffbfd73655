package com.microservice.user.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TimeUtil {

    public static String getNowDateTime() {
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return f.format(new Date());
    }

    public static String getNowDateTime(String format) {
        SimpleDateFormat f = new SimpleDateFormat(format);
        return f.format(new Date());
    }

    public static String date2String(Date date) {
        if (date == null) return "";
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return f.format(date);
    }

    public static String date2String(Date date, String format) {
        SimpleDateFormat f = new SimpleDateFormat(format);
        return f.format(date);
    }

    public static Date string2Date(String dateTime) {
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return string2Date(dateTime, f);
    }

    public static Date string2Date(String dateTime, String format) {
        SimpleDateFormat f = new SimpleDateFormat(format);
        return string2Date(dateTime, f);
    }

    public static Date string2Date(String dateTime, SimpleDateFormat f) {
        Date d = null;
        try {
            d = f.parse(dateTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return d;
    }

    public static Date addDay(Date d, int dayCount) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.DATE, dayCount);
        return c.getTime();
    }

    public static Date addMinute(Date d, int minuteCount) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.MINUTE, minuteCount);
        return c.getTime();
    }
}
