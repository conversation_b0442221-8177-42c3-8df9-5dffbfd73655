package com.microservice.user.util;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONObject;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {

    public static String createUuid(String identityId) {
        if (isEmpty(identityId)) {
            return createUuid();
        }
        if (identityId.length() == 36) {
            return identityId;
        }
        String md5Str = DigestUtils.md5DigestAsHex(identityId.getBytes());
        return md5Str.substring(0, 8) + "-"
                + md5Str.substring(8, 12) + "-"
                + md5Str.substring(12, 16) + "-"
                + md5Str.substring(16, 20) + "-"
                + md5Str.substring(20, 32);
    }

    public static String createUuid() {
        return UUID.randomUUID().toString();
    }

    public static boolean isEmpty(String s) {
        return s == null || s.isEmpty();
    }


    public static <T> String jsonEncode(T obj) {
        ObjectMapper objectMapper = new ObjectMapper();
        String s = "";
        try {
            s = objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return s;
    }

    public static <T> T jsonDecode(String jsonString, Class<T> c) {
        if (jsonString == null) {
            return null;
        }
        T o = null;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            o = objectMapper.readValue(jsonString, c);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return o;
    }

    public static Map<String, Object> getMapFromString(String jsonString) {
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            return jsonObject.toMap();
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    public static <T> List<T> getListFromString(String paramStr, Class<T> clazz) {
        ObjectMapper objectMapper = new ObjectMapper();
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, clazz);
        List<T> list = new ArrayList<>();
        try {
            list = objectMapper.readValue(paramStr, javaType);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return list;
    }

    public static String randomNumber(int length) {
        StringBuilder s = new StringBuilder();
        for (int i = 0; i < length; i++) {
            s.append((int) (Math.random() * 10));
        }
        return s.toString();
    }

    public static String randomString(int length) {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = (int) (str.length() * Math.random());
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    public static String createRandomString(int length) {
        char[][] chars = {
                {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'},
                {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'},
                {'1', '2', '3', '4', '5', '6', '7', '8', '9'}
        };
        StringBuilder sb = new StringBuilder();
        int i = (int) (Math.random() * (chars.length - 1));
        while (sb.toString().length() < length) {
            int arrayIndex = i % chars.length;
            char[] charArr = chars[arrayIndex];
            int charIndex = (int) (Math.random() * (charArr.length - 1));
            sb.append(charArr[charIndex]);
            i++;
        }
        return sb.toString();
    }

    public static String md5(String s) {
        return DigestUtils.md5DigestAsHex(s.getBytes(StandardCharsets.UTF_8));
    }

    public static String myMd5(String s) {
        return md5(md5(s) + "%petrol+2021*");
    }

    public static boolean checkUuid(String uuid) {
        //[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}
        //匹配变量
        String p = "[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}";
        Pattern r = Pattern.compile(p);
        Matcher m = r.matcher(uuid);
        if (m.matches()) {
            return true;
        }
        return false;
    }

    /**
     * 驼峰转下划线
     *
     * @param camelCase 驼峰字符串。例companyName
     * @return 下划线字符串。例company_name
     */
    public static String camelToUnderscore(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }


    public static String underscoreToCamel(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        StringBuilder result = new StringBuilder();
        String[] parts = input.split("_");
        for (int i = 0; i < parts.length; i++) {
            if (i == 0) {
                result.append(parts[i].toLowerCase()); // 首字母小写，其余部分首字母大写
            } else {
                result.append(capitalize(parts[i])); // 其余部分首字母大写
            }
        }
        return result.toString();
    }

    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

}
