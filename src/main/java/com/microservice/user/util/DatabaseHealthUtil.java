package com.microservice.user.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
public class DatabaseHealthUtil {
    /**
     *  @Autowired
     *     private JdbcTemplate jdbcTemplate;
     */

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public String checkDatabaseConnection() {
        try {
            // 执行简单查询验证连接
            jdbcTemplate.queryForObject("SELECT 1", Integer.class);
            return "1";
        } catch (Exception e) {
            return "0";
        }
    }


}
