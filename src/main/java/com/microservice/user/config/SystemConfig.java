package com.microservice.user.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SystemConfig {

    @Value("${server.appChannelId}")
    private String appChannelId;

    @Value("${server.dataChannelId}")
    private String dataChannelId;

    @Value("${server.apiKey}")
    private String apiKey;

    @Value("${msLogDomain}")
    private String msLogDomain;

    @Value("${msConfigDomain}")
    private String msConfigDomain;

    @Value("${logLevel}")
    private int logLevel;

    @Value("${spring.application.name}")
    private String springApplicationName;

    @Value("${server.port}")
    private String serverPort;
}
