package com.microservice.user.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationConfig {
    @Value("${server.appChannelId}")
    private String appChannelId;
    @Value("${server.dataChannelId}")
    private String dataChannelId;
    @Value("${msConfigDomain}")
    private String msConfigDomain;
}
