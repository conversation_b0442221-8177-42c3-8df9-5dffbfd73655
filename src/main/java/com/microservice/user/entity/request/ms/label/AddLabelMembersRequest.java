package com.microservice.user.entity.request.ms.label;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
public class AddLabelMembersRequest extends MsBaseRequest {

    /**
     * 标签id
     */
    @ApiModelProperty(value = "标签id")
    private String labelId;

    @ApiModelProperty(value = "标签id列表")
    private List<String> labelIdList;


    /**
     * 部门id
     */
    private List<String> departmentId;

    /**
     * 身份id
     */
    private List<String> identityId;

    /**
     * 公司ID
     */
    private List<String> companyId;

    /**
     * 默认管理范围
     */
    private String manageCompanyId;

    /**
     * 扩展信息
     */
    private String extraContent;
}
