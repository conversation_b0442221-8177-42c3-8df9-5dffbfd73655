package com.microservice.user.entity.request.ms.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/12 16:01
 **/
@Data
@ApiModel("获取公司树，带人数")
public class GetCompanyTreeWithPeopleNumRequest implements Serializable {

    private final static long serialVersionUID = 1L;

    @NotBlank(message = "公司ID不能为空")
    @ApiModelProperty("公司ID")
    private String companyId;
}