package com.microservice.user.entity.request.ms.group;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeleteGroupRequest extends MsBaseRequest {
    @NotEmpty(message = "用户组id错误")
    private String groupId;
    private int isForce;
}
