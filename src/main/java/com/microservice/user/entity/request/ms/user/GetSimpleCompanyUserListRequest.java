package com.microservice.user.entity.request.ms.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsGetListRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * 查询简单公司用户列表入参
 *
 * <AUTHOR>
 * @date 2024/12/11 14:50
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("查询简单公司用户列表入参")
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetSimpleCompanyUserListRequest extends MsGetListRequest {

    @NotBlank(message = "公司ID不能为空")
    @ApiModelProperty("公司ID")
    private String companyId;

    @ApiModelProperty("部门ID")
    private String departmentId;

    @ApiModelProperty("账号")
    private String userName;
}
