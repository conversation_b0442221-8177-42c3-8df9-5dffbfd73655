package com.microservice.user.entity.request.ms.group;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CopyGroupRequest extends MsBaseRequest {
    @NotEmpty(message = "旧渠道id错误")
    private String fromAppChannelId;
    @NotEmpty(message = "新渠道id错误")
    private String toAppChannelId;
    private List<String> groupIdList;
}
