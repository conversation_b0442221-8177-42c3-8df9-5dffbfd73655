package com.microservice.user.entity.request.ms.position;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddPositionRequest extends MsBaseRequest {
    private String parentId;
    private String companyId;
    private String departmentId;
    @NotEmpty(message = "职位名称错误")
    private String positionName;
    private Map<String, Object> extraContent;
    private Integer sort;
}
