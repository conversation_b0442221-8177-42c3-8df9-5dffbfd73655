package com.microservice.user.entity.request.ms.friend;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddFriendRequest extends MsBaseRequest {
    @NotEmpty(message = "用户身份id错误")
    private String identityId;
    @Size(min = 1, message = "目标用户身份id错误")
    private List<String> targetIdentityIdList;
    private Map<String, Object> extraContent;
}
