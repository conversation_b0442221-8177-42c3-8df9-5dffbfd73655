package com.microservice.user.entity.request.ms.rosterconfig;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateRosterTeamRequest {

    /**
     * 子集主键id
     */
    @NotBlank(message = "子集id不能为空")
    private String rosterTeamId;

    /**
     * 子集名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 显示顺序
     */
    @Min(value = 0,message = "显示顺序参数错误")
    private Integer sort;

    /**
     * 子集下分组限制 默认为6
     */
    @Min(value = 0,message = "子集下分组限制参数错误")
    private Integer groupCountMax;

    /**
     *  是否展示 0否 1是
     */
    @Min(value = 0,message = "显示数据参数错误")
    @Max(value = 1,message = "显示数据参数错误")
    private Integer isShow;
}
