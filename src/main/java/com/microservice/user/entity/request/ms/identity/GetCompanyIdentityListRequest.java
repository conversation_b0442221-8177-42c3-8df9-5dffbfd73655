package com.microservice.user.entity.request.ms.identity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dto.ms.IdentityDTO;
import com.microservice.user.entity.request.ms.MsGetListRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCompanyIdentityListRequest extends MsGetListRequest {
    private String companyId;
    private List<String> companyIdList;
    private String departmentId;
    private List<String> departmentIdList;
    private String positionId;
    private List<String> positionIdList;
    private Map<String, Object> condition;
    private IdentityKeyFilter identityKeyFilter;
    private int isSimple;
}
