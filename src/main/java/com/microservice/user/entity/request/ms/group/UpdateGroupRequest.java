package com.microservice.user.entity.request.ms.group;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateGroupRequest extends MsBaseRequest {
    @NotEmpty(message = "用户组id错误")
    private String groupId;
    private String groupName;
    private Map<String, Object> extraContent;
    private List<String> eventIdList;
    private List<String> pageIdList;
    private List<String> dataList;
    private List<String> userIdList;
    private Integer sort;
}
