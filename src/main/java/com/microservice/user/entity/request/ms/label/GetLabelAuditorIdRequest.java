package com.microservice.user.entity.request.ms.label;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
public class GetLabelAuditorIdRequest extends MsBaseRequest {


    /**
     * 标签id
     */
    @ApiModelProperty(value = "标签id")
    private String labelId;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签id")
    private List<String> labelIdList;


    /**
     * 提交审批申请人id
     */
    @NotEmpty(message = "提交审批申请人id不能为空")
    @ApiModelProperty(value = "提交审批申请人id", required = true)
    private String userId;

    @ApiModelProperty(value = "是否包含子标签 0否 1是")
    private Integer isChild;


    /**
     * 公司ID
     */
    @NotEmpty(message = "公司ID不能为空")
    @ApiModelProperty(value = "公司ID", required = true)
    private String companyId;
}
