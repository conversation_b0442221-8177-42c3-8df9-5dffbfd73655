package com.microservice.user.entity.request.ms.rosterconfig;

import com.microservice.user.entity.request.ms.MsGetListRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetRosterGroupRequest extends MsGetListRequest {

    /**
     * 分组主键id
     */
    private List<String> rosterGroupIds;

    /**
     * 子集id查询条件
     */
    private List<String> rosterTramIds;

    /**
     * field键
     */
    private List<String> fields;

    /**
     * 子集名称查询
     */
    private String name;

    /**
     * 是否显示插叙条件[0否、1是]
     */
    private Integer isShow;

    /**
     * 是否系统内置 0否 1是  初始基本数据时使用
     */
    private Integer isSystem;

    /**
     * 时间查询条件 开始时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String startDateTime;

    /**
     * 时间查询条件 结束时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String endDateTime;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 是否删除  0否 1是
     */
    private Integer isDelete;

    /**
     * 是否关联数据 [0否 1是] 将该子集下的分组关联出来默认为否
     */
    @Min(value = 0,message = "示范关联数据参数错误")
    @Max(value = 1,message = "示范关联数据参数错误")
    private Integer isRelatedData;

}
