package com.microservice.user.entity.request.ms.token;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsGetListRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetTokenListRequest extends MsGetListRequest {
    private String userId;
    private String identityId;
    private String token;
    private String tokenId;
    private String ip;
    private String tokenStatus;
}
