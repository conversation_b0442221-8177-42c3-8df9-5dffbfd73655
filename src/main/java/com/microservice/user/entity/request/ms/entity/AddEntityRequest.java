package com.microservice.user.entity.request.ms.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddEntityRequest extends MsBaseRequest {
    @NotEmpty(message = "标签名称错误")
    private String entityName;
    @Min(value = 1, message = "标签类别错误")
    private Integer type;
    private Integer sort;
    private Map<String, Object> extraContent;
}
