package com.microservice.user.entity.request.ms.identity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import com.microservice.user.entity.request.ms.item.RelationItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateIdentityRelationRequest extends MsBaseRequest {
    @NotEmpty(message = "身份id错误")
    private String identityId;
    @Size(min = 1, message = "关联关系错误")
    private List<RelationItem> relationList;
}
