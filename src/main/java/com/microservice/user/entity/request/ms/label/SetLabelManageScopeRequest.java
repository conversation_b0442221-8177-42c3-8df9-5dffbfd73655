package com.microservice.user.entity.request.ms.label;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
public class SetLabelManageScopeRequest extends MsBaseRequest {


    /**
     * 标签id
     */
    @NotEmpty(message = "标签id不能为空")
    @ApiModelProperty(value = "标签id", required = true)
    private String labelId;

    /**
     * 标签id
     */
    @NotNull(message = "成员与标签关系表id不能为空")
    @ApiModelProperty(value = "成员与标签关系表id", required = true)
    private Integer labelRelationId;


    /**
     * 部门id
     */
    private List<String> departmentId;

    /**
     * 身份id
     */
    private List<String> identityId;

    /**
     * 公司ID
     */
    private List<String> companyId;

    /**
     * 扩展信息
     */
    private String extraContent;
}
