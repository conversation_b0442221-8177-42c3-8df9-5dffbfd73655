package com.microservice.user.entity.request.ms.rosterconfig;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaveBatchRosterTeamRequest {

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 应用渠道号
     */
    private String appChannelId;

    /**
     * 批量数据
     */
    private List<@Valid AddRosterTeamRequest> rosterTeamRequestList;
}
