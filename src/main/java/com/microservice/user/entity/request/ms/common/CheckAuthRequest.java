package com.microservice.user.entity.request.ms.common;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckAuthRequest extends MsBaseRequest {
    private String eventId;//要验证的事件id
    private String pageId;//要验证的页面id
    private String data;//要验证的数据
    @NotEmpty(message = "身份id错误")
    private String identityId;
}
