package com.microservice.user.entity.request.ms.rosterconfig;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class AddRosterTeamRequest {

    /**
     * field键
     * 业务系统初始基本数据时使用一般添加系统自动生成
     */
    private String field;

    /**
     * 子集名称
     */
    @NotBlank(message = "子集名称不能为空")
    private String name;

    /**
     * 显示顺序
     */
    @Min(value = 0,message = "显示顺序参数错误，不能为负数")
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子集下分组限制
     */
    @Min(value = 0,message = "子集下分组个数限制参数错误，不能为负数")
    private Integer groupCountMax;

    /**
     *  是否展示 0否 1是
     */
    @Min(value = 0,message = "显示数据不正确")
    @Max(value = 1,message = "显示数值不正确")
    private Integer isShow;

    /**
     * 是否系统内置 0否 1是
     * 业务系统初始化数据时使用
     */
    @Min(value = 0,message = "系统参数不正确")
    @Max(value = 1,message = "系统参数不正确")
    private Integer isSystem;

}
