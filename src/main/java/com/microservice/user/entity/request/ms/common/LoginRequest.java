package com.microservice.user.entity.request.ms.common;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoginRequest extends MsBaseRequest {
    @NotEmpty(message = "用户名错误")
    private String userName;
    private String password;
    private int isCheckPassword = 1;//是否验证密码 1是 0否
    private int isCreateUser = 0;
}
