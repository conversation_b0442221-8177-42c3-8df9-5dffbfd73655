package com.microservice.user.entity.request.ms.label;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class AddLabelRequest extends MsBaseRequest {

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 标签名称
     */
    @NotEmpty(message = "标签名称不能为空")
    private String labelName;

    /**
     * 父标签id
     */
    private String parentId;

    /**
     * 创建人id
     */
    private String createUccId;
}
