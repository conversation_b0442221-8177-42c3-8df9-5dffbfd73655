package com.microservice.user.entity.request.ms.rosterconfig;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RosterDictRequest {

    /**
     * 子集id
     */
    private String rosterRuleTeamId;

    /**
     * 分组id
     */
    private String rosterRuleGroupId;

    /**
     * 字段标识
     */
    private String rosterRuleFieldId;

    /**
     * 数据字典名称
     */
    private String name;

    /**
     * 名称对应的枚举值
     */
    private String value;

}
