package com.microservice.user.entity.request.ms.identity;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class DeleteIdentityListRequest extends MsBaseRequest {
    @Size(min = 1, message = "身份id错误")
    private List<String> identityIdList;
}
