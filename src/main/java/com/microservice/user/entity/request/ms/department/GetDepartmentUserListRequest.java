package com.microservice.user.entity.request.ms.department;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsGetListRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetDepartmentUserListRequest extends MsGetListRequest {
    private String departmentId;
    private List<String> departmentIdList;
    private int isGetChildren;
    private int isSimple;
    private Map<String, Object> condition;   //扩展字段搜索
}

