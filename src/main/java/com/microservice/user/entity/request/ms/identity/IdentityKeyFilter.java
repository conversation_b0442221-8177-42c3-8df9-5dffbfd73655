package com.microservice.user.entity.request.ms.identity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdentityKeyFilter {
    private List<String> extraContentKeyList;
    private int isGetCompany = 1;
    private int isGetUser = 1;
    private int isGetEventIdList = 1;
    private int isGetGroupList = 1;
    private int isGetToken = 1;
    private int isGetIgrList = 1;
    private int isGetPositionList = 1;
    private int isGetDepartmentList = 1;
}
