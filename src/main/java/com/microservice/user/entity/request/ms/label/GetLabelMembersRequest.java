package com.microservice.user.entity.request.ms.label;

import com.microservice.user.entity.condition.QueryCondition;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import com.microservice.user.entity.request.ms.MsGetListRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetLabelMembersRequest extends MsGetListRequest {

    @ApiModelProperty(value = "标签Id")
    private String labelId;

    private List<String> labelIdList;

    @ApiModelProperty(value = "是否包含子标签 0否 1是")
    private Integer isChild;

    private String condition;
}
