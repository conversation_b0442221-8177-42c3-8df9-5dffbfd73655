package com.microservice.user.entity.request.ms.chatGroup;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateChatGroupRequest extends MsBaseRequest {
    @NotEmpty(message = "群聊id错误")
    private String chatGroupId;
    private List<String> identityIdList;
    private String chatGroupName;
    private Map<String, Object> extraContent;
}
