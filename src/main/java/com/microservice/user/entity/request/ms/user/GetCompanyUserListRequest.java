package com.microservice.user.entity.request.ms.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsGetListRequest;
import com.microservice.user.entity.request.ms.identity.IdentityKeyFilter;
import com.mssdk.user.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCompanyUserListRequest extends MsGetListRequest {
    private String companyId;
    private String departmentId;
    private String positionId;
    private String userName;
    private int isSimple;//简单实体，只返回主表信息
    private UserKeyFilter userKeyFilter;
    private List<String> companyIdList;
    private List<String> departmentIdList;
}
