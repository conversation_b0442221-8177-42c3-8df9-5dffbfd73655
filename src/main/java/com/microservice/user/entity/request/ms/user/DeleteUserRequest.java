package com.microservice.user.entity.request.ms.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeleteUserRequest extends MsBaseRequest {
    //    private String userId;
    @NotEmpty(message = "身份id错误")
    private String identityId;
    /**
     * 是否强制删除用户。1是 0否
     * 如果强制删除用户则会删除用户下的所有身份信息
     */
    private int isForce;
}
