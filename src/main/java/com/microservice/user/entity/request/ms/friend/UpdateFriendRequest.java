package com.microservice.user.entity.request.ms.friend;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateFriendRequest extends MsBaseRequest {
    @NotEmpty(message = "用户身份id错误")
    private String identityId;
    @NotEmpty(message = "目标用户身份id错误")
    private String targetIdentityId;
    private Map<String, Object> extraContent;
}
