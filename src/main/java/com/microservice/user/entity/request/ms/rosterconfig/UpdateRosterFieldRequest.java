package com.microservice.user.entity.request.ms.rosterconfig;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateRosterFieldRequest {

    /**
     * 字段主键id
     */
    @NotBlank(message = "主键id不能为空")
    private String rosterFieldId;

    /**
     * 字段名称
     */
    private String name;

    /**
     * 字段类型 [0单行文本框 1单行数字框 2多行文本框 3单行下拉框 4多选下拉框 5日期选择框 6时间选择器 7上传 8单选框]
     */
    @Min(value = 0,message = "字段类型错误")
    @Max(value = 8,message = "字段类型错误")
    private Integer type;

    /**
     * 排序字段
     */
    @Min(value = 0,message = "字段排序参数错误")
    private Integer sort;

    /**
     * 是否非空 [0 否/可以为空 ,1 是/不可以为空]
     */
    @Min(value = 0,message = "是否非空字段参数错误")
    @Max(value = 1,message = "是否非空字段参数错误")
    private Integer isNonNull;

    /**
     * 是否展示[0 否,1是]
     */
    @Min(value = 0,message = "显示参数错误")
    @Max(value = 1,message = "显示参数错误")
    private Integer isShow;

    /**
     * 数据长的 如果是字符串类型展示限制字符串的长度
     */
    @Min(value = 0,message = "字段长度参数错误")
    private Integer length;

    /**
     * 字段的数据字典 rg:字段类型为 "合同类型"(单行下拉框) 数据字点列表为 "劳动合同 ---> 0"、"劳务合同 ----> 1"等
     */
    private List<RosterDictRequest> dictList;
}
