package com.microservice.user.entity.request.ms.rosterconfig;

import com.microservice.user.entity.request.ms.MsGetListRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetRosterFieldRequest extends MsGetListRequest {

    /**
     * 字段主键查询
     */
    private List<String> rosterFields;

    /**
     * 字段参数id集合查询条件
     */
    private List<String> fields;

    /**
     * 规则分组id集合查询条件
     */
    private List<String> rosterGroupIds;

    /**
     * 子集id集合查询条件
     */
    private List<String> rosterTramIds;

    /**
     * 字段名称名称查询
     */
    private String name;

    /**
     * 是否显示插叙条件[0否、1是]
     */
    private Integer isShow;

    /**
     * 是否删除  0否 1是
     */
    private Integer isDelete;

    /**
     * 是否系统内置 0否 1是  初始基本数据时使用
     */
    private Integer isSystem;

    /**
     * 时间查询条件 开始时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String startDateTime;

    /**
     * 时间查询条件 结束时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String endDateTime;

    /**
     * 公司id
     */
    private String companyId;
}
