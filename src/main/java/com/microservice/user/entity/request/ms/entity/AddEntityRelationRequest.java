package com.microservice.user.entity.request.ms.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddEntityRelationRequest extends MsBaseRequest {
    @NotEmpty(message = "标签id错误")
    private String entityId;
    @Size(min = 1, message = "目标标签id错误")
    private List<String> targetEntityIdList;
    private Map<String, Object> extraContent;
}
