package com.microservice.user.entity.request.ms.identity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import com.microservice.user.entity.request.ms.item.RelationItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddIdentityRequest extends MsBaseRequest {
    private String identityId;
    private String identityName;
    private List<RelationItem> relationList;
    private Map<String, Object> extraContent;
    private String userId;
    private String newIdentityId;//新身份的身份id，不传自动生成
}
