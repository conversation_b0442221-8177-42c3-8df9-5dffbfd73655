package com.microservice.user.entity.request.ms.department;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateDepartmentRequest extends MsBaseRequest {
    @NotEmpty(message = "部门id错误")
    private String departmentId;
    private String departmentName;
    private Map<String, Object> extraContent;
    private String parentId;
    private Integer sort;
}
