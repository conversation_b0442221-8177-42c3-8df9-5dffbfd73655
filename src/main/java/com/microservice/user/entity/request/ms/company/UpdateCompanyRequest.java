package com.microservice.user.entity.request.ms.company;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateCompanyRequest extends MsBaseRequest {
    @NotEmpty(message = "公司id错误")
    private String companyId;
    private String parentId;
    private String companyName;
    private Map<String, Object> extraContent;
}
