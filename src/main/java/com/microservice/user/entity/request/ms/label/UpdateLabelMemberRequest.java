package com.microservice.user.entity.request.ms.label;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class UpdateLabelMemberRequest extends MsBaseRequest {

    /**
     * 标签id
     */
    @NotNull(message = "标签关系表ID不能为空")
    @ApiModelProperty(value = "标签关系表ID", required = true)
    private Integer id;


    /**
     * 状态（1有效 0无效）
     */
    @ApiModelProperty(value = "状态（1有效 0无效）")
    private Integer status;
}
