package com.microservice.user.entity.request.ms.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsGetListRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetEntityListRequest extends MsGetListRequest {
    private Map<String, Object> condition;
    private int isShowAll;
}
