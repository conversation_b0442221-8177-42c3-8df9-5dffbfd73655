package com.microservice.user.entity.request.ms.label;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class UpdateLabelRequest extends MsBaseRequest {

    /**
     * 标签id
     */
    @NotEmpty(message = "标签ID不能为空")
    private String id;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 状态（1有效 0无效）
     */
    private Integer status;
}
