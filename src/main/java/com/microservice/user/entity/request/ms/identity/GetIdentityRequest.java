package com.microservice.user.entity.request.ms.identity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetIdentityRequest extends MsBaseRequest {
    @NotEmpty(message = "身份id错误")
    private String identityId;
    private String userName;
    /**
     * 单独仅仅获取身份信息和部门信息 其余字段都不要
     * 0: 不获取部门信息
     * 1: 获取部门信息
     */
    private Integer isGetDepartmentListSign = 0; // 是否获取部门列表
}
