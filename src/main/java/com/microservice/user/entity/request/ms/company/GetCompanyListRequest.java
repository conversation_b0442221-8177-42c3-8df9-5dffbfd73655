package com.microservice.user.entity.request.ms.company;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.condition.QueryCondition;
import com.microservice.user.entity.request.ms.MsGetListRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCompanyListRequest extends MsGetListRequest {
    private Map<String, Object> condition;
    public QueryCondition queryCondition;
    private int isShowAll;
    private int isSimple;
    private CompanyKeyFilter companyKeyFilter;
    private List<String> columnList;
}
