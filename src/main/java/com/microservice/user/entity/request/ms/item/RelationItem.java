package com.microservice.user.entity.request.ms.item;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RelationItem {
    private String relateType;
    private int itemType;
    private List<String> relateIdList;
    private Map<String, Object> extraContent;
}
