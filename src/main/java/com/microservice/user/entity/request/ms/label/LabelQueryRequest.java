package com.microservice.user.entity.request.ms.label;

import com.microservice.user.entity.request.ms.MsGetListRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelQueryRequest extends MsGetListRequest {

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 父级id
     */
    private String parentId;

    /**
     * 公司id
     */
    @NotEmpty(message = "公司id不能为空")
    private String companyId;

    /**
     * 标签id集合 不要和分页同时传
     */
    private List<String> labelIds;
}
