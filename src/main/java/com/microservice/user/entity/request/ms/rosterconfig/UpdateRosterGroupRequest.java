package com.microservice.user.entity.request.ms.rosterconfig;

import com.microservice.user.entity.request.ms.MsBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateRosterGroupRequest {

    /**
     * 分组id
     */
    @NotBlank(message = "分组id不能为空")
    private String rosterGroupId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 显示顺序
     */
    @Min(value = 0,message = "显示顺序参数错误")
    private Integer sort;

    /**
     *  是否展示 0否 1是
     */
    @Min(value = 0,message = "显示数据参数错误")
    @Max(value = 1,message = "显示数据参数错误")
    private Integer isShow;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 规则分组下规则上限
     */
    @Min(value = 0,message = "分组下字段个数限制参数错误，不能负数")
    private Integer fieldCountMax;
}
