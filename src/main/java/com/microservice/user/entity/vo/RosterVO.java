package com.microservice.user.entity.vo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.microservice.user.entity.dao.IdentityRosterDAO;
import com.microservice.user.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * &#064;DATE: 2024/9/9
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RosterVO {

    /**
     * 用户账号 (手机号)
     */
    private String userName;

    /**
     * 身份ID
     */
    private String identityId;

    /**
     * 身份扩展字段
     */
    private Map<String, Object> extraContent;

    /**
     * 入职时间
     */
    private String joinTime;

    /**
     * 员工状态:1在职、2离职
     */
    private Integer employeeStatus;

    /**
     * 员工类型： 1全职、2实习、3外派
     */
    private Integer employeeType;

    /**
     * 历史工龄
     */
    private String seniority;

    /**
     * 部门ID
     */
    private List<String> deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 职位
     */
    private String post;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 证件类型
     */
    private String idNumberType;

    /**
     * 证件正面
     */
    private String idPhotoFront;

    /**
     * 证件反面
     */
    private String idPhotoSide;

    /**
     * 出生时间
     */
    private String birthDate;

    /**
     * 生日类型
     */
    private String birthType;

    /**
     * 扩展字段
     */
    private Map<String, Object> extend;

    /**
     * 所属公司
     */
    private String companyId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    public static RosterVO create(IdentityRosterDAO identityRosterDAO) {
        return RosterVO.builder()
                .userName(identityRosterDAO.getUserName())
                .identityId(identityRosterDAO.getIdentityId())
                .extraContent(StringUtil.jsonDecode(identityRosterDAO.getExtraContent(),Map.class))
                .joinTime(identityRosterDAO.getJoinTime())
                .employeeStatus(identityRosterDAO.getEmployeeStatus())
                .employeeType(identityRosterDAO.getEmployeeType())
                .seniority(identityRosterDAO.getSeniority())
                .deptId(StrUtil.split(identityRosterDAO.getDeptId(), ","))
                .deptName(StrUtil.isNotEmpty(identityRosterDAO.getDeptName()) ? identityRosterDAO.getDeptName() : "")
                .post(identityRosterDAO.getPost())
                .jobNumber(identityRosterDAO.getJobNumber())
                .idNumber(identityRosterDAO.getIdNumber())
                .idNumberType(identityRosterDAO.getIdNumberType())
                .idPhotoFront(identityRosterDAO.getIdPhotoFront())
                .idPhotoSide(identityRosterDAO.getIdPhotoSide())
                .birthDate(DateUtil.formatLocalDateTime(identityRosterDAO.getBirthDate()))
                .birthType(identityRosterDAO.getBirthType())
                .extend(JSONUtil.parseObj(identityRosterDAO.getExtend()))
                .companyId(identityRosterDAO.getCompanyId())
                .createTime(DateUtil.formatLocalDateTime(identityRosterDAO.getCreateTime()))
                .updateTime(DateUtil.formatLocalDateTime(identityRosterDAO.getUpdateTime()))
                .build();
    }
}
