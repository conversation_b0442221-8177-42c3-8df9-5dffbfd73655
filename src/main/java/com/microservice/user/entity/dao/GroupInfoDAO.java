package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GroupInfoDAO {
    /**
     * 用户组id
     */
    private String groupId;

    /**
     * 用户组名
     */
    private String groupName;

    /**
     * 附加信息
     */
    private String extraContent;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 应用渠道号
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
