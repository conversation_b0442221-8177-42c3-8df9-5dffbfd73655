package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IGERWithIdentityDAO extends IdentityInfoDAO {
    /**
     * 用户组id
     */
    private String groupId;

    /**
     * 事件id
     */
    private String eventId;
}
