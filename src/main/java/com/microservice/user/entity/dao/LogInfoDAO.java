package com.microservice.user.entity.dao;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@Builder
public class LogInfoDAO {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 日志类型
     */
    private Integer type;

    /**
     * 操作类型
     */
    private String opt;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 扩展信息
     */
    private String extraContent;

    /**
     * 应用渠道号
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 创建时间
     */
    private Date createTime;

}
