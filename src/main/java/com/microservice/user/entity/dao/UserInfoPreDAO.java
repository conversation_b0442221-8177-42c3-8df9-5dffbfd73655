package com.microservice.user.entity.dao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfoPreDAO {
    /**
     * 预加入用户id
     */
    private String preUserId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 预加入用户信息
     */
    private String extraContent;

    /**
     * 用户来源渠道id
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
