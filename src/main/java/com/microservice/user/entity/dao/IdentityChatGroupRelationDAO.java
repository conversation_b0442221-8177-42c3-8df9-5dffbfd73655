package com.microservice.user.entity.dao;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class IdentityChatGroupRelationDAO {
    /**
     * 身份id
     */
    private String identityId;

    /**
     * 群聊id
     */
    private String chatGroupId;

    /**
     * 扩展信息
     */
    private String extraContent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
