package com.microservice.user.entity.dao;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 标签关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09 14:18:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelRelationDAO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 标签id
     */
    private String labelId;

    /**
     * 关联id
     */
    private String relationId;

    /**
     * 类型 (1部门 2员工[identity_info 主键])
     */
    private Integer relationType;

    /**
     * 扩展信息
     */
    private String extraContent;

    /**
     * 状态（1有效 0无效）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
