package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IdentityInfoDAO {
    /**
     * 用户身份id
     */
    public String identityId;

    /**
     * 用户身份名称
     */
    public String identityName;

    /**
     * 用户id
     */
    public String userId;

    /**
     * 保留域，不处理直接返回
     */
    public String extraContent;

    /**
     * 显示顺序
     */
    public Integer sort;

    /**
     * 用户来源渠道id
     */
    public String appChannelId;

    /**
     * 数据渠道号
     */
    public String dataChannelId;

    /**
     * 创建时间
     */
    public Date createTime;

    /**
     * 更新时间
     */
    public Date updateTime;
}
