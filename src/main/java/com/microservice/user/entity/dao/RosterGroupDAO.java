package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RosterGroupDAO {

    /**
     * 主键id
     */
    private String id;

    /**
     * field键
     */
    private String field;

    /**
     * 信息子集id
     */
    private String rosterRuleTeamId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 分组中最大字段限制
     */
    private Integer countMax;

    /**
     * 排序顺序
     */
    private Integer sort;

    /**
     * 是否显示 [0否 1是]
     */
    private Integer isShow;

    /**
     * 是否系统内置 0否 1是
     */
    private Integer isSystem;

    /**
     * 是否支持多组添加 0否 1是
     */
    private Integer isAdd;

    /**
     * 描述
     */
    private String remark;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 应用渠道号
     */
    private String appChannelId;

    /**
     * 数据渠道和
     */
    private String dataChannelId;

    /**
     * 是否删除 [0否 1是]
     */
    private Integer isDelete;
}
