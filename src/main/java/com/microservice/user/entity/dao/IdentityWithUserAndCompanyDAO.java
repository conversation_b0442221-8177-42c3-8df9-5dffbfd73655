package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdentityWithUserAndCompanyDAO extends IdentityInfoDAO {
    /**
     * 公司id
     */
    private String companyId;

    /**
     * 公司名
     */
    private String companyName;

    /**
     * 保留域，不处理直接返回
     */
    private String companyExtraContent;
    private String identityExtraContent;
    private String userExtraContent;
    private String userName;
}
