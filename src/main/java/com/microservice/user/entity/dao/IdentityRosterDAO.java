package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * &#064;DATE: 2024/9/5
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IdentityRosterDAO {

    /**
     * 用户账号 (手机号)
     */
    private String userName;

    /**
     * 身份ID
     */
    private String identityId;

    /**
     * 身份扩展字段
     */
    private String extraContent;

    /**
     * 入职时间
     */
    private String joinTime;

    /**
     * 员工状态:1在职、2离职
     */
    private Integer employeeStatus;

    /**
     * 员工类型： 1全职、2实习、3外派
     */
    private Integer employeeType;

    /**
     * 历史工龄
     */
    private String seniority;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 职位
     */
    private String post;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 证件类型
     */
    private String idNumberType;

    /**
     * 证件正面
     */
    private String idPhotoFront;

    /**
     * 证件反面
     */
    private String idPhotoSide;

    /**
     * 出生时间
     */
    private LocalDateTime birthDate;

    /**
     * 生日类型
     */
    private String birthType;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 所属公司
     */
    private String companyId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
