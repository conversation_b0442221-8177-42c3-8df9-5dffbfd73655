package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RosterFieldDAO {

    /**
     * 主键id
     */
    private String id;

    /**
     * 信息子集id
     */
    private String rosterRuleTeamId;

    /**
     * 分组id
     */
    private String rosterRuleGroupId;

    /**
     * 字段参数id
     */
    private String field;

    /**
     * 字段参数名称
     */
    private String name;

    /**
     * 字段类型
     * [0单行文本框 1单行数字框 2多行文本框 3单行下拉框 4多选下拉框 5日期选择框 6时间选择器 7上传 8单选框]
     */
    private Integer type;

    /**
     * 该字段是否非空[0否 1是]
     */
    private Integer isNonNull;

    /**
     * 是否系统内置 0否 1是
     */
    private Integer isSystem;

    /**
     * 字段长度(文本类型限制长度)
     */
    private Integer length;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 是否展示[0否 1是]
     */
    private Integer isShow;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 应用渠道号
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 是否删除[0否 1是]
     */
    private Integer isDelete;
}