package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * &#064;DATE: 2024/9/13
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RosterDAO {

    private String identityId;

    private String userName;

    private String post;

    private String jobNumber;

    private String idNumber;

    private String idNumberType;

    private String idPhotoFront;

    private String idPhotoSide;

    private String birthDate;

    private String birthType;

    private String extend;

    private String companyId;

    private String createTime;

    private String updateTime;

    private String appChannelId;

    private String dataChannelId;


}
