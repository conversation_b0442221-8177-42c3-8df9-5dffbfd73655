package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RosterTeamDAO {

    /**
     * 主键id
     */
    private String id;

    /**
     * field键
     */
    private String field;

    /**
     * 子集名称
     */
    private String title;

    /**
     * 是否支持多组添加 0否 1是
     */
    private Integer isAdd;

    /**
     * 是否展示[0否 1是]
     */
    private Integer isShow;

    /**
     * 是否系统内置 0否 1是
     */
    private Integer isSystem;

    /**
     * 备注
     */
    private String remark;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 子集下分组数量上限
     */
    private Integer countMax;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 应用渠道号
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 是否删除[0否 1是]
     */
    private Integer isDelete;

}
