package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IdentityFriendRelationDAO {
    /**
     * 用户身份id
     */
    private String identityId;

    /**
     * 好友用户身份id
     */
    private String targetIdentityId;

    /**
     * 扩展信息
     */
    private String extraContent;

    /**
     * 应用渠道id
     */
    private String appChannelId;

    /**
     * 渠道id
     */
    private String dataChannelId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
