package com.microservice.user.entity.dao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GroupDataRelationDAO {
    /**
     * 用户组id
     */
    private String groupId;

    /**
     * 数据
     */
    private String data;

    /**
     * 创建时间
     */
    private Date createTime;


    private String identityId;
}
