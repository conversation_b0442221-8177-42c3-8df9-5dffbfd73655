package com.microservice.user.entity.dao;

import lombok.Builder;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 成员标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09 14:18:18
 */
@Data
@Builder
public class LabelInfoDAO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 父级标签id
     */
    private String parentId;

    /**
     * 状态（1有效 0无效）
     */
    private Integer status;

    private String appChannelId;


    private String dataChannelId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
