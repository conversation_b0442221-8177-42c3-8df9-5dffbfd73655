package com.microservice.user.entity.dao;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class EntityRelationDAO {
    /**
     * 用户身份id
     */
    private String entityId;

    /**
     * 好友用户身份id
     */
    private String targetEntityId;

    /**
     * 扩展信息
     */
    private String extraContent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
