package com.microservice.user.entity.dao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dto.ms.GroupDTO;
import com.mssdk.user.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 用户信息带身份信息
 *
 * <AUTHOR>
 * @date 2024/12/11 17:47
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfoWithIdentityDAO extends UserInfoDAO {

    /**
     * 身份列表
     *
     * @param null
     * @return
     * @return null
     * <AUTHOR>
     * @date 2024/12/11 17:48
     **/
    private List<IdentityInfoWithCompanyInfoDAO> identityList;
}
