package com.microservice.user.entity.dao;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class TokenInfoDAO {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * token主键
     */
    private String tokenId;

    /**
     * 用户登录token
     */
    private String token;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户身份id
     */
    private String identityId;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 有效期截止时间
     */
    private Long endTimestamp;

    /**
     * token 状态
     */
    private String tokenStatus;

    /**
     * 应用渠道号
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
