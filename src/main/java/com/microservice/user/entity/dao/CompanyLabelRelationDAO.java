package com.microservice.user.entity.dao;

import java.io.Serializable;
import java.util.Date;

import lombok.Builder;
import lombok.Data;

/**
 * <p>
 * 公司和标签关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09 14:18:18
 */
@Data
@Builder
public class CompanyLabelRelationDAO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 标签id
     */
    private String labelId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 状态（1有效 0无效）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
