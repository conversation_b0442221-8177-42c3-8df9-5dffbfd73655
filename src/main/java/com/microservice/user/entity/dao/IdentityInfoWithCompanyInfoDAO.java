package com.microservice.user.entity.dao;

import com.microservice.user.entity.dto.ms.DepartmentDTO;
import com.microservice.user.entity.dto.ms.GroupDTO;
import com.microservice.user.entity.dto.ms.PositionDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdentityInfoWithCompanyInfoDAO extends IdentityInfoDAO {
    /**
     * 公司id
     */
    private String companyId;

    /**
     * 公司名
     */
    private String companyName;

    /**
     * 保留域，不处理直接返回
     */
    private String companyExtraContent;

    /**
     * 部门列表
     */
    private List<DepartmentDTO> departmentList;

    /**
     * 职位列表
     */
    private List<PositionDTO> positionList;

    /**
     * 角色列表
     */
    private List<GroupDTO> groupList;
}
