package com.microservice.user.entity.dao;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class ERWithEntityDAO {
    /**
     * 标签id
     */
    private String entityId;
    private String entityName;
    private Integer entityType;

    /**
     * 目标标签id
     */
    private String targetEntityId;
    private String targetEntityName;
    private Integer targetEntityType;

    /**
     * 扩展信息
     */
    private String extraContent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
