package com.microservice.user.entity.dao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfoDAO {
    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 保留域，不处理直接返回
     */
    private String extraContent;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 用户来源渠道id
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 身份id
     */
    private String identityId;

    /**
     * 保留域，不处理直接返回
     */
    private String uccExtraContent;

}
