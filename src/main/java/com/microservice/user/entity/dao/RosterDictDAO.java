package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RosterDictDAO {

    /**
     * 主键名称
     */
    private String id;

    /**
     * 子集id
     */
    private String rosterRuleTeamId;

    /**
     * 分组id
     */
    private String rosterRuleGroupId;

    /**
     * 字段参数id
     */
    private String rosterRuleFieldId;

    /**
     * 字段字典名称
     */
    private String name;

    /**
     * 对应值
     */
    private String value;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 应用渠道号
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

}
