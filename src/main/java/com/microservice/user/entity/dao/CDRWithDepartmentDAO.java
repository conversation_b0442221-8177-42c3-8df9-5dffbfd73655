package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CDRWithDepartmentDAO extends DepartmentInfoDAO {
    /**
     * 公司id
     */
    private String companyId;

    /**
     * 部门id
     */
    private String departmentId;


}
