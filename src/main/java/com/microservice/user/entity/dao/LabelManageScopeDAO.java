package com.microservice.user.entity.dao;

import java.io.Serializable;
import java.util.Date;

import lombok.*;

/**
 * <p>
 * 标签成员管理范围关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16 14:51:23
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelManageScopeDAO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 标签id
     */
    private String labelId;

    /**
     * 成员与标签关系表id
     */
    private Integer labelRelationId;

    /**
     * 关联id(部门、身份id)
     */
    private String relationId;

    /**
     * 类型 (1部门 2员工[identity_info 主键])
     */
    private Integer relationType;

    /**
     * 扩展信息
     */
    private String extraContent;

    /**
     * 状态（1有效 0无效）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
