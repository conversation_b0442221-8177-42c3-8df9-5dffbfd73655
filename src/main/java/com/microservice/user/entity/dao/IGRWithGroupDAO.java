package com.microservice.user.entity.dao;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IGRWithGroupDAO extends GroupInfoDAO {
    /**
     * 身份id
     */
    private String identityId;

    /**
     * 用户组id
     */
    private String groupId;

    /**
     * identity_group_relation表创建时间
     */
    private String igrCreateTime;
}
