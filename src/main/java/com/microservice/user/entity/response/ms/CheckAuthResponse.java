package com.microservice.user.entity.response.ms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckAuthResponse {
    private String eventId;
    private int isPassEvent;
    private String pageId;
    private int isPassPage;
    private String data;
    private int isPassData;
    private String identityId;
}
