package com.microservice.user.entity.dto.ms.identity;

import com.microservice.user.entity.dao.IdentityByCompanyDAO;
import com.microservice.user.util.StringUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * &#064;DATE: 2024/11/10
 * &#064;AUTHOR: XSL
 *
 */
@Data
public class IdentityByCompanyDTO implements Serializable {

    private String identityId;

    private Map<String, Object> identityExtraContent;

    private String userId;

    private String userName;

    private Map<String, Object> userExtraContent;

    public static IdentityByCompanyDTO create(IdentityByCompanyDAO identityByCompanyDAO) {
        IdentityByCompanyDTO identityByCompanyDTO = new IdentityByCompanyDTO();
        identityByCompanyDTO.setIdentityId(identityByCompanyDAO.getIdentityId());
        identityByCompanyDTO.setUserId(identityByCompanyDAO.getUserId());
        identityByCompanyDTO.setUserName(identityByCompanyDAO.getUserName());
        identityByCompanyDTO.setIdentityExtraContent(StringUtil.getMapFromString(identityByCompanyDAO.getIdentityExtraContent()));
        identityByCompanyDTO.setUserExtraContent(StringUtil.getMapFromString(identityByCompanyDAO.getUserExtraContent()));
        return identityByCompanyDTO;

    }
}
