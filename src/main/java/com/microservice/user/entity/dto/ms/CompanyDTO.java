package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.CompanyInfoDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompanyDTO extends BaseDTO {
    private String companyId;//主键
    private String companyName;//公司名称
    private String parentId;
    private int userCount;
    private List<CompanyDTO> children;

    public static CompanyDTO create(CompanyInfoDAO dao) {
        CompanyDTO companyDTO = CompanyDTO.builder()
                .companyId(dao.getCompanyId())
                .companyName(dao.getCompanyName())
                .parentId(dao.getParentId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            companyDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return companyDTO;
    }
}
