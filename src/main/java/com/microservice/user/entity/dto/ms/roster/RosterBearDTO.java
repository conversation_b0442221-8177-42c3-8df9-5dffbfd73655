package com.microservice.user.entity.dto.ms.roster;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * &#064;DATE: 2024/9/5
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RosterBearDTO {

    /**
     * 字段参数id
     */
    private String field;

    /**
     * 字段参数名称
     */
    private String name;

    /**
     * 字段类型
     * [ 0单行文本框 1单行数字框 2多行文本框 3单行下拉框 4多选下拉框 5日期选择框 6时间选择器 7上传]
     */
    private Integer type;

    /**
     * 该字段是否非空[0否 1是]
     */
    private Integer isNonNull;

    /**
     * 字段长度(文本类型限制长度)
     */
    private Integer length;

    /**
     * 是否展示[0否 1是]
     */
    private Integer isShow;

}
