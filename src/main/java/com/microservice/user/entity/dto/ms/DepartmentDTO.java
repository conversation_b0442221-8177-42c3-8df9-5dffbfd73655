package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.CDRWithDepartmentDAO;
import com.microservice.user.entity.dao.DepartmentInfoDAO;
import com.microservice.user.entity.dao.IDRWithDepartmentDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DepartmentDTO extends BaseDTO {
    private String departmentId;//部门id
    private String departmentName;//部门名称
    private String parentId;//上级id
    private String companyId;//公司id
    private List<String> identityIds; // 身份id集合
    private List<String> allDepartmentIds; // 所有部门id集合 包含其父部门

    public static DepartmentDTO create(IDRWithDepartmentDAO dao) {
        DepartmentDTO departmentDTO = DepartmentDTO.builder()
                .departmentId(dao.getDepartmentId())
                .departmentName(dao.getDepartmentName())
                .parentId(dao.getParentId())
                .sort(dao.getSort())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            departmentDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return departmentDTO;
    }

    public static DepartmentDTO create(DepartmentInfoDAO dao) {
        DepartmentDTO departmentDTO = DepartmentDTO.builder()
                .departmentId(dao.getDepartmentId())
                .departmentName(dao.getDepartmentName())
                .parentId(dao.getParentId())
                .sort(dao.getSort())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            departmentDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return departmentDTO;
    }

    public static DepartmentDTO create(CDRWithDepartmentDAO dao) {
        DepartmentDTO departmentDTO = DepartmentDTO.builder()
                .departmentId(dao.getDepartmentId())
                .departmentName(dao.getDepartmentName())
                .parentId(dao.getParentId())
                .sort(dao.getSort())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .companyId(dao.getCompanyId())
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            departmentDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return departmentDTO;
    }
}
