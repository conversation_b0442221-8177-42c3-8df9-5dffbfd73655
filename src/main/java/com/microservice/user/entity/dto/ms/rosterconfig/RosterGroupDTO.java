package com.microservice.user.entity.dto.ms.rosterconfig;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class RosterGroupDTO extends BaseRosterConfigDTO {

    /**
     * 主键id
     */
    private String id;

    /**
     * field键
     */
    private String field;

    /**
     * 信息子集id
     */
    private String rosterRuleTeamId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 分组中最大字段限制
     */
    private Integer countMax;

    /**
     * 排序顺序
     */
    private Integer sort;

    /**
     * 是否显示 [0否 1是]
     */
    private Integer isShow;

    /**
     * 是否系统内置 0否 1是
     */
    private Integer isSystem;

    /**
     * 是否支持多组添加 0否 1是
     */
    private Integer isAdd;

    /**
     * 描述
     */
    private String remark;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 字段列表
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<RosterFieldDTO> rosterFiledList;
}
