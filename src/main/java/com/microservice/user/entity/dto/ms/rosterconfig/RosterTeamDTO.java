package com.microservice.user.entity.dto.ms.rosterconfig;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class RosterTeamDTO extends BaseRosterConfigDTO{
    /**
     * 主键id
     */
    private String id;

    /**
     * field键
     */
    private String field;

    /**
     * 子集名称
     */
    private String name;

    /**
     * 是否系统内置 0否 1是
     */
    private Integer isSystem;

    /**
     * 是否展示[0否 1是]
     */
    private Integer isShow;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子集下分组上限
     */
    private Integer countMax;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 分组列表
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    List<RosterGroupDTO> rosterGroupList;

}
