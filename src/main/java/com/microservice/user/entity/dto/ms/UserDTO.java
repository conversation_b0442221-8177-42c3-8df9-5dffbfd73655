package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.UserInfoDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDTO extends BaseDTO {
    private String userId;//用户id
    private String identityId;//用户身份id
    private String token;//token
    private String userName;//用户名
    private String lastLoginTime;//最后登录时间
    private String lastLoginIp;//最后登录ip
    private List<IdentityDTO> identityList;
    private Map<String, Object> uccExtraContent;

    public static UserDTO create(UserInfoDAO dao) {
        UserDTO userDTO = UserDTO.builder()
                .userId(dao.getUserId())
                .userName(dao.getUserName())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .sort(dao.getSort())
                .identityId(dao.getIdentityId())
                .build();
        userDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        if(!StringUtil.isEmpty(dao.getUccExtraContent())){
            userDTO.setUccExtraContent(StringUtil.getMapFromString(dao.getUccExtraContent()));
        }
        if (dao.getCreateTime() != null) {
            userDTO.setCreateTime(TimeUtil.date2String(dao.getCreateTime()));
        }
        if (dao.getUpdateTime() != null) {
            userDTO.setUpdateTime(TimeUtil.date2String(dao.getUpdateTime()));
        }
        return userDTO;
    }
}