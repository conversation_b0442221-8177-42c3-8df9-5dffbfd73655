package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.EntityInfoDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class EntityDTO extends BaseDTO {
    private String entityId;
    private String entityName;
    private Integer type;

    public static EntityDTO create(EntityInfoDAO dao) {
        EntityDTO dto = EntityDTO.builder()
                .entityId(dao.getEntityId())
                .entityName(dao.getEntityName())
                .type(dao.getType())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .sort(dao.getSort())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (dao.getExtraContent() != null) {
            dto.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return dto;
    }
}
