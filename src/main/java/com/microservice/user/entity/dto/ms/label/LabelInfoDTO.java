package com.microservice.user.entity.dto.ms.label;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelInfoDTO {


    private String id;

    /**
     * 标签成员关系id
     */
    private String relationId;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 父标签id
     */
    private String parentId;

    /**
     * 状态（1有效 0无效）
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private List<LabelInfoDTO> childrenLabels;

    /**
     * 标签成员id集合 包括其子标签
     */
    private List<String> identityIds;
}
