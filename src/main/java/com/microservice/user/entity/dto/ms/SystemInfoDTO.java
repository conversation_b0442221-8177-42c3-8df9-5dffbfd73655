package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SystemInfoDTO {
    private String name;
    private String ip;
    private String port;
    private String version;
    private String appChannelId;
    private String dataChannelId;
    private Map<String, Object> dependencies;
    private int health;
}
