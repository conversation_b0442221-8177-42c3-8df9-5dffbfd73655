package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TreeItemDTO {
    public String id;
    public String name;
    public String type;
    // 预加入
    public Integer sort;
    public Map<String, Object> extraContent;
    public List<TreeItemDTO> children;
    public String identityId;
    public String userId;
    public Map<String, Object> uccExtraContent;
}