package com.microservice.user.entity.dto.ms;

import com.microservice.user.entity.dao.TokenInfoDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TokenDTO {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * token主键
     */
    private String tokenId;

    /**
     * 用户登录token
     */
    private String token;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户身份id
     */
    private String identityId;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 有效期截止时间
     */
    private Long endTimestamp;

    /**
     * token 状态
     */
    private String tokenStatus;

    /**
     * 应用渠道号
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    public static TokenDTO create(TokenInfoDAO dao) {
        if (dao == null) return null;
        TokenDTO tokenDTO = TokenDTO.builder()
                .tokenId(dao.getTokenId())
                .token(dao.getToken())
                .userId(dao.getUserId())
                .identityId(dao.getIdentityId())
                .ip(dao.getIp())
                .endTimestamp(dao.getEndTimestamp())
                .tokenStatus(dao.getTokenStatus())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .build();
        if (dao.getCreateTime() != null) {
            tokenDTO.setCreateTime(TimeUtil.date2String(dao.getCreateTime()));
        }
        if (dao.getUpdateTime() != null) {
            tokenDTO.setUpdateTime(TimeUtil.date2String(dao.getUpdateTime()));
        }
        return tokenDTO;
    }
}
