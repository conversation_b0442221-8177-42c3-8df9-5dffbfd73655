package com.microservice.user.entity.dto.ms;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * &#064;DATE: 2024/11/13
 * &#064;AUTHOR: XSL
 *
 */
@Data
public class DeptTreeDTO {

    /**
     * 部门id
     */
    private String departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 上级id
     */
    private String parentId;

    /**
     * 子集
     */
    private List<DeptTreeDTO> children;

    /**
     * 对象转换
     * @param departmentDTO
     * @return
     */
    public static DeptTreeDTO create(DepartmentDTO departmentDTO) {
        DeptTreeDTO deptTreeDTO = new DeptTreeDTO();
        deptTreeDTO.setDepartmentId(departmentDTO.getDepartmentId());
        deptTreeDTO.setDepartmentName(departmentDTO.getDepartmentName());
        deptTreeDTO.setParentId(departmentDTO.getParentId());
        return deptTreeDTO;
    }

    /**
     * 新增子集对象
     * @param deptTreeDTO 子集对象
     */
    public void addDeptTreeDTO(DeptTreeDTO deptTreeDTO) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(deptTreeDTO);
    }

}
