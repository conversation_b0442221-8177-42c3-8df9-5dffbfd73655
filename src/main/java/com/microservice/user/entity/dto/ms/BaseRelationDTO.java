package com.microservice.user.entity.dto.ms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseRelationDTO {
    public String createTime;//创建时间
    public String updateTime;//更新时间
    public Map<String, Object> extraContent;//扩展信息
}