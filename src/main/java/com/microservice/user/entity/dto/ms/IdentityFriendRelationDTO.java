package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.IdentityFriendRelationDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdentityFriendRelationDTO extends BaseRelationDTO {
    public String identityId;//用户身份id
    public String targetIdentityId;//好友身份id
    public String appChannelId;//应用渠道ID
    public String dataChannelId;//渠道ID
    public IdentityDTO targetIdentity;

    public static IdentityFriendRelationDTO create(IdentityFriendRelationDAO dao) {
        IdentityFriendRelationDTO dto = IdentityFriendRelationDTO.builder()
                .identityId(dao.getIdentityId())
                .targetIdentityId(dao.getTargetIdentityId())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            dto.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        if (dao.getCreateTime() != null) {
            dto.setCreateTime(TimeUtil.date2String(dao.getCreateTime()));
        }
        if (dao.getUpdateTime() != null) {
            dto.setUpdateTime(TimeUtil.date2String(dao.getUpdateTime()));
        }
        return dto;
    }
}