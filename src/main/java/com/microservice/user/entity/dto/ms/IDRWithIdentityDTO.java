package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.IDRWithIdentityDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class IDRWithIdentityDTO extends IdentityDTO {
    private String departmentId;

    public static IDRWithIdentityDTO create(IDRWithIdentityDAO dao) {
        IDRWithIdentityDTO dto = IDRWithIdentityDTO.builder()
                .identityId(dao.getIdentityId())
                .identityName(dao.getIdentityName())
                .userId(dao.getUserId())
                .sort(dao.getSort())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .departmentId(dao.getDepartmentId())
                .build();
        if (dao.getExtraContent() != null) {
            dto.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return dto;
    }
}
