package com.microservice.user.entity.dto.ms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataListDTO<T> {
    @Builder.Default
    public long dataCount = 0;
    @Builder.Default
    public List<T> dataList = new ArrayList<>();

    public static <T> DataListDTO<T> create(List<T> dataList, long dataCount) {
        DataListDTO<T> dataListDTO = new DataListDTO<>();
        dataListDTO.setDataCount(dataCount);
        dataListDTO.setDataList(dataList);
        return dataListDTO;
    }

    public static <T> DataListDTO<T> create(List<T> dataList) {
        DataListDTO<T> dataListDTO = new DataListDTO<>();
        dataListDTO.setDataCount(dataList.size());
        dataListDTO.setDataList(dataList);
        return dataListDTO;
    }

    public static <T> DataListDTO<T> create() {
        DataListDTO<T> dataListDTO = new DataListDTO<>();
        dataListDTO.setDataCount(0);
        dataListDTO.setDataList(new ArrayList<>());
        return dataListDTO;
    }

}
