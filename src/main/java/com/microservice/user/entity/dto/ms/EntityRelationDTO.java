package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.ERWithEntityDAO;
import com.microservice.user.entity.dao.EntityRelationDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class EntityRelationDTO extends BaseRelationDTO {
    private String entityId;
    private String entityName;
    private Integer entityType;
    private String targetEntityId;
    private String targetEntityName;
    private Integer targetEntityType;

    public static EntityRelationDTO create(EntityRelationDAO dao) {
        EntityRelationDTO dto = EntityRelationDTO.builder()
                .entityId(dao.getEntityId())
                .targetEntityId(dao.getTargetEntityId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            dto.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return dto;
    }

    public static EntityRelationDTO create(ERWithEntityDAO dao) {
        EntityRelationDTO dto = EntityRelationDTO.builder()
                .entityId(dao.getEntityId())
                .entityName(dao.getEntityName())
                .entityType(dao.getEntityType())
                .targetEntityId(dao.getTargetEntityId())
                .targetEntityName(dao.getTargetEntityName())
                .targetEntityType(dao.getTargetEntityType())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            dto.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return dto;
    }
}
