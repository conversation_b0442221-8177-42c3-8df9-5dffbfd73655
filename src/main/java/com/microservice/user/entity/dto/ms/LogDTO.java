package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.LogInfoDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LogDTO extends BaseDTO {
    private Integer id;
    private int type;
    private String opt;
    private String content;

    public static LogDTO create(LogInfoDAO dao) {
        LogDTO logDTO = LogDTO.builder()
                .id(dao.getId())
                .type(dao.getType())
                .opt(dao.getOpt())
                .content(dao.getContent())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .extraContent(StringUtil.getMapFromString(dao.getExtraContent()))
                .build();
        if (dao.getCreateTime() != null) {
            logDTO.setCreateTime(TimeUtil.date2String(dao.getCreateTime()));
        }
        return logDTO;
    }
}
