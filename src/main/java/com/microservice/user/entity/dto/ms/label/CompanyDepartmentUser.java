package com.microservice.user.entity.dto.ms.label;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 公司、部门、身份 集合dto对象（组织架构）
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompanyDepartmentUser implements Serializable {
    //{"companyId": ["ffc6cb49-1f97-5608-cc9a-d8e11e89f95b"], "identityId": [], "departmentId": []}
    //公司id
    private Set<String> companyId;
    //部门id
    private Set<String> departmentId;
    //身份id
    private Set<String> identityId;

    //公司列表
    private Set<String> companyName;
    //部门列表
    private Set<String> departmentName;
    //身份列表
    private Set<String> identityName;

}
