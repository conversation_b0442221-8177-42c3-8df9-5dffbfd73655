package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.ChatGroupInfoDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.sql.Time;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatGroupDTO extends BaseDTO {
    public String chatGroupId;//群聊id
    public String chatGroupName;//群聊名称
    public List<IdentityDTO> identityList;

    public static ChatGroupDTO create(ChatGroupInfoDAO dao) {
        ChatGroupDTO chatGroupDTO = ChatGroupDTO.builder()
                .chatGroupId(dao.getChatGroupId())
                .chatGroupName(dao.getChatGroupName())
                .sort(dao.getSort())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (dao.getExtraContent() != null) {
            chatGroupDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return chatGroupDTO;
    }
}