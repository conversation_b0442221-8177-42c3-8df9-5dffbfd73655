package com.microservice.user.entity.dto.ms.label;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelMemberDTO {

    private Integer id;
    /**
     * 关联id
     */
    private String relationId;
    /**
     * 标签id
     */
        private String labelId;
    /**
     * 1:部门 2:用户
     */
    private Integer relationType;

    private String name;

    private String parentName;

    private List<LabelManageScopeDTO> manageScopes;


    private CompanyDepartmentUser manageMrVisibility;
}
