package com.microservice.user.entity.dto.ms.roster;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * &#064;DATE: 2024/9/11
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RosterAddListDTO {

    /**
     * 身份花名册数据
     */
    private List<IdentityRosterAddDTO> identityRosterList;

    /**
     * 所属公司
     */
    private String companyId;

    /**
     * 渠道ID
     */
    private String appChannelId;

    /**
     * 数据ID
     */
    private String dataChannelId;

}
