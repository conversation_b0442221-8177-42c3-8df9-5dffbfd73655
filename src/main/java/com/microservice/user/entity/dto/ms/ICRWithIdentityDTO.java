package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.ICRWithIdentityDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ICRWithIdentityDTO extends IdentityDTO {
    private String companyId;

    public static ICRWithIdentityDTO create(ICRWithIdentityDAO dao) {
        ICRWithIdentityDTO dto = ICRWithIdentityDTO.builder()
                .identityId(dao.getIdentityId())
                .identityName(dao.getIdentityName())
                .userId(dao.getUserId())
                .sort(dao.getSort())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .companyId(dao.getCompanyId())
                .build();
        dto.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        return dto;
    }
}
