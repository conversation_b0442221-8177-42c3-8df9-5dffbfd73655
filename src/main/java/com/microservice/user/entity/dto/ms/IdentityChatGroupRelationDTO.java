package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.IdentityChatGroupRelationDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdentityChatGroupRelationDTO extends BaseRelationDTO {
    private String identityId;
    private String chatGroupId;
    private IdentityDTO identity;

    public static IdentityChatGroupRelationDTO create(IdentityChatGroupRelationDAO dao) {
        IdentityChatGroupRelationDTO dto = IdentityChatGroupRelationDTO.builder()
                .identityId(dao.getIdentityId())
                .chatGroupId(dao.getChatGroupId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (dao.getExtraContent() != null) {
            dto.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return dto;
    }
}
