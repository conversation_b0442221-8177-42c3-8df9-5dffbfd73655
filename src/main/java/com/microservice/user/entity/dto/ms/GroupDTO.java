package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.GroupInfoDAO;
import com.microservice.user.entity.dao.IGRWithGroupDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GroupDTO extends BaseDTO {
    public String groupId;//用户组id
    public String groupName;//用户组名
    public Integer userCount;
    public List<String> eventIdList;//用户组关联事件id列表
    public List<String> pageIdList;//用户组关联页面id列表
    public List<String> dataList;//用户组关联数据列表
    public List<String> identityIdList;

    public static GroupDTO create(IGRWithGroupDAO dao) {
        GroupDTO groupDTO = GroupDTO.builder()
                .groupId(dao.getGroupId())
                .groupName(dao.getGroupName())
                .sort(dao.getSort())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            groupDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return groupDTO;
    }

    public static GroupDTO create(GroupInfoDAO dao) {
        GroupDTO groupDTO = GroupDTO.builder()
                .groupId(dao.getGroupId())
                .groupName(dao.getGroupName())
                .sort(dao.getSort())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            groupDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return groupDTO;
    }


}