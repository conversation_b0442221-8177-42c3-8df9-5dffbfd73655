package com.microservice.user.entity.dto.ms.roster;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * &#064;DATE: 2024/9/5
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IdentityRosterAddDTO {

    /**
     * 用户名称
     */
    private String identityName;

    /**
     * 部门ID集合
     */
    private List<String> deptIdList;

    /**
     * 职位
     */
    private String post;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 证件类型
     */
    private String idNumberType;

    /**
     * 证件正面
     */
    private String idPhotoFront;

    /**
     * 证件反面
     */
    private String idPhotoSide;

    /**
     * 用户账号 (手机号)
     */
    private String userName;

    /**
     * 员工状态:1在职、2离职
     */
    private Integer employeeStatus;

    /**
     * 员工类型： 1全职、2实习、3外派
     */
    private Integer employeeType;

    /**
     * 入职时间
     */
    private String joinTime;

    /**
     * 历史工龄
     */
    private String seniority;

    /**
     * 出生时间
     */
    private String birthDate;

    /**
     * 生日类型
     */
    private String birthType;

    /**
     * 扩展字段
     */
    private Map<String, Object> extend;

}
