package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.IdentityInfoDAO;
import com.microservice.user.entity.dao.IdentityInfoWithCompanyInfoDAO;
import com.microservice.user.entity.dao.IdentityWithUserAndCompanyDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdentityDTO extends BaseDTO {
    private String userId;
    private String identityId;
    private String identityName;
    private int isSelected;
    private UserDTO user;
    private TokenDTO token;
    private CompanyDTO company;
    private String companyId;
    private List<String> departmentIdList;
    private List<DepartmentDTO> departmentList;
    private List<String> positionIdList;
    private List<PositionDTO> positionList;
    private List<String> groupIdList;
    private List<GroupDTO> groupList;
    private List<String> eventIdList;
    private List<String> pageIdList;
    private List<IdentityGroupRelationDTO> igrList;//身份和用户组关系列表

    public static IdentityDTO create(IdentityInfoDAO dao) {
        if (dao == null) {
            return null;
        }
        IdentityDTO identityDTO = IdentityDTO.builder()
                .userId(dao.getUserId())
                .identityId(dao.getIdentityId())
                .identityName(dao.getIdentityName())
                .sort(dao.getSort())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (dao.getExtraContent() != null) {
            identityDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return identityDTO;
    }

    public static IdentityDTO create(IdentityInfoWithCompanyInfoDAO dao) {
        IdentityDTO identityDTO = IdentityDTO.builder()
                .userId(dao.getUserId())
                .companyId(dao.getCompanyId())
                .identityId(dao.getIdentityId())
                .identityName(dao.getIdentityName())
                .sort(dao.getSort())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (dao.getExtraContent() != null) {
            identityDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        //公司实体
        if (!StringUtil.isEmpty(dao.getCompanyId())) {
            CompanyDTO companyDTO = CompanyDTO.builder()
                    .companyId(dao.getCompanyId())
                    .companyName(dao.getCompanyName())
                    .build();
//            if (dao.getCompanyExtraContent() != null) {
//                companyDTO.setExtraContent(StringUtil.getMapFromString(dao.getCompanyExtraContent()));
//            }
            identityDTO.setCompany(companyDTO);
        }
        return identityDTO;
    }

    public static IdentityDTO create(IdentityWithUserAndCompanyDAO dao) {
        IdentityDTO identityDTO = IdentityDTO.builder()
                .userId(dao.getUserId())
                .companyId(dao.getCompanyId())
                .identityId(dao.getIdentityId())
                .identityName(dao.getIdentityName())
                .sort(dao.getSort())
                .appChannelId(dao.getAppChannelId())
                .dataChannelId(dao.getDataChannelId())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (dao.getIdentityExtraContent() != null) {
            identityDTO.setExtraContent(StringUtil.getMapFromString(dao.getIdentityExtraContent()));
        }
        //用户实体
        UserDTO userDTO = UserDTO.builder()
                .userId(dao.getUserId())
                .userName(dao.getUserName())
                .build();
        if (dao.getUserExtraContent() != null) {
            userDTO.setExtraContent(StringUtil.getMapFromString(dao.getUserExtraContent()));
        }
        identityDTO.setUser(userDTO);
        //公司实体
        if (!StringUtil.isEmpty(dao.getCompanyId())) {
            CompanyDTO companyDTO = CompanyDTO.builder()
                    .companyId(dao.getCompanyId())
                    .companyName(dao.getCompanyName())
                    .build();
            if (dao.getCompanyExtraContent() != null) {
                companyDTO.setExtraContent(StringUtil.getMapFromString(dao.getCompanyExtraContent()));
            }
            identityDTO.setCompany(companyDTO);
        }
        return identityDTO;
    }
}