package com.microservice.user.entity.dto.ms;

import com.microservice.user.entity.dao.IGRWithGroupDAO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IdentityGroupRelationDTO {
    /**
     * 身份id
     */
    private String identityId;

    /**
     * 用户组id
     */
    private String groupId;

    /**
     * 创建时间
     */
    private String createTime;


    public IdentityGroupRelationDTO(IGRWithGroupDAO dao) {
        identityId = dao.getIdentityId();
        groupId = dao.getGroupId();
        createTime = dao.getIgrCreateTime();
    }
}
