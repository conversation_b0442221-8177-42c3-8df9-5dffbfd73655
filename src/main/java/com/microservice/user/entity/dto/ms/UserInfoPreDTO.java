package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.UserInfoPreDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfoPreDTO {
    /**
     * 预加入用户id
     */
    private String preUserId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 预加入用户信息
     */
    private Map<String, Object> extraContent;

    /**
     * 用户来源渠道id
     */
    private String appChannelId;

    /**
     * 数据渠道号
     */
    private String dataChannelId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;


    public UserInfoPreDTO(UserInfoPreDAO dao) {
        preUserId = dao.getPreUserId();
        userName = dao.getUserName();
        appChannelId = dao.getAppChannelId();
        dataChannelId = dao.getDataChannelId();
        if (dao.getExtraContent() != null) {
            extraContent = StringUtil.getMapFromString(dao.getExtraContent());
        }
        if (dao.getCreateTime() != null) {
            createTime = TimeUtil.date2String(dao.getCreateTime());
        }
        if (dao.getUpdateTime() != null) {
            updateTime = TimeUtil.date2String(dao.getUpdateTime());
        }
    }
}
