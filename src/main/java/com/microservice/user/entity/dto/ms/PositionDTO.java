package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.microservice.user.entity.dao.CPRWithPositionDAO;
import com.microservice.user.entity.dao.DPRWithPositionDAO;
import com.microservice.user.entity.dao.IPRWithPositionDAO;
import com.microservice.user.entity.dao.PositionInfoDAO;
import com.microservice.user.util.StringUtil;
import com.microservice.user.util.TimeUtil;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PositionDTO extends BaseDTO {
    public String positionId;//主键
    public String positionName;//职位名称
    public String parentId;//上级职位id
    public String companyId;//公司id
    public String departmentId;//部门id

    public static PositionDTO create(IPRWithPositionDAO dao) {
        PositionDTO positionDTO = PositionDTO.builder()
                .positionId(dao.getPositionId())
                .positionName(dao.getPositionName())
                .parentId(dao.getParentId())
                .sort(dao.getSort())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            positionDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return positionDTO;
    }

    public static PositionDTO create(DPRWithPositionDAO dao) {
        PositionDTO positionDTO = PositionDTO.builder()
                .positionId(dao.getPositionId())
                .positionName(dao.getPositionName())
                .parentId(dao.getParentId())
                .sort(dao.getSort())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .departmentId(dao.getDepartmentId())
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            positionDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return positionDTO;
    }

    public static PositionDTO create(CPRWithPositionDAO dao) {
        PositionDTO positionDTO = PositionDTO.builder()
                .positionId(dao.getPositionId())
                .positionName(dao.getPositionName())
                .parentId(dao.getParentId())
                .sort(dao.getSort())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .companyId(dao.getCompanyId())
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            positionDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return positionDTO;
    }

    public static PositionDTO create(PositionInfoDAO dao) {
        PositionDTO positionDTO = PositionDTO.builder()
                .positionId(dao.getPositionId())
                .positionName(dao.getPositionName())
                .parentId(dao.getParentId())
                .sort(dao.getSort())
                .createTime(TimeUtil.date2String(dao.getCreateTime()))
                .updateTime(TimeUtil.date2String(dao.getUpdateTime()))
                .build();
        if (!StringUtil.isEmpty(dao.getExtraContent())) {
            positionDTO.setExtraContent(StringUtil.getMapFromString(dao.getExtraContent()));
        }
        return positionDTO;
    }
}