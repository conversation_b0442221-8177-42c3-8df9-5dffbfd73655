package com.microservice.user.entity.dto.ms.label;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelManageScopeDTO {

    /**
     * 管理范围
     */
    private String manageScope;

    /**
     * 关联id
     */
    private String relationId;

    /**
     * 类型 (1部门 2员工[identity_info 主键])
     */
    private String relationType;
}
