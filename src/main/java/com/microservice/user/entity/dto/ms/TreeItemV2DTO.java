package com.microservice.user.entity.dto.ms;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/12 17:10
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel("树形接口V2模板")
public class TreeItemV2DTO {

    @ApiModelProperty("节点ID")
    private String id;

    @ApiModelProperty("节点名称")
    private String name;

    @ApiModelProperty("节点类型")
    private String type;

    @ApiModelProperty("节点排序")
    private Integer sort;

    @ApiModelProperty("节点拓展字段")
    private Map<String, Object> extraContent;

    @ApiModelProperty("子节点列表")
    private List<TreeItemV2DTO> children;

    @ApiModelProperty("已加入人数，包含所有子节点")
    private Integer alreadyJoinedCount;

    @ApiModelProperty("预加入人数，包含所有子节点")
    private Integer preJoinedCount;

    @JsonIgnore
    @ApiModelProperty("已加入用户Id列表")
    private List<String> alreadyJoinedList;

    @JsonIgnore
    @ApiModelProperty("预加入用户Id列表")
    private List<String> preJoinedList;

    @ApiModelProperty("本节点已加入人数")
    private Integer currentAlreadyJoinedCount;

    @ApiModelProperty("本节点预加入人数")
    private Integer currentPreJoinedCount;

    @ApiModelProperty("总人数，包含子节点， = alreadyJoinedCount + preJoinedCount")
    private Integer totalCount;
}