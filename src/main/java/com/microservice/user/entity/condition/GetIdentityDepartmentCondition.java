package com.microservice.user.entity.condition;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetIdentityDepartmentCondition extends Condition {
    private String identityId;
    private List<String> identityIdList;
    private String departmentId;
    private List<String> departmentIdList;
    private String companyId;
    private Map<String, Object> extraContentCondition;
    private Map<String, Object> extraContentInCondition;
}
