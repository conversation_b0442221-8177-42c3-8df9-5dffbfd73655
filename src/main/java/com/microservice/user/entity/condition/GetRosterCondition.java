package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * &#064;DATE: 2024/9/6
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetRosterCondition {

    /**
     * 身份ID集合
     */
    private List<String> identityIdList;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 证件号类型
     */
    private Integer idNumberType;

    /**
     * (账户) 手机号
     */
    private String userName;

    /**
     * 员工类型 1在职 2离职
     */
    private Integer employeeStatus;

    /**
     * 员工状态： 1全职、2实习、3外派
     */
    private Integer employeeType;

    /**
     * 入职时间 开始
     */
    private String entryTimeStart;

    /**
     * 入职时间 结束
     */
    private String entryTimeEnd;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 产品ID
     */
    private String appChannelId;

    /**
     * 数据ID
     */
    private String dataChannelId;

}
