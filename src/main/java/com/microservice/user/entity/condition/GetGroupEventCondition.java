package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetGroupEventCondition extends Condition {
    private String identityId;
    private List<String> identityIdList;
    private String groupId;
    private List<String> groupIdList;
    private String eventId;
    private List<String> eventIdList;
}
