package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetIdentityEntityCondition extends Condition {
    private String identityId;
    private List<String> identityIdList;
    private String entityId;
    private List<String> entityIdList;
}
