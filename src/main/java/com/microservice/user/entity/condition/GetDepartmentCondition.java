package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class GetDepartmentCondition extends Condition {
    private List<String> identityIdList;
    private List<String> departmentIdList;
    private List<String> companyIdList;
    private String companyId;
    private String departmentId;
    private String parentId;
    private Map<String, Object> extraContentCondition;
    private String departmentName;
}
