package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class GetUserPreCondition extends Condition {
    private String preUserId;
    private List<String> preUserIdList;
    private String userName;
    private List<String> userNameList;
    private Map<String, Object> extraContentCondition;
    private Map<String, Object> extraContentInCondition;
}
