package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetTokenCondition extends Condition {
    private Integer id;
    private String userId;
    private List<String> userIdList;
    private String identityId;
    private List<String> identityIdList;
    private String tokenId;
    private List<String> tokenIdList;
    private String token;
    private List<String> tokenList;
    private String tokenStatus;
    private String ip;
}
