package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetCompanyDepartmentCondition extends Condition {
    private String companyId;
    private String departmentId;
    private List<String> companyIdList;
    private List<String> departmentIdList;
}
