package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetPositionCondition extends Condition {
    private List<String> identityIdList;
    private List<String> positionIdList;
    private List<String> companyIdList;
    private List<String> departmentIdList;
    private String companyId;
    private String departmentId;
    private String positionId;
    private String parentId;
    private Map<String, Object> extraContentCondition;
    private String positionName;
}
