package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetIdentityChatGroupCondition extends Condition {
    private String chatGroupId;
    private List<String> chatGroupIdList;
    private String identityId;
    private List<String> identityIdList;
    private Map<String, Object> extraContentCondition;
}
