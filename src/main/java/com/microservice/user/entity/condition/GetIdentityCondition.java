package com.microservice.user.entity.condition;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetIdentityCondition extends Condition {
    private String userId;
    private List<String> userIdList;
    private String identityId;
    private List<String> identityIdList;
    private Map<String, Object> extraContentCondition;
    private String appChannelId;
    private String dataChannelId;
}
