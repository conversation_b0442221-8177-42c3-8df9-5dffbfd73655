package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetEntityRelationCondition extends Condition {
    private String entityId;
    private List<String> entityIdList;
    private String targetEntityId;
    private List<String> targetEntityIdList;
    private Map<String, Object> extraContentCondition;
}
