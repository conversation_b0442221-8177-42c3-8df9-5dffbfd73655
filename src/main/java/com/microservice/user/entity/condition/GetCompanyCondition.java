package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetCompanyCondition extends Condition {
    private String companyId;
    private List<String> companyIdList;
    private List<String> parentIdList;
    private String companyName;
    private String parentId;
    private Map<String, Object> extraContentCondition;
    private Map<String, Object> extraContentInCondition;
}
