package com.microservice.user.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetIdentityFriendCondition extends Condition {
    private String identityId;
    private List<String> identityIdList;
    private String targetIdentityId;
    private List<String> targetIdentityIdList;
    private Map<String, Object> extraContentCondition;
}
