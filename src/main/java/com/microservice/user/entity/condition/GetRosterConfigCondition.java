package com.microservice.user.entity.condition;

import com.microservice.user.constant.RosterConfigConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GetRosterConfigCondition extends Condition{


    private List<String> primaryKeyIds;  // 公用主键查询条件
    private String name;               // 公用名称查询
    private String companyId;          // 公用公司id
    private Integer isSystem;          // 是否系统内置查询条件
    private Integer isShow;            // 公用是否显示查询条件
    private List<String> rosterTeamIds;  // 子集id查询查询条件 根据该条件查询子集下分组信息
    private List<String> rosterFields; // 字段参数field查询条件
    private List<String> rosterGroupIds; // 分组id查询条件
    // 是否删除条件,不传默认查未删除数据
    private Integer isDelete = RosterConfigConstant.IsDeleted.NOT_DELETED.getCode();
}
