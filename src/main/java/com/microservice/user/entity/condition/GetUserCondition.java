package com.microservice.user.entity.condition;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class GetUserCondition extends Condition {
    private String userId;
    private List<String> userIdList;
    private String companyId;
    private List<String> companyUserIdList;
    private String userName;
    private List<String> userNameList;
    private String identityId;
    private List<String> identityIdList;
    private Map<String, Object> extraContentCondition;
}
