package com.microservice.user.entity.condition;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetIdentityPositionCondition extends Condition {
    private String identityId;
    private List<String> identityIdList;
    private String positionId;
    private List<String> positionIdList;
    private Map<String, Object> extraContentCondition;
    private Map<String, Object> extraContentInCondition;
}
