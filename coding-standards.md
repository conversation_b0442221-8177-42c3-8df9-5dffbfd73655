# MS-User 微服务代码规范

## 1. 项目概述
本文档定义了 ms-user 微服务项目的代码编写规范，确保代码质量和团队协作效率。

## 2. 技术栈规范
- **Java版本**: JDK 8+
- **Spring Boot**: 2.3.2.RELEASE
- **数据访问**: MyBatis + MySQL
- **缓存**: Redis
- **文档**: Swagger 2.9.2
- **工具库**: Lombok, Hutool, Guava

## 3. 包结构规范
```
com.microservice.user
├── MsUserApplication.java           # 启动类
├── annotation/                      # 自定义注解
├── aspect/                         # AOP切面
├── common/                         # 通用组件
├── config/                         # 配置类
├── constant/                       # 常量定义
├── controller/                     # 控制器层
├── entity/                         # 实体类
│   ├── condition/                  # 查询条件
│   ├── dao/                        # 数据访问对象
│   ├── dto/                        # 数据传输对象
│   ├── request/                    # 请求对象
│   └── response/                   # 响应对象
├── exception/                      # 异常处理
├── listener/                       # 事件监听器
├── mapper/                         # 数据访问层
├── service/                        # 业务逻辑层
│   └── impl/                       # 业务实现类
└── util/                          # 工具类
```

## 4. 命名规范

### 4.1 类命名
- **Controller**: 以`Controller`结尾，如`UserController`
- **Service接口**: 以`Service`结尾，如`UserService`
- **Service实现**: 以`ServiceImpl`结尾，如`UserServiceImpl`
- **Mapper接口**: 以`Mapper`结尾，如`UserMapper`
- **DAO类**: 以`DAO`结尾，如`UserInfoDAO`
- **DTO类**: 以`DTO`结尾，如`UserDTO`
- **Request类**: 以`Request`结尾，如`AddUserRequest`
- **Response类**: 以`Response`结尾，如`MsResponse`
- **Exception类**: 以`Exception`结尾，如`UserException`
- **常量类**: 以`Constant`结尾，如`UserConstant`

### 4.2 方法命名
- **查询方法**: `get`、`find`、`query`开头
- **新增方法**: `add`、`create`、`insert`开头
- **修改方法**: `update`、`modify`、`edit`开头
- **删除方法**: `delete`、`remove`开头
- **判断方法**: `is`、`has`、`can`开头

### 4.3 变量命名
- 使用驼峰命名法
- 布尔类型变量以`is`、`has`、`can`开头
- 集合类型变量以`List`、`Map`、`Set`结尾

## 5. 注解使用规范

### 5.1 Lombok注解
```java
@Data                    // 生成getter/setter/toString/equals/hashCode
@Builder                 // 生成建造者模式
@NoArgsConstructor      // 生成无参构造器
@AllArgsConstructor     // 生成全参构造器
@EqualsAndHashCode(callSuper = true)  // 继承类使用
```

### 5.2 Spring注解
```java
@RestController         // REST控制器
@RequestMapping        // 请求映射
@Autowired            // 依赖注入
@Service              // 服务层
@Transactional        // 事务管理
@Valid                // 参数校验
```

### 5.3 Jackson注解
```java
@JsonIgnoreProperties(ignoreUnknown = true)  // 忽略未知属性
@JsonProperty         // 属性映射
@JsonFormat          // 日期格式化
```

## 6. 代码编写规范

### 6.1 类结构顺序
```java
public class ExampleClass {
    // 1. 静态常量
    private static final String CONSTANT = "value";
    
    // 2. 实例变量
    private String field;
    
    // 3. 构造方法
    public ExampleClass() {}
    
    // 4. 静态方法
    public static void staticMethod() {}
    
    // 5. 实例方法
    public void instanceMethod() {}
    
    // 6. getter/setter方法
    public String getField() { return field; }
}
```

### 6.2 异常处理
```java
// 业务异常抛出
if (StringUtil.isEmpty(userName)) {
    throw new UserException("60001", "用户名不能为空");
}

// 统一异常响应
@ExceptionHandler(UserException.class)
public MsResponse<Object> handleUserException(UserException e) {
    return MsResponse.error(e.getCode(), e.getMessage());
}
```

### 6.3 日志规范
```java
// 使用Slf4j
private static final Logger logger = LoggerFactory.getLogger(UserService.class);

// 日志级别使用
logger.debug("调试信息: {}", debugInfo);
logger.info("业务信息: {}", businessInfo);
logger.warn("警告信息: {}", warnInfo);
logger.error("错误信息: {}", errorInfo, exception);
```

## 7. 数据库访问规范

### 7.1 Mapper接口
```java
@Mapper
public interface UserMapper {
    int addUser(UserInfoDAO userInfoDAO);
    UserInfoDAO getUserById(@Param("userId") String userId);
    List<UserInfoDAO> getUserList(GetUserCondition condition);
    int updateUser(UserInfoDAO userInfoDAO);
    int deleteUser(@Param("userId") String userId);
}
```

### 7.2 SQL编写规范
- 使用参数化查询防止SQL注入
- 复杂查询使用XML配置
- 分页查询使用PageHelper
- 字段名使用下划线命名

## 8. 接口设计规范

### 8.1 统一返回格式
```java
@Data
@Builder
public class MsResponse<T> {
    private String code;      // 响应码
    private String message;   // 响应消息
    private T data;          // 响应数据
    private Long timestamp;   // 时间戳
}
```

### 8.2 请求参数校验
```java
public class AddUserRequest extends MsBaseRequest {
    @NotEmpty(message = "用户名不能为空")
    private String userName;
    
    @Email(message = "邮箱格式不正确")
    private String email;
}
```

## 9. 工具类使用规范

### 9.1 字符串处理
```java
// 使用项目自定义工具类
StringUtil.isEmpty(str)
StringUtil.isNotEmpty(str)
StringUtil.jsonEncode(object)
StringUtil.myMd5(password)
```

### 9.2 时间处理
```java
// 使用项目时间工具类
TimeUtil.getCurrentTime()
TimeUtil.formatDateTime(dateTime)
```

## 10. 代码质量要求

### 10.1 代码复杂度
- 方法行数不超过50行
- 类行数不超过500行
- 圈复杂度不超过10

### 10.2 注释规范
```java
/**
 * 添加用户
 * @param userName 用户名
 * @param password 密码
 * @param extraContent 扩展内容
 * @return 用户信息
 * @throws UserException 用户异常
 */
public UserDTO addUser(String userName, String password, Map<String, Object> extraContent) {
    // 业务逻辑实现
}
```

### 10.3 单元测试
- 核心业务方法必须编写单元测试
- 测试覆盖率不低于70%
- 使用JUnit 5 + Mockito

## 11. 性能优化规范

### 11.1 数据库优化
- 避免N+1查询问题
- 合理使用索引
- 分页查询大数据集
- 使用Redis缓存热点数据

### 11.2 代码优化
- 避免在循环中进行数据库操作
- 合理使用集合操作
- 及时关闭资源
- 使用StringBuilder拼接字符串

## 12. 安全规范

### 12.1 数据安全
- 敏感信息加密存储
- 密码使用MD5加密
- 防止SQL注入
- 输入参数校验

### 12.2 接口安全
- 重要接口添加权限校验
- 防止重复提交
- 限制请求频率
- 记录操作日志
