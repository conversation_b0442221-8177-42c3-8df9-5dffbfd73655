# 用户微服务文档

## 项目简介

### 项目名称

- 用户微服务

### 项目背景

- 随着公司业务项目逐渐增加，每个业务层都需要开发用户类功能，建立用户微服务后，可减少用户类功能的重复工作

### 主要功能

- 用户微服务对外提供用户管理、登录验证、权限管理、组织架构管理、好友管理、群聊管理、标签管理等功能

### 业务关系图



## 开发环境

### 系统环境

- Java VERSION 18.0.2
- MySQL VERSION 8.0.27
- Redis VERSION 6.2.6

### SDK

- MSSDK VERSION 1.0.0

### 依赖

- SpringBoot VERSION 2.3.2.RELEASE
- MyBatis VERSION 2.2.2
- okhttp3 VERSION 4.4.1
- lombok VERSION 1.18.26

## 目录结构

### 项目目录

| **目录名称** | **目录说明**       | **文件说明**                      |
| ------------ | ------------------ | --------------------------------- |
| config       | 配置文件目录       | application.properties 等配置文件 |
| docs         | 文档目录           | 说明文档目录                      |
| release      | 历史版本文件目录   | 保存各个历史版本的jar包           |
| sql          | 数据库脚本文件目录 | 初始化sql文件                     |
| src          | 开发文件目录       | 开发文件                          |

### 开发目录

| **目录名称** | **目录说明**   | **文件说明**                                           |
| ------------ | -------------- | ------------------------------------------------------ |
| annotation   | 自定义注解文件 | 自定义注解文件，例如：操作日志注解                     |
| aspect       | 切面类文件     | 切面类文件，例如：请求日志切面文件                     |
| common       | 公共类文件     | 公共类文件，例如：公共父类文件、异常处理类文件         |
| config       | 配置类文件     | 配置类文件                                             |
| constant     | 常量类文件     | 常量类文件                                             |
| controller   | 控制器类文件   | 控制器类文件                                           |
| entity       | 实体类文件     | 实体类文件，例如：请求参数类、返回参数类、数据库映射类 |
| exception    | 异常类文件     | 自定义异常类文件                                       |
| mapper       | 数据表映射类   | 数据表映射类                                           |
| service      | 业务逻辑实现类 | 业务逻辑实现类                                         |
| util         | 工具类         | 工具类                                                 |

### 运行目录

| **目录名称** | **目录说明** | **文件说明**                      |
| ------------ | ------------ | --------------------------------- |
| config       | 配置文件     | application.properties 等配置文件 |
| log          | 日志文件     | 日志文件目录                      |
| ms-user.jar  | 运行文件     | 微服务运行文件                    |



## 配置文件

```properties
# -----------服务配置-----------
# 应用名称
spring.application.name=MicroService-User
# 端口号
server.port=9717
# 应用渠道号
server.appChannelId=b1f2ee32-6903-fb60-a011-823c34e37aa9
# 数据渠道号
server.dataChannelId=0cac5607-91d7-f67a-e1bc-86dc54551d2a
# 接口密钥
server.apiKey=
# 日志级别(仅记录大于或等于此级别的日志) 1:debug, 2:info, 3:warn, 4:error, 5:fatal
server.logLevel=1
logging.pattern.console=[%d{yyyy-MM-dd HH:mm:ss.SSS}][%level]%msg[%logger][%thread]%n
logging.pattern.file=[%d{yyyy-MM-dd HH:mm:ss.SSS}][%level]%msg[%logger][%thread]%n
logging.file.path=./log/
logging.file.name=./log/ms-user.log
logging.level.com.microservice.user.mapper=debug
# -----------数据库配置-----------
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://127.0.0.1:3306/ms_user?serverTimezone=GMT%2B8
spring.datasource.username=root
spring.datasource.password=123456
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.mapper-locations=classpath:mybatis/mapper/*.xml
# -----------redis配置-----------
spring.redis.host=*************
spring.redis.port=6379
spring.redis.password=YiyouDev+2021
# -----------微服务配置-----------
# 日志微服务地址
ms.msLogDomain=http://draft.eeeyou.cn:7100/
```

## 上线说明

1. 导入数据库

    1. 新建数据库ms_user
    2. 导入ms_user.sql

2. 修改配置文件

    1. 复制配置文件内容到/config/application.properties
    2. 修改端口号、数据库配置、微服务依赖

3. 部署服务

    1. 下载docker镜像

       ```bash
       docker pull openjdk
       ```

    2. 运行镜像

       ```bash
       docker run \
         --detach \
         --publish server-port:container-port \
         --name container-name \
         --restart unless-stopped \
         -v /mnt/dir-name:/usr/local/dir-name \
         image-name
       ```

    3. 进入容器启动服务

       ```bash
       java -jar ms-user.jar --spring.config.location=/config/application.properties
       ```

4. 测试服务

    - 测试接口：http://draft.eeeyou.cn:server-port/ping

## 数据表

**基础表**

1. **用户表（user_info）**

| **序号** | **字段名称**    | **字段类型** | **字段说明**           | **索引** |
| -------- | --------------- | ------------ | ---------------------- | -------- |
| **1**    | user_id         | char(36)     | 用户id，36位uuid       | PK       |
| **2**    | user_name       | varchar(32)  | 用户名                 | KEY      |
| **3**    | extra_content   | json         | 保留域，不处理直接返回 |          |
| **4**    | sort            | int          | 排序字段               |          |
| **5**    | data_channel_id | char(36)     | 数据渠道号             |          |
| **6**    | app_channel_id  | char(36)     | 应用渠道号             |          |
| **7**    | create_time     | datetime     | 创建时间               |          |
| **8**    | update_time     | datetime     | 更新时间               |          |



2. **用户身份表（identity_info）**

| **序号** | **字段名称**    | **字段类型** | **字段说明**           | **索引** |
| -------- | --------------- | ------------ | ---------------------- | -------- |
| **1**    | identity_id     | char(36)     | 用户身份id，36位uuid   | PK       |
| **2**    | identity_name   | varchar(32)  | 用户身份名称           |          |
| **3**    | user_id         | char(36)     | 用户id，36位uuid       | KEY      |
| **4**    | extra_content   | json         | 保留域，不处理直接返回 |          |
| **5**    | sort            | int          | 排序字段               |          |
| **6**    | data_channel_id | char(36)     | 数据渠道号             |          |
| **7**    | app_channel_id  | char(36)     | 应用渠道号             |          |
| **8**    | create_time     | datetime     | 创建时间               |          |
| **9**    | update_time     | datetime     | 更新时间               |          |



3. **用户组表（group_info）**

| **序号** | **字段名称**    | **字段类型** | **字段说明**           | **索引** |
| -------- | --------------- | ------------ | ---------------------- | -------- |
| **1**    | group_id        | char(36)     | 用户组id，36位uuid     | PK       |
| **2**    | group_name      | varchar(30)  | 用户组名               | KEY      |
| **3**    | extra_content   | json         | 保留域，不处理直接返回 |          |
| **4**    | sort            | int          | 排序字段               |          |
| **5**    | data_channel_id | char(36)     | 数据渠道号             |          |
| **6**    | app_channel_id  | char(36)     | 应用渠道号             |          |
| **7**    | create_time     | datetime     | 创建时间               |          |
| **8**    | update_time     | datetime     | 更新时间               |          |



4. **用户登录状态表（token_info）**

| **序号** | **字段名称**    | **字段类型** | **字段说明**                  | **索引** |
| -------- | --------------- | ------------ | ----------------------------- | -------- |
| **1**    | token_id        | char(36)     | 主键                          | PK       |
| **2**    | token           | varchar(50)  | 用户登录token                 | KEY      |
| **3**    | user_id         | char(36)     | 用户id，36位uuid              | KEY      |
| **4**    | identity_id     | char(36)     | 用户身份id，36位uuid          | KEY      |
| **5**    | ip              | varchar(32)  | IP地址                        |          |
| **6**    | end_timestamp   | int unsigned | 有效期截止时间                |          |
| **7**    | token_status    | char(5)      | token状态码（详见配置项说明） |          |
| **8**    | app_channel_id  | char(36)     | 渠道id                        |          |
| **9**    | data_channel_id | char(36)     | 数据渠道号                    |          |
| **10**   | create_time     | datetime     | 创建时间                      |          |
| **11**   | update_time     | datetime     | 更新时间                      |          |



5. **公司表（company_info）**

| **序号** | **字段名**      | **字段类型** | **字段说明**     | **索引** |
| -------- | --------------- | ------------ | ---------------- | -------- |
| **1**    | company_id      | char(36)     | 公司id，36位uuid | PK       |
| **2**    | company_name    | varchar(64)  | 公司名称         | KEY      |
| **3**    | parent_id       | char(36)     | 上级id           |          |
| **4**    | extra_content   | json         | 公司信息         |          |
| **5**    | sort            | int          | 排序字段         |          |
| **6**    | data_channel_id | char(36)     | 数据渠道号       |          |
| **7**    | app_channel_id  | char(36)     | 应用渠道号       |          |
| **8**    | create_time     | datetime     | 创建时间         |          |
| **9**    | update_time     | datetime     | 更新时间         |          |



6. **部门表（department_info）**

| **序号** | **字段名**      | **字段类型** | **字段说明**     | **索引** |
| -------- | --------------- | ------------ | ---------------- | -------- |
| **1**    | department_id   | char(36)     | 部门id，36位uuid | PK       |
| **2**    | department_name | varchar(64)  | 部门名称         | KEY      |
| **3**    | parent_id       | char(36)     | 上级id           |          |
| **4**    | sort            | int unsigned | 显示顺序         |          |
| **5**    | extra_content   | json         | 部门信息         |          |
| **6**    | create_time     | datetime     | 创建时间         |          |
| **7**    | update_time     | datetime     | 更新时间         |          |



7. **职位表（position_info）**

| **序号** | **字段名**    | **字段类型** | **字段说明**     | **索引** |
| -------- | ------------- | ------------ | ---------------- | -------- |
| **1**    | position_id   | char(36)     | 职位id，36位uuid | PK       |
| **2**    | position_name | varchar(64)  | 职位名称         | KEY      |
| **3**    | parent_id     | char(36)     | 上级id           |          |
| **4**    | sort          | int unsigned | 显示顺序         |          |
| **5**    | extra_content | json         | 职位信息         |          |
| **6**    | create_time   | datetime     | 创建时间         |          |
| **7**    | update_time   | datetime     | 更新时间         |          |



8. **群聊表（chat_group_info）**

| **序号** | **字段名**      | **字段类型** | **字段说明** | **索引** |
| -------- | --------------- | ------------ | ------------ | -------- |
| **1**    | chat_group_id   | char(36)     | 群聊id       | PK       |
| **2**    | chat_group_name | varchar(32)  | 群聊名称     | KEY      |
| **3**    | extra_content   | json         | 扩展信息     |          |
| **4**    | sort            | int          | 排序字段     |          |
| **5**    | app_channel_id  | char(36)     | 应用渠道号   |          |
| **6**    | data_channel_id | char(36)     | 数据渠道号   |          |
| **7**    | create_time     | datetime     | 创建时间     |          |
| **8**    | update_time     | datetime     | 更新时间     |          |



9. **标签表（entity_info）**

| **序号** | **字段名**      | **字段类型** | **字段说明** | **索引** |
| -------- | --------------- | ------------ | ------------ | -------- |
| **1**    | entity_id       | char(36)     | 标签id       | PK       |
| **2**    | entity_name     | varchar(128) | 标签名称     | KEY      |
| **3**    | type            | varchar(32)  | 标签类别     |          |
| **4**    | extra_content   | json         | 扩展信息     |          |
| **5**    | sort            | int          | 排序字段     |          |
| **6**    | app_channel_id  | char(36)     | 应用渠道号   |          |
| **7**    | data_channel_id | char(36)     | 数据渠道号   |          |
| **8**    | create_time     | datetime     | 创建时间     |          |
| **9**    | update_time     | datetime     | 更新时间     |          |



10. **日志表（log_info）**

| **序号** | **字段名**      | **字段类型** | **字段说明** | **索引** |
| -------- | --------------- | ------------ | ------------ | -------- |
| **1**    | id              | int          | 主键，自增id | PK       |
| **2**    | type            | int          | 日志类型     |          |
| **3**    | opt             | varchar(64)  | 操作类型     |          |
| **4**    | content         | varchar(255) | 日志内容     |          |
| **5**    | extra_content   | json         | 扩展信息     |          |
| **6**    | app_channel_id  | char(36)     | 应用渠道号   |          |
| **7**    | data_channel_id | char(36)     | 数据渠道号   |          |
| **8**    | create_time     | datetime     | 创建时间     |          |



**关系表**

11. **用户身份和公司关系表（identity_company_relation）**

| **序号** | **字段名称**  | **字段类型** | **字段说明**         | **索引** |
| -------- | ------------- | ------------ | -------------------- | -------- |
| **1**    | identity_id   | char(36)     | 用户身份id，36位uuid | KEY      |
| **2**    | company_id    | char(36)     | 公司id，36位uuid     | KEY      |
| **3**    | extra_content | json         | 扩展信息             |          |
| **4**    | create_time   | datetime     | 创建时间             |          |
| **5**    | update_time   | datetime     | 更新时间             |          |



12. **用户身份部门关系表（identity_department_relation）**

| **序号** | **字段名称**  | **字段类型** | **字段说明**         | **索引** |
| -------- | ------------- | ------------ | -------------------- | -------- |
| **1**    | identity_id   | char(36)     | 用户身份id，36位uuid | KEY      |
| **2**    | department_id | char(36)     | 部门id，36位uuid     | KEY      |
| **3**    | extra_content | json         | 扩展信息             |          |
| **4**    | create_time   | datetime     | 创建时间             |          |



13. **用户身份职位关系表（identity_position_relation）**

| **序号** | **字段名称**  | **字段类型** | **字段说明**         | **索引** |
| -------- | ------------- | ------------ | -------------------- | -------- |
| **1**    | identity_id   | char(36)     | 用户身份id，36位uuid | KEY      |
| **2**    | position_id   | char(36)     | 职位id，36位uuid     | KEY      |
| **3**    | extra_content | json         | 扩展信息             |          |
| **4**    | create_time   | datetime     | 创建时间             |          |



14. **用户身份和用户组关系表（identity_group_relation）**

| **序号** | **字段名称** | **字段类型** | **字段说明**         | **索引** |
| -------- | ------------ | ------------ | -------------------- | -------- |
| **1**    | identity_id  | char(36)     | 用户身份id，36位uuid | KEY      |
| **2**    | group_id     | char(36)     | 用户组id，36位uuid   | KEY      |
| **3**    | create_time  | datetime     | 创建时间             |          |



15. **用户组和事件关系表（group_event_relation）**

| **序号** | **字段名称** | **字段类型** | **字段说明**       | **索引** |
| -------- | ------------ | ------------ | ------------------ | -------- |
| **1**    | group_id     | char(36)     | 用户组id，36位uuid | KEY      |
| **2**    | event_id     | char(36)     | 事件id，36位uuid   | KEY      |
| **3**    | create_time  | datetime     | 创建时间           |          |



16. **用户身份好友表（identity_friend_relation）**

| **序号** | **字段名**         | **字段类型** | **字段说明**         | **索引** |
| -------- | ------------------ | ------------ | -------------------- | -------- |
| **1**    | identity_id        | char(36)     | 用户身份id，36位uuid | KEY      |
| **2**    | target_identity_id | char(36)     | 好友身份id           | KEY      |
| **3**    | extra_content      | json         | 扩展信息             |          |
| **4**    | app_channel_id     | char(36)     | 应用渠道号           |          |
| **5**    | data_channel_id    | char(36)     | 数据渠道号           |          |
| **6**    | create_time        | datetime     | 创建时间             |          |



17. **公司和部门关系表（company_department_relation）**

| **序号** | **字段名称**  | **字段类型** | **字段说明**     | **索引** |
| -------- | ------------- | ------------ | ---------------- | -------- |
| **1**    | company_id    | char(36)     | 公司id，36位uuid | KEY      |
| **2**    | department_id | char(36)     | 部门id，36位uuid | KEY      |
| **3**    | create_time   | datetime     | 创建时间         |          |



18. **公司和职位关系表（company_position_relation）**

| **序号** | **字段名称** | **字段类型** | **字段说明**     | **索引** |
| -------- | ------------ | ------------ | ---------------- | -------- |
| **1**    | company_id   | char(36)     | 公司id，36位uuid | KEY      |
| **2**    | position_id  | char(36)     | 职位id，36位uuid | KEY      |
| **3**    | create_time  | datetime     | 创建时间         |          |



19. **部门和职位关系表（department_position_relation）**

| **序号** | **字段名称**  | **字段类型** | **字段说明**     | **索引** |
| -------- | ------------- | ------------ | ---------------- | -------- |
| **1**    | department_id | char(36)     | 部门id，36位uuid | KEY      |
| **2**    | position_id   | char(36)     | 职位id，36位uuid | KEY      |
| **3**    | create_time   | datetime     | 创建时间         |          |



20. **群成员表（identity_chat_group_relation）**

| **序号** | **字段名**    | **字段类型** | **字段说明** | **索引** |
| -------- | ------------- | ------------ | ------------ | -------- |
| **1**    | identity_id   | char(36)     | 用户身份id   | KEY      |
| **2**    | chat_group_id | char(36)     | 群聊id       | KEY      |
| **3**    | extra_content | json         | 扩展信息     |          |
| **4**    | create_time   | datetime     | 创建时间     |          |
| **5**    | update_time   | datetime     | 更新时间     |          |



21. **标签关系表（entity_relation）**

| **序号** | **字段名**       | **字段类型** | **字段说明** | **索引** |
| -------- | ---------------- | ------------ | ------------ | -------- |
| **1**    | entity_id        | char(36)     | 标签id       | KEY      |
| **2**    | target_entity_id | char(36)     | 目标标签id   | KEY      |
| **3**    | extra_content    | json         | 扩展信息     |          |
| **4**    | create_time      | datetime     | 创建时间     |          |
| **5**    | update_time      | datetime     | 更新时间     |          |



## 接口文档

### 接口说明

**微服务域名：** **http://domain:7030/**

### 请求头

| **序号** | **请求参数**      | **参数类型** | **是否必传** | **参数说明** |
| -------- | ----------------- | ------------ | ------------ | ------------ |
| **1**    | x-app_channel_id  | String       | 是           | 应用渠道号   |
| **2**    | x-data_channel_id | String       | 是           | 数据渠道号   |
| **3**    | x-real_ip         | String       | 否           | 客户端ip地址 |
| **4**    | x-api_key         | String       | 否           | 接口加密密钥 |
| **5**    | x-trace_id        | String       | 否           | 日志跟踪id   |



### 用户实体类

#### 添加用户 / 注册

- 接口描述：创建新用户，同时创建用户默认身份
- 请求地址：/user/addUser
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型**        | **是否必传** | **参数说明**                                    |
| -------- | ------------- | ------------------- | ------------ | ----------------------------------------------- |
| **1**    | userName      | String              | 是           | 用户名                                          |
| **2**    | password      | String              | 否           | 密码，不传默认生成随机密码                      |
| **3**    | groupIdList   | List<String>        | 否           | 用户组id列表，默认：空                          |
| **4**    | extraContent  | Map<String, Object> | 否           | 其他用户信息，服务端不处理直接返回              |
| **5**    | appChannelId  | String              | 否           | 用户所属渠道号不传则取header的x-app_channel_id  |
| **6**    | dataChannelId | String              | 否           | 用户所属渠道号不传则取header的x-data_channel_id |

- 请求示例

```json
{
	"userName": "18500040117",
	"password": "3a4922277552a2702f40e2e51fcd81ac",
	"groupIdList": ["97d00597-186f-4089-b20f-7a3563b6b2ed", "baa25ec2-6965-8fb1-9625-fba6a7209b07"],
	"extraContent": {
		"avatar": "https://erp.eeeyou.cn/default-avatar.png",
		"nickname": "YY_wJ3tX5xF"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | UserEntity   | 返回数据     |

- 返回示例

```json
{
	"time": "2023-01-02 01:29:53.346",
	"code": "00000",
	"data": {
		"identityId": "b50957c1-6991-d4e3-1a90-ef1f27c92f66",
		"userName": "18500040117",
		"createTime": "2023-01-02 01:29:53",
		"extraContent": {
			"avatar": "https://erp.eeeyou.cn/default-avatar.png",
			"nickname": "YY_wJ3tX5xF"
		},
		"identityList": [{
			"identityId": "b50957c1-6991-d4e3-1a90-ef1f27c92f66",
			"userId": "60c57771-a866-957d-73d7-5547622e56cb",
			"companyId": "",
			"companyName": "个人账户",
			"departmentIdList": [],
			"positionIdList": [],
			"isSelected": 1,
			"groupIdList": []
		}],
		"groupIdList": [],
		"eventIdList": [],
		"appChannelId": "a07deab0-9280-d197-6115-089d71ae98e7",
		"dataChannelId": "866a7beb-af41-faaf-c557-7edff945e87b"
	},
	"message": "请求成功"
}
```

#### 修改用户

- 接口描述：修改用户个人身份的信息
- 请求地址：/user/updateUser
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型**        | **是否必传** | **参数说明**                                                 |
| -------- | ------------- | ------------------- | ------------ | ------------------------------------------------------------ |
| **1**    | identityId    | String              | 是           | 用户身份id                                                   |
| **2**    | password      | String              | 否           | 密码，不更新此字段传null或不传，默认：null                   |
| **3**    | groupIdList   | List<String>        | 否           | 用户组id列表，不更新此字段传null或不传，默认：null           |
| **5**    | extraContent  | Map<String, Object> | 否           | 其他用户信息，服务端不处理直接返回，不更新此字段传null或不传，默认：null |
| **6**    | appChannelId  | String              | 否           | 用户所属渠道号不传则取header的x-app_channel_id               |
| **7**    | dataChannelId | String              | 否           | 用户所属渠道号不传则取header的x-data_channel_id              |

- 请求示例

```json
{
	"identityId": "d3032602-b60f-5db3-ab10-535a1aaa2b55",
	"extraContent": {
		"nickname": "YY_87654321"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | UserEntity   | 返回数据     |

- 返回示例

```json
{
	"time": "2023-01-02 01:56:10.814",
	"code": "00000",
	"data": {
		"userId": "d3032602-b60f-5db3-ab10-535a1aaa2b55",
		"identityId": "262bd03e-bc32-dbe1-89b6-977fed3281cd",
		"userName": "18500040131",
		"isFreeze": 0,
		"createTime": "2023-01-02 00:05:02",
		"extraContent": {
			"nickname": "YY_87654321"
		},
		"identityList": [{
				"identityId": "262bd03e-bc32-dbe1-89b6-977fed3281cd",
				"userId": "d3032602-b60f-5db3-ab10-535a1aaa2b55",
				"companyId": "",
				"companyName": "个人账户",
				"departmentIdList": [],
				"positionIdList": [],
				"isSelected": 1,
				"groupIdList": []
			}
		],
		"groupIdList": [],
		"eventIdList": [],
		"appChannelId": "yy593453ed6c9034",
		"dataChannelId": "123321123321"
	},
	"message": "请求成功"
}
```

#### 删除用户

- 接口描述：只能删除没有公司身份的用户，删除用户同时删除用户和用户组关系
- 请求地址：/user/deleteUser
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明** |
| -------- | ------------ | ------------ | ------------ | ------------ |
| **1**    | identityId   | String       | 是           | 用户身份id   |

- 请求示例

```json
{
	"identityId": "********-b3f7-32dd-cb4d-3861bfe33444"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | UserEntity   | 返回数据     |

- 返回示例

```json
{
	"time": "2022-09-01 11:59:06.443",
	"code": "00000",
	"data": {
		"userId": "********-b3f7-32dd-cb4d-3861bfe33444"
	},
	"message": "请求成功"
}
```

#### 获取用户列表

- 请求地址：/user/getUserList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------ | ------------ | ------------------------------------------------------------ |
| **1**    | page         | int          | 是           | 页码，默认：1                                                |
| **2**    | limit        | int          | 是           | 每页条数，默认：10，传0返回全部                              |
| **3**    | condition    | UserEntity   | 是           | 查询参数支持直接查询的参数：userIduserIdListuserNameidentityIdListappChannelIddataChannelId其他参数一律按json方式查询 |
| **4**    | isSimple     | int          | 否           | 是否返回简版用户实体[1:是,0:否]，只需要用户基本信息时建议传此参数，提高效率 |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"identityIdList": [
			"fb0dd0b6-936f-2c56-fcc8-618cb7e7c9e6",
			"336d67fb-3c5e-a7f9-072c-1b2f16a87aa0"
		]
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- ListData

| **序号** | **字段名称** | **字段类型**     | **字段说明** |
| -------- | ------------ | ---------------- | ------------ |
| **1**    | dataCount    | int              | 总数量       |
| **2**    | dataList     | List<UserEntity> | 数据列表     |

- 返回示例

```json
{
	"time": "2022-08-31 11:29:41.787",
	"code": "00000",
	"data": {
		"dataList": [{
			"userId": "fb0dd0b6-936f-2c56-fcc8-618cb7e7c9e6",
			"userName": "18500040115",
			"phone": "18500040115",
			"createTime": "2022-08-29 14:24:07",
			"updateTime": "1900-01-01 00:00:00",
			"extraContent": {
				"avatar": "https://erp.eeeyou.cn/images/default-avatar.png",
				"nickname": "YY_2dJ4kU9b"
			},
			"identityList": [],
			"eventList": []
		}],
		"dataCount": 1
	},
	"message": "请求成功"
}
```

#### 获取用户组内用户列表

- 请求地址：/user/getGroupUserList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                   |
| -------- | ------------ | ------------ | ------------ | ------------------------------ |
| **1**    | page         | int          | 是           | 页码，默认：1                  |
| **2**    | limit        | int          | 是           | 每页条数，默认：0，传0返回全部 |
| **3**    | groupId      | String       | 是           | 用户组id                       |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"groupId": "a5b53682-7f00-9d9b-6024-154e97d447ea"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**     | **字段说明** |
| -------- | ------------ | ---------------- | ------------ |
| **1**    | dataCount    | int              | 总数量       |
| **2**    | dataList     | List<UserEntity> | 数据列表     |

- 返回示例

```json
{
	"time": "2023-05-24 17:01:59.236",
	"code": "00000",
	"data": {
		"dataList": [{
			"userId": "5a0cc5d3-cd8b-5e7a-98e6-61dcca3caa53",
			"identityId": "12e0d47e-488f-2238-811e-c6098bbc6cee",
			"token": "",
			"userName": "15350612089",
			"isFreeze": 0,
			"createTime": "2023-05-24 10:08:05",
			"extraContent": {
				"avatar": "https://erp.eeeyou.cn/default-avatar.png",
				"nickname": "YY_4nF1vX7k"
			},
			"identityList": [{
				"token": "",
				"identityId": "12e0d47e-488f-2238-811e-c6098bbc6cee",
				"userId": “5a0cc5d3-cd8b-5e7a-98e6-61dcca3caa53"
			}],
			"groupIdList": ["87835bfa-c13a-e6c2-9b95-1cb760dec8f7"],
			"eventIdList": [],
			"lastLoginTime": "",
			"lastLoginIp": "",
			"appChannelId": "40ec7186-4a76-ecc0-315c-d1ec47805aef",
			"dataChannelId": "48e7bb43-2780-dfeb-989a-fed41c9df910"
		}],
		"dataCount": 4
	},
	"message": "请求成功"
}
```

#### 获取事件权限用户列表

- 应用场景：用权限id查询有此权限的用户列表
- 请求地址：/user/getEventUserList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明** |
| -------- | ------------ | ------------ | ------------ | ------------ |
| **1**    | eventId      | String       | 是           | 事件权限id   |
| **2**    | companyId    | String       | 否           | 公司id       |

- 请求示例

```json
{
	"eventId": "4a7cf212-7017-1ca8-c0cc-dda2281b3cb0"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**     | **字段说明** |
| -------- | ------------ | ---------------- | ------------ |
| **1**    | code         | String           | 返回状态码   |
| **2**    | time         | String           | 请求返回时间 |
| **3**    | message      | String           | 返回说明     |
| **4**    | data         | List<UserEntity> | 返回数据     |

- 返回示例

```json
{
	"time": "2022-08-31 11:29:41.787",
	"code": "00000",
	"data": [{
		"userId": "fb0dd0b6-936f-2c56-fcc8-618cb7e7c9e6",
		"userName": "18500040115",
		"phone": "18500040115",
		"createTime": "2022-08-29 14:24:07",
		"updateTime": "1900-01-01 00:00:00",
		"extraContent": {
			"avatar": "https://erp.eeeyou.cn/images/default-avatar.png",
			"nickname": "YY_2dJ4kU9b"
		},
		"identityList": [],
		"eventList": []
	}],
	"message": "请求成功"
}
```

### 用户身份实体关系类接口

#### 添加用户身份

- 请求地址：/identity/addIdentity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型**                 | **是否必传** | **参数说明**                                    |
| -------- | ------------- | ---------------------------- | ------------ | ----------------------------------------------- |
| **1**    | identityId    | String                       | 是           | 用户身份id                                      |
| **2**    | identityName  | String                       | 否           | 用户身份名称                                    |
| **3**    | relationList  | List<IdentityRelationEntity> | 否           | 关联关系列表                                    |
| **4**    | extraContent  | Map<String, Object>          | 否           | 其他用户信息，服务端不处理直接返回              |
| **5**    | sort          | Integer                      | 否           | 排列顺序                                        |
| **6**    | appChannelId  | String                       | 否           | 用户所属渠道号不传则取header的x-app_channel_id  |
| **7**    | dataChannelId | String                       | 否           | 用户所属渠道号不传则取header的x-data_channel_id |

- 请求示例

```json
{
	"identityId": "031a38a2-2671-4a41-b2f8-45bc762a3c46",
	"identityName": "新身份",
	"sort": 0,
	"extraContent": {
		"abc": "123"
	},
	"relationList": [{
		"relateType": "company",
		"relateIdList": ["7bc68f9e-4ee6-1bbb-40fa-f9857b729f09"]
	}]
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | IdentityEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-01-02 01:29:53.346",
	"code": "00000",
	"data": {
		"userId": "60c57771-a866-957d-73d7-5547622e56cb",
		"identityId": "b50957c1-6991-d4e3-1a90-ef1f27c92f66",
		"userName": "18500040117",
		"isFreeze": 0,
		"createTime": "2023-01-02 01:29:53",
		"extraContent": {
			"avatar": "https://erp.eeeyou.cn/default-avatar.png",
			"nickname": "YY_wJ3tX5xF"
		},
		"identityList": [
			{
				"identityId": "b50957c1-6991-d4e3-1a90-ef1f27c92f66",
				"userId": "60c57771-a866-957d-73d7-5547622e56cb",
				"companyId": "",
				"companyName": "个人账户",
				"departmentIdList": [],
				"positionIdList": [],
				"isSelected": 1,
				"groupIdList": []
			}
		],
		"groupIdList": [],
		"eventIdList": [],
		"appChannelId": "a07deab0-9280-d197-6115-089d71ae98e7",
		"dataChannelId": "866a7beb-af41-faaf-c557-7edff945e87b"
	},
	"message": "请求成功"
}
```

#### 修改用户身份

- 请求地址：/identity/updateIdentity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**                 | **是否必传** | **参数说明**                       |
| -------- | ------------ | ---------------------------- | ------------ | ---------------------------------- |
| **1**    | identityId   | String                       | 是           | 用户身份id                         |
| **2**    | identityName | String                       | 否           | 用户身份名称                       |
| **3**    | relationList | List<IdentityRelationEntity> | 否           | 关联关系列表                       |
| **4**    | extraContent | Map<String, Object>          | 否           | 其他用户信息，服务端不处理直接返回 |
| **5**    | sort         | Integer                      | 否           | 排列顺序                           |

- 请求示例

```json
{
	"userId": "d3032602-b60f-5db3-ab10-535a1aaa2b55",
	"companyId": "3a492227-7552-a270-2f40-e2e51fcd81ac",
	"departmentIdList": [
		"11d00597-186f-4089-b20f-7a3563b6b2ed",
		"11a25ec2-6965-8fb1-9625-fba6a7209b07"
	],
	"positionIdList": [
		"220e7a80-f4bb-b37c-3aae-7e850c554d3d",
		"22d75841-82b7-de54-97c4-992cd9dfa3f2"
	],
	"extraContent": {
		"nickname": "YY_87654321"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | IdentityEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-01-02 01:56:10.814",
	"code": "00000",
	"data": {
		"userId": "d3032602-b60f-5db3-ab10-535a1aaa2b55",
		"identityId": "262bd03e-bc32-dbe1-89b6-977fed3281cd",
		"userName": "18500040131",
		"isFreeze": 0,
		"createTime": "2023-01-02 00:05:02",
		"extraContent": {
			"nickname": "YY_87654321"
		},
		"identityList": [{
				"identityId": "262bd03e-bc32-dbe1-89b6-977fed3281cd",
				"userId": "d3032602-b60f-5db3-ab10-535a1aaa2b55",
				"companyId": "",
				"companyName": "个人账户",
				"departmentIdList": [],
				"positionIdList": [],
				"isSelected": 1,
				"groupIdList": []
			}
		],
		"groupIdList": [],
		"eventIdList": [],
		"appChannelId": "yy593453ed6c9034",
		"dataChannelId": "123321123321"
	},
	"message": "请求成功"
}
```

#### 删除用户身份

- 接口描述：删除用户身份，同时删除用户和部门、职位、用户组的关系
- 请求地址：/identity/deleteIdentity

- 请求方式：POST / JSON

- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明** |
| -------- | ------------ | ------------ | ------------ | ------------ |
| **1**    | identityId   | String       | 是           | 用户身份id   |

- 请求示例

```json
{
	"identityId": "********-b3f7-32dd-cb4d-3861bfe33444"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | IdentityEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-09-01 11:59:06.443",
	"code": "00000",
	"data": {
		"userId": "********-b3f7-32dd-cb4d-3861bfe33444",
		"identityId": "3ef07662-bb9a-720b-9a3a-ba72bd0bed19",
		"userName": "18500040119",
		"phone": "18500040119",
		"channelId": "yy593453ed6c9034",
		"createTime": "2022-08-31 16:57:30",
		"updateTime": "1900-01-01 00:00:00",
		"extraContent": {
			"avatar": "https://erp.eeeyou.cn/default-avatar.png",
			"nickname": "YY_3kQ6pP5r"
		},
		"identityList": [],
		"eventList": []
	},
	"message": "请求成功"
}

```

#### 获取用户身份

- 使用场景：获取用户在某公司下的详细信息
- 请求地址：/identity/getIdentity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**     |
| -------- | ------------ | ------------ | ------------ | ---------------- |
| **1**    | identityId   | String       | 是           | 用户和公司关系id |

- 请求示例

```json
{
	"identityId": "ccf6d4d0-2c6b-401f-0166-c4513fbd63f6"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | IdentityEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-05-24 13:56:49.142",
	"code": "00000",
	"data": {
		"token": "",
		"identityId": "00b6416d-1b55-9c51-0914-b0640aa2105c",
		"userId": "ccf6d4d0-2c6b-401f-0166-c4513fbd63f6",
		"userName": "***********",
		"companyId": "c1829cc5-8983-99c5-a8a2-6946b0b3fc49",
		"companyName": "廊坊市宏丰石油销售有限公司",
		"departmentList": [],
		"departmentIdList": [],
		"positionList": [],
		"positionIdList": [],
		"isSelected": 0,
		"groupIdList": [],
		"extraContent": {
			"id": 455,
			"isMain": 0,
			"status": 1,
			"userId": "ccf6d4d0-2c6b-401f-0166-c4513fbd63f6",
			"endTime": "2099-12-31 23:59:59",
			"groupId": "0"
		},
		"eventIdList": [],
		"createTime": "2023-02-01 22:28:50",
		"lastLoginTime": "",
		"lastLoginIp": "",
		"userExtraContent": {
			"id": 100144,
			"phone": "***********",
			"birthday": "1900-01-01",
			"msUserId": "ccf6d4d0-2c6b-401f-0166-c4513fbd63f6",
			"nickname": "陈总"
		},
		"uccExtraContent": {
			"id": 455,
			"status": 1,
			"userId": “ccf6d4d0-2c6b-401f-0166-c4513fbd63f6"
		},
		"companyExtraContent": {
			"id": 86799,
			"name": "廊坊市宏丰石油销售有限公司"
		}
	},
	"message": "请求成功"
}
```

#### 获取用户身份列表

- 使用场景：获取用户身份列表、获取某公司下用户列表
- 请求地址：/identity/getIdentityList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------ | ------------ | ------------------------------------------------------------ |
| **1**    | page         | int          | 否           | 页码，默认：1                                                |
| **2**    | limit        | int          | 否           | 每页条数，默认：10，传0返回全部                              |
| **3**    | condition    | Object       | 否           | 查询参数支持直接查询的参数：userIduserIdListidentityIdidentityIdListappChannelIddataChannelId其他参数一律按json方式查询 |

- 请求示例

```json
{
	"condition": {
		"identityIdList": [
			"57895e98-7d58-9733-efeb-5422dffff27c", 
			“f01067c2-4edd-369e-c158-16f61f4a5aa7"
		]
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**             | **字段说明** |
| -------- | ------------ | ------------------------ | ------------ |
| **1**    | code         | String                   | 返回状态码   |
| **2**    | time         | String                   | 请求返回时间 |
| **3**    | message      | String                   | 返回说明     |
| **4**    | data         | DataList<IdentityEntity> | 返回数据     |

- 返回示例

```json
{
	"time": "2023-05-24 14:18:10.572",
	"code": "00000",
	"data": {
		"dataList": [{
			"token": "",
			"identityId": "00b6416d-1b55-9c51-0914-b0640aa2105c",
			"userId": "ccf6d4d0-2c6b-401f-0166-c4513fbd63f6",
			"userName": "***********",
			"companyId": "c1829cc5-8983-99c5-a8a2-6946b0b3fc49",
			"companyName": "廊坊市宏丰石油销售有限公司",
			"departmentList": [],
			"departmentIdList": [],
			"positionList": [],
			"positionIdList": [],
			"isSelected": 0,
			"groupIdList": [],
			"extraContent": {
				"id": 455,
				"status": 1,
				"userId": "ccf6d4d0-2c6b-401f-0166-c4513fbd63f6",
				"endTime": "2099-12-31 23:59:59"
			},
			"eventIdList": [],
			"createTime": "2023-02-01 22:28:50",
			"lastLoginTime": "",
			"lastLoginIp": "",
			"userExtraContent": {
				"id": 100144,
				"phone": "***********",
				"birthday": "1900-01-01",
				"isFreeze": 0,
				"msUserId": "ccf6d4d0-2c6b-401f-0166-c4513fbd63f6",
				"nickname": "陈总"
			},
			"uccExtraContent": {
				"id": 455,
				"isMain": 0,
				"status": 1,
				"userId": "ccf6d4d0-2c6b-401f-0166-c4513fbd63f6",
				"endTime": "2099-12-31 23:59:59"
			},
			"companyExtraContent": {
				"id": 86799,
				"bank": "工行光明道支行",
				"code": "C0026",
				"logo": "http:///static/www/images/app-logo-transparent.png",
				"memo": "",
				"name": "廊坊市宏丰石油销售有限公司"
			}
		}],
		"dataCount": 1
	},
	"message": "请求成功"
}
```

#### 切换身份

- 接口描述：以目标身份id重新登录系统，生成新的token
- 使用场景：APP、管理后台切换公司
- 请求地址：/identity/changeIdentity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明** |
| -------- | ------------ | ------------ | ------------ | ------------ |
| **1**    | identityId   | String       | 是           | 用户身份id   |

- 请求示例

```json
{
	"identityId": "********-b3f7-32dd-cb4d-3861bfe33444"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | UserEntity   | 返回数据     |

- 返回示例

```json
{
	"time": "2022-08-31 18:29:40.062",
	"code": "00000",
	"data": {
		"userId": "fb0dd0b6-936f-2c56-fcc8-618cb7e7c9e6",
		"identityId": "********-b3f7-32dd-cb4d-3861bfe33444",
		"token": "hbukp99ggjfjaaf7ngoru84b3q8wlos71661941780",
		"userName": "18500040114",
		"phone": "18500040114",
		"extraContent": {
			"nickname": "YY_87654321"
		},
		"identityList": [{
			"identityId": "********-b3f7-32dd-cb4d-3861bfe33444",
			"userId": "336d67fb-3c5e-a7f9-072c-1b2f16a87aa0",
			"companyId": "be0854d7-703b-45c4-0ad2-8461c852c645",
			"departmentId": "968b5697-62de-4933-da4f-3d8e33b61b34",
			"positionId": "be0854d7-703b-45c4-0ad2-8461c852c645",
			"endTime": "",
			"isSelected": 1
		}, {
			"identityId": "3ef07662-bb9a-720b-9a3a-ba72bd0bed19",
			"userId": "2878be6f-6cf0-e409-fad5-7f769d23da6a",
			"companyId": "",
			"departmentId": "",
			"positionId": "",
			"endTime": "",
			"isSelected": 0
		}],
		"eventList": [
			"819ff175-1e85-32d5-8fe9-462e22886ea8", 
			"********-b3f7-32dd-cb4d-3861bfe33444"
		]
	},
	"message": "请求成功"
}
```

### 其他类

#### 登录

- 接口描述：

    - 用户可通过用户名和密码登录

    - 如果isCreateUser=1，当用户不存在时，创建新用户

    - 如果isCreateUser=0，当用户不存在时，返回登录失败

    - 登录成功生成新的token，旧token不失效

- 执行流程：
    - 验证密码
    - 尝试从用户token表获取一条此用户登录历史记录，如果有历史记录则取上次登录的身份，否则取个人身份
    - 用户token表增加一条记录
    - 以token为key生成一条用户登录缓存
- 请求地址：/common/login

- 请求方式：POST / JSON

- 请求参数

| **序号** | **请求参数**    | **参数类型** | **是否必传** | **参数说明**                           | **备注**                         |
| -------- | --------------- | ------------ | ------------ | -------------------------------------- | -------------------------------- |
| **1**    | userName        | String       | 是           | 用户名                                 |                                  |
| **2**    | password        | String       | 否           | 密码，32位，明码md5加密，默认：空      | isCheckPassword为0时，密码非必传 |
| **3**    | isCheckPassword | int          | 否           | 是否检查密码[1:是,0:否]，默认：0       |                                  |
| **4**    | isCreateUser    | int          | 否           | 是否自动创建新用户[1:是,0:否]，默认：0 |                                  |

- 请求示例

```json
{
	"userName": "18500040114",
	"password": "ae9e024882840c925b79bb9b2542549d"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | UserEnitty   | 返回数据     |

- 返回示例

```json
{
	"time": "2022-08-31 18:29:40.062",
	"code": "00000",
	"data": {
		"userId": "fb0dd0b6-936f-2c56-fcc8-618cb7e7c9e6",
		"identityId": "********-b3f7-32dd-cb4d-3861bfe33444",
		"token": "hbukp99ggjfjaaf7ngoru84b3q8wlos71661941780",
		"userName": "18500040114",
		"phone": "18500040114",
		"extraContent": {
			"nickname": "YY_87654321"
		},
		"identityList": [{
			"identityId": "********-b3f7-32dd-cb4d-3861bfe33444",
			"userId": "336d67fb-3c5e-a7f9-072c-1b2f16a87aa0",
			"companyId": "be0854d7-703b-45c4-0ad2-8461c852c645",
			"departmentId": "968b5697-62de-4933-da4f-3d8e33b61b34",
			"positionId": "be0854d7-703b-45c4-0ad2-8461c852c645",
			"endTime": "",
			"isSelected": 1
		}, {
			"identityId": "3ef07662-bb9a-720b-9a3a-ba72bd0bed19",
			"userId": "2878be6f-6cf0-e409-fad5-7f769d23da6a",
			"companyId": "",
			"departmentId": "",
			"positionId": "",
			"endTime": "",
			"isSelected": 0
		}],
		"eventList": [
			"819ff175-1e85-32d5-8fe9-462e22886ea8", 
			"********-b3f7-32dd-cb4d-3861bfe33444"
		]
	},
	"message": "请求成功"
}
```

#### 退出

- 接口描述：根据tokenList批量退出用户登录状态
- 执行流程：
    - 根据tokenList更新用户token表状态
    - 清除token登录缓存

- 请求地址：/common/logout

- 请求方式：POST / JSON

- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                          |
| -------- | ------------ | ------------ | ------------ | ------------------------------------- |
| **1**    | tokenList    | List<String> | 是           | token列表，批量退出使用，默认：空数组 |

- 请求示例

```json
{
	"tokenList": [
		"cvtcpi3gi7ygefd0hy12ao3xuw5mkpn51675999644",
		"wuf4ic3p3ezpntd3psfpg924s9yrrvme1675999098",
		"fl4n0d6m9i7x33gu5ruszm5zrjvrha6s1675995990"
	]
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |

- 返回示例

```json
{
    "time": "2022-08-26 16:14:47.136",
    "code": "00000",
    "message": "请求成功"
}
```

#### 验证身份

- 使用场景：验证token是否有效、token换取用户信息
- 请求地址：/common/checkToken
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                          |
| -------- | ------------ | ------------ | ------------ | ------------------------------------- |
| **1**    | token        | String       | 是           | token                                 |
| **2**    | isRefresh    | int          | 否           | 是否更新token数据，[1:更新, 0:不更新] |
| **3**    | isSimple     | int          | 否           | 是否只返回验证结果，[1:是, 0:否]      |

- 请求示例

```json
{
	"token": "uf6c4t0vdqtel8swt82g5yri8qo40dr21672196760",
	"isRefresh": 1
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | UserEntity   | 返回数据     |

- 返回示例

```json
{
	"time": "2022-08-31 18:29:40.062",
	"code": "00000",
	"data": {
		"userId": "fb0dd0b6-936f-2c56-fcc8-618cb7e7c9e6",
		"identityId": "********-b3f7-32dd-cb4d-3861bfe33444",
		"token": "hbukp99ggjfjaaf7ngoru84b3q8wlos71661941780",
		"userName": "18500040114",
		"phone": "18500040114",
		"extraContent": {
			"nickname": "YY_87654321"
		},
		"identityList": [{
			"identityId": "********-b3f7-32dd-cb4d-3861bfe33444",
			"userId": "336d67fb-3c5e-a7f9-072c-1b2f16a87aa0",
			"companyId": "be0854d7-703b-45c4-0ad2-8461c852c645",
			"departmentId": "968b5697-62de-4933-da4f-3d8e33b61b34",
			"positionId": "be0854d7-703b-45c4-0ad2-8461c852c645",
			"endTime": "",
			"isSelected": 1
		}, {
			"identityId": "3ef07662-bb9a-720b-9a3a-ba72bd0bed19",
			"userId": "2878be6f-6cf0-e409-fad5-7f769d23da6a",
			"companyId": "",
			"departmentId": "",
			"positionId": "",
			"endTime": "",
			"isSelected": 0
		}],
		"eventList": [
			"819ff175-1e85-32d5-8fe9-462e22886ea8", 
			"********-b3f7-32dd-cb4d-3861bfe33444"
		]
	},
	"message": "请求成功"
}
```

#### 获取操作日志列表

- 请求地址：/log/getLogList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------ | ------------ | ------------------------------------------------------------ |
| **1**    | page         | int          | 否           | 页码，默认：1                                                |
| **2**    | limit        | int          | 否           | 每页条数，默认：10，传0返回全部                              |
| **3**    | condition    | Object       | 否           | 查询参数支持直接查询的参数：idtype（见配置项说明）optcontentappChannelIddataChannelId其他参数一律按json方式查询 |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"type": 1
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**             | **字段说明** |
| -------- | ------------ | ------------------------ | ------------ |
| **1**    | code         | String                   | 返回状态码   |
| **2**    | time         | String                   | 请求返回时间 |
| **3**    | message      | String                   | 返回说明     |
| **4**    | data         | DataList<IdentityEntity> | 返回数据     |

- 返回示例

```json
{
	"code": "00000",
	"message": "请求成功",
	"time": "2023-08-17 10:15:03.282",
	"data": {
		"dataCount": 2,
		"dataList": [{
			"createTime": "2023-08-17 01:58:39",
			"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
			"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
			"id": 113,
			"type": 1,
			"content": "登录成功，用户id：82b2ccdd-46c7-2dd0-f728-648ff53373d5，用户名：18500040114"
		}, {
			"createTime": "2023-08-17 01:54:24",
			"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
			"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
			"id": 112,
			"type": 1,
			"content": "登录成功，用户id：82b2ccdd-46c7-2dd0-f728-648ff53373d5，用户名：18500040114"
		}]
	}
}
```

### 权限类

#### 添加用户组

- 请求地址：/group/addGroup
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型**        | **是否必传** | **参数说明**                                  |
| -------- | ------------- | ------------------- | ------------ | --------------------------------------------- |
| **1**    | groupName     | String              | 是           | 用户组名称                                    |
| **2**    | eventIdList   | List<String>        | 否           | 用户组关联事件id列表                          |
| **3**    | extraContent  | Map<String, Object> | 否           | 附加信息                                      |
| **4**    | sort          | Integer             | 否           | 排列顺序                                      |
| **5**    | appChannelId  | String              | 否           | 应用渠道号不传默认取header的x-app_channel_id  |
| **6**    | dataChannelId | String              | 否           | 数据渠道号不传默认取header的x-data_channel_id |

- 请求示例

```json
{
	"groupName": "测试用户组",
	"eventIdList": [
		"aac8a3f8-9b62-a668-6da9-35d6cacfa9c7",
		"447da2b9-e9aa-4dab-9bad-d4a0e755303b"
	],
	"extraContent": {
		"companyId": "3ef07662-bb9a-720b-9a3a-ba72bd0bed19",
		"memo": "这是一个测试用的用户组"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**    | **字段说明** |
| -------- | ------------ | --------------- | ------------ |
| **1**    | code         | String          | 返回状态码   |
| **2**    | time         | String          | 请求返回时间 |
| **3**    | message      | String          | 返回说明     |
| **4**    | data         | UserGroupEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-09-01 13:48:38.332",
	"code": "00000",
	"data": {
		"groupId": "cdc24239-f935-d138-6755-f5014c3efd3f",
		"groupName": "测试用户组",
		"createTime": "",
		"eventIdList": [
			"aac8a3f8-9b62-a668-6da9-35d6cacfa9c7",
			"447da2b9-e9aa-4dab-9bad-d4a0e755303b"
		],
		"extraContent": {
			"companyId": "3ef07662-bb9a-720b-9a3a-ba72bd0bed19",
			"memo": "这是一个测试用的用户组"
		}
	},
	"message": "请求成功"
}
```

### 修改用户组

- 请求地址：/group/updateGroup
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**        | **是否必传** | **参数说明**                                               |
| -------- | ------------ | ------------------- | ------------ | ---------------------------------------------------------- |
| **1**    | groupId      | int                 | 是           | 用户组id                                                   |
| **2**    | groupName    | String              | 否           | 用户组名称，不更新此字段传null或不传，默认：null           |
| **3**    | eventIdList  | List<String>        | 否           | 用户组关联事件id列表，不更新此字段传null或不传，默认：null |
| **4**    | extraContent | Map<String, Object> | 否           | 附加信息，不更新此字段传null或不传，默认：null             |
| **5**    | sort         | Integer             | 否           | 排列顺序                                                   |

- 请求示例

```json
{
	"groupId": "b1ba90cc-a006-169c-8210-5ebcaa66a9d5",
	"groupName": "测试用户组4444",
	"eventIdList": [
		"227da2b9-e9aa-4dab-9bad-d4a0e755303b",
		"33c8a3f8-9b62-a668-6da9-35d6cacfa9c7",
		"55c8a3f8-9b62-a668-6da9-35d6cacfa9c7"
	],
	"extraContent": {
		"companyId": "ccca90cc-a006-169c-8210-5ebcaa66a9d5",
		"memo": "测试11232132"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**    | **字段说明** |
| -------- | ------------ | --------------- | ------------ |
| **1**    | code         | String          | 返回状态码   |
| **2**    | time         | String          | 请求返回时间 |
| **3**    | message      | String          | 返回说明     |
| **4**    | data         | UserGroupEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-01-02 14:24:39.045",
	"code": "00000",
	"data": {
		"groupId": "b1ba90cc-a006-169c-8210-5ebcaa66a9d5",
		"groupName": "测试用户组4444",
		"createTime": "2023-01-02 05:06:01",
		"eventIdList": [
			"227da2b9-e9aa-4dab-9bad-d4a0e755303b",
			"33c8a3f8-9b62-a668-6da9-35d6cacfa9c7",
			"55c8a3f8-9b62-a668-6da9-35d6cacfa9c7"
		],
		"userIdList": [],
		"extraContent": {
			"companyId": "ccca90cc-a006-169c-8210-5ebcaa66a9d5",
			"memo": "测试11232132"
		},
		"appChannelId": "yy593453ed6c9034",
		"dataChannelId": "123321123321"
	},
	"message": "请求成功"
}
```

#### 删除用户组

- 接口描述：只能删除未关联用户的用户组
- 请求地址：/group/deleteGroup
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明** |
| -------- | ------------ | ------------ | ------------ | ------------ |
| **1**    | groupId      | String       | 是           | 用户组id     |

- 请求示例

```json
{
	"groupId": "cdc24239-f935-d138-6755-f5014c3efd3f"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**    | **字段说明** |
| -------- | ------------ | --------------- | ------------ |
| **1**    | code         | String          | 返回状态码   |
| **2**    | time         | String          | 请求返回时间 |
| **3**    | message      | String          | 返回说明     |
| **4**    | data         | UserGroupEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-09-01 13:48:38.332",
	"code": "00000",
	"data": {
		"groupId": "cdc24239-f935-d138-6755-f5014c3efd3f"
	},
	"message": "请求成功"
}
```

#### 获取用户组列表

- 请求地址：/group/getGroupList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型**        | **是否必传** | **参数说明**                                                 |
| -------- | ------------- | ------------------- | ------------ | ------------------------------------------------------------ |
| **1**    | page          | int                 | 是           | 页码，默认：1                                                |
| **2**    | limit         | int                 | 是           | 每页条数，默认：10，传0返回全部                              |
| **3**    | condition     | Map<String, Object> | 是           | 查询条件支持直接查询的字段：groupIdgroupNamegroupIdListappChannelIddataChannelId其他字段一律按json方式查询 |
| **4**    | appChannelId  | String              | 否           | 用户所属渠道号 不传则取header的x-app_channel_id              |
| **5**    | dataChannelId | String              | 否           | 用户所属渠道号 不传则取header的x-data_channel_id             |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"groupIdList": [
			"a5b53682-7f00-9d9b-6024-154e97d447ea",
			"cdc24239-f935-d138-6755-f5014c3efd3f",
			"b1ba90cc-a006-169c-8210-5ebcaa66a9d5"
		]
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**          | **字段说明** |
| -------- | ------------ | --------------------- | ------------ |
| **1**    | dataCount    | int                   | 总数量       |
| **2**    | dataList     | List<UserGroupEntity> | 数据列表     |

- 返回示例

```json
{
	"time": "2023-01-02 15:10:56.726",
	"code": "00000",
	"data": {
		"dataList": [{
			"groupId": "b1ba90cc-a006-169c-8210-5ebcaa66a9d5",
			"groupName": "测试用户组4444",
			"createTime": "2023-01-02 05:06:01",
			"eventIdList": ["227da2b9-e9aa-4dab-9bad-d4a0e755303b"],
			"userIdList": [],
			"extraContent": {
				"memo": "测试11232132",
				"companyId": "ccca90cc-a006-169c-8210-5ebcaa66a9d5"
			},
			"appChannelId": "yy593453ed6c9034",
			"dataChannelId": "123321123321"
		}, {
			"groupId": "a5b53682-7f00-9d9b-6024-154e97d447ea",
			"groupName": "测试用户组12321",
			"createTime": "2022-12-25 11:38:51",
			"eventIdList": [],
			"userIdList": [],
			"extraContent": [],
			"appChannelId": "yy593453ed6c9034",
			"dataChannelId": "1233212"
		}],
		"dataCount": 3
	},
	"message": "请求成功"
}
```

#### 验证权限

- 接口描述：先取出用户当前身份，再查询用户身份关联的用户组是否有此权限
- 请求地址：/common/checkEvent
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明** |
| -------- | ------------ | ------------ | ------------ | ------------ |
| **1**    | eventId      | String       | 是           | 事件权限id   |
| **2**    | identityId   | String       | 是           | 用户身份id   |

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | CheckEventData | 返回数据     |

- - CheckEventData

| **序号** | **字段名称** | **字段类型** | **字段说明**                     |
| -------- | ------------ | ------------ | -------------------------------- |
| **1**    | eventId      | String       | 事件id                           |
| **2**    | identityId   | String       | 用户身份id                       |
| **3**    | isPass       | int          | 是否有权限，[1:有权限, 0:无权限] |

### 组织架构类

#### 添加公司

- 请求地址：/company/addCompany
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**        | **是否必传** | **参数说明**                           |
| -------- | ------------ | ------------------- | ------------ | -------------------------------------- |
| **1**    | companyName  | String              | 是           | 公司名称                               |
| **2**    | extraContent | Map<String, Object> | 是           | 公司扩展信息，不处理直接返回，json格式 |
| **3**    | parentId     | String              | 否           | 上级公司id，默认：空                   |

- 请求示例

```json
{
	"companyName": "测试公司2222345",
	"extraContent": {
		"companyName": "测试公司2222345",
		"logo": "https://erp.eeeyou.cn/static/www/images/oa/group-icon.png"
	},
	"parentId": "8b0eac08-fe63-9cf1-fa8d-65cd88817cb9"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**  | **字段说明** |
| -------- | ------------ | ------------- | ------------ |
| **1**    | code         | String        | 返回状态码   |
| **2**    | time         | String        | 请求返回时间 |
| **3**    | message      | String        | 返回说明     |
| **4**    | data         | CompanyEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-10-14 14:34:12.548",
	"code": "00000",
	"data": {
		"companyId": “b32381e6-a5b7-e001-7a6c-5510976bfcb8",
"companyName": "测试公司2222345",
		"parentId": "8b0eac08-fe63-9cf1-fa8d-65cd88817cb9",
		"extraContent": {
			"companyName": "测试公司2222345",
			"logo": "https:\/\/erp.eeeyou.cn\/static\/www\/images\/oa\/group-icon.png"
		},
		"createTime": "2022-12-12 12:00:00",
		"children": [],
		"appChannelId": "79bc3318-d985-b9af-3508-159f662f16c8",
		"dataChannelId": "0f9086f4-c233-2989-4481-cca2f48c9a1b"
	},
	"message": "请求成功"
}
```

#### 修改公司

- 请求地址：/company/updateCompany
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**        | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------------- | ------------ | ------------------------------------------------------------ |
| **1**    | companyId    | String              | 是           | 公司id                                                       |
| **2**    | companyName  | String              | 否           | 公司名称，不更新此字段传null或不传，默认：null               |
| **3**    | extraContent | Map<String, Object> | 否           | 公司扩展信息，不处理直接返回，json格式不更新此字段传null或不传，默认：null |
| **4**    | parentId     | String              | 否           | 上级公司id，不更新此字段传null或不传，默认：null             |

- 请求示例

```json
{
    "companyId": "b32381e6-a5b7-e001-7a6c-5510976bfcb8",
    "extraContent": {
        "companyName": "测试公司2333555",
        "logo": "https://erp.eeeyou.cn/static/www/images/oa/group-icon.png"
    }
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**  | **字段说明** |
| -------- | ------------ | ------------- | ------------ |
| **1**    | code         | String        | 返回状态码   |
| **2**    | time         | String        | 请求返回时间 |
| **3**    | message      | String        | 返回说明     |
| **4**    | data         | CompanyEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-10-14 14:48:42.265",
	"code": "00000",
	"data": {
		"companyId": "b32381e6-a5b7-e001-7a6c-5510976bfcb8",
		"parentId": "",
		"extraContent": {
			"companyName": "测试公司2333555",
			"logo": "https://erp.eeeyou.cn/static/www/images/oa/group-icon.png"
		},
		"createTime": "2022-10-08 02:43:44",
		"children": [],
		"appChannelId": "79bc3318-d985-b9af-3508-159f662f16c8",
		"dataChannelId": "0f9086f4-c233-2989-4481-cca2f48c9a1b"
	},
	"message": "请求成功"
}

```

#### 删除公司

- 请求地址：/company/deleteCompany
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                                               |
| -------- | ------------ | ------------ | ------------ | ---------------------------------------------------------- |
| **1**    | companyId    | String       | 是           | 公司id                                                     |
| **2**    | isForce      | int          | 否           | 是否强制删除[1:是, 0:否]强制删除直接删除公司下属部门和职位 |

- 请求示例

```json
{
    "companyId": "b32381e6-a5b7-e001-7a6c-5510976bfcb8"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**  | **字段说明** |
| -------- | ------------ | ------------- | ------------ |
| **1**    | code         | String        | 返回状态码   |
| **2**    | time         | String        | 请求返回时间 |
| **3**    | message      | String        | 返回说明     |
| **4**    | data         | CompanyEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-10-14 14:48:42.265",
	"code": "00000",
	"data": {
		"companyId": "b32381e6-a5b7-e001-7a6c-5510976bfcb8",
		"parentId": "",
		"extraContent": {
			"companyName": "测试公司2333555",
			"logo": "https://erp.eeeyou.cn/static/www/images/oa/group-icon.png"
		},
		"createTime": "2022-10-08 02:43:44",
		"children": [],
		"appChannelId": "79bc3318-d985-b9af-3508-159f662f16c8",
		"dataChannelId": "0f9086f4-c233-2989-4481-cca2f48c9a1b"
	},
	"message": "请求成功"
}
```

#### 获取公司列表

- 请求地址：/company/getCompanyList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型**        | **是否必传** | **参数说明**                                                 |
| -------- | ------------- | ------------------- | ------------ | ------------------------------------------------------------ |
| **1**    | page          | int                 | 否           | 页码，默认：1                                                |
| **2**    | limit         | int                 | 否           | 每页条数，默认：10，传0返回全部                              |
| **3**    | condition     | Map<String, Object> | 否           | 查询条件支持直接查询的字段：companyIdcompanyIdListcompanyName（模糊查询）startDateTime（创建时间）endDateTime（创建时间）appChannelIddataChannelId |
| **4**    | isSimple      | int                 | 否           | 是否返回简单实体 [1:是,0:否]                                 |
| **5**    | dataChannelId | String              | 否           | 数据渠道号                                                   |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"companyIdList": [
			"33477218-c779-0419-4c3e-e12a4b4f11fd",
			"bc78df67-2b74-bedd-5cd2-182cd81e1f5e"
		]
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**        | **字段说明** |
| -------- | ------------ | ------------------- | ------------ |
| **1**    | dataCount    | int                 | 总数量       |
| **2**    | dataList     | List<CompanyEntity> | 数据列表     |

- 返回示例

```json
{
	"time": "2022-10-14 15:24:23.917",
	"code": "00000",
	"data": {
		"dataList": [{
				"companyId": "0f9086f4-c233-2989-4481-cca2f48c9a1b",
				"parentId": "",
				"extraContent": {
					"address": "北京市汉威广场",
					"foundTime": "2022-08-30 00:00:00",
					"openLimit": "[\"2022-08-30\",\"2022-09-24\"]",
					"creditCode": "91430981730506989C"
				},
				"createTime": "2021-10-16 02:26:13",
				"children": [],
				"appChannelId": "b32381e6-a5b7-e001-7a6c-5510976bfcb8",
				"dataChannelId": "32ac8b28-9108-c838-ade8-5cd1044d33f9"
			},
			{
				"companyId": "bfff9329-25d2-ea11-1c48-d63d18089b54",
				"parentId": "0f9086f4-c233-2989-4481-cca2f48c9a1b",
				"extraContent": {
					"address": "南京市江宁滨江经济开发区宝象路50号7",
					"foundTime": "2008-09-09 00:00:00",
					"openLimit": "[\"2008-08-05\",\"2039-08-06\"]",
					"creditCode": "913201156637772752"
				},
				"createTime": "2021-10-16 02:24:30",
				"children": [],
				"appChannelId": "b32381e6-a5b7-e001-7a6c-5510976bfcb8",
				"dataChannelId": "32ac8b28-9108-c838-ade8-5cd1044d33f9"
			}
		],
		"dataCount": 2
	},
	"message": "请求成功"
}
```

#### 添加部门

- 请求地址：/department/addDepartment
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**   | **参数类型**        | **是否必传** | **参数说明**                           |
| -------- | -------------- | ------------------- | ------------ | -------------------------------------- |
| **1**    | departmentName | String              | 是           | 部门名称                               |
| **2**    | companyId      | String              | 是           | 所属公司id                             |
| **3**    | parentId       | String              | 否           | 上级部门id                             |
| **4**    | sort           | int                 | 否           | 排列顺序                               |
| **5**    | extraContent   | Map<String, Object> | 否           | 部门扩展信息，不处理直接返回，json格式 |

- 请求示例

```json
{
	"departmentName": "测试部门*********",
	"extraContent": {
		"departmentName": "测试部门*********"
	},
	"companyId": "32ac8b28-9108-c838-ade8-5cd1044d33f9",
	"parentId": "f032e70a-9f47-6786-f532-df7dfec62b4b",
	"sort": 0
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**     | **字段说明** |
| -------- | ------------ | ---------------- | ------------ |
| **1**    | code         | String           | 返回状态码   |
| **2**    | time         | String           | 请求返回时间 |
| **3**    | message      | String           | 返回说明     |
| **4**    | data         | DepartmentEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-10-14 15:45:30.376",
	"code": "00000",
	"data": {
		"departmentId": "dcef2954-f258-b698-1178-c517072afc45",
		"extraContent": {
			"departmentName": "测试部门*********"
		},
		"parentId": "f032e70a-9f47-6786-f532-df7dfec62b4b",
		"companyId": "32ac8b28-9108-c838-ade8-5cd1044d33f9",
		"sort": 0,
		"createTime": "2022-10-14 15:45:30"
	},
	"message": "请求成功"
}
```

#### 修改部门

- 请求地址：/department/updateDepartment
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**   | **参数类型**        | **是否必传** | **参数说明**                                                 |
| -------- | -------------- | ------------------- | ------------ | ------------------------------------------------------------ |
| **1**    | departmentId   | String              | 是           | 部门id                                                       |
| **2**    | departmentName | String              | 否           | 部门名称传null或不传不更新此字段，默认：null                 |
| **3**    | parentId       | String              | 否           | 上级部门id传null或不传不更新此字段，默认：null               |
| **4**    | sort           | int                 | 否           | 排列顺序                                                     |
| **5**    | extraContent   | Map<String, Object> | 否           | 部门扩展信息，不处理直接返回，json格式传null或不传不更新此字段，默认：null |

- 请求示例

```json
{
	"departmentId": "836013fd-3830-2723-413c-1b3c0a4d112a",
	"extraContent": {
		"departmentName": "测试部门555555"
	},
	"parentId": "698c0479-6363-c418-a502-8f30682b8053",
	"sort": 1
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**     | **字段说明** |
| -------- | ------------ | ---------------- | ------------ |
| **1**    | code         | String           | 返回状态码   |
| **2**    | time         | String           | 请求返回时间 |
| **3**    | message      | String           | 返回说明     |
| **4**    | data         | DepartmentEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-10-14 15:52:31.426",
	"code": "00000",
	"data": {
		"departmentId": "836013fd-3830-2723-413c-1b3c0a4d112a",
		"extraContent": {
			"departmentName": "测试部门555555"
		},
		"parentId": "698c0479-6363-c418-a502-8f30682b8053",
		"companyId": "32ac8b28-9108-c838-ade8-5cd1044d33f9",
		"sort": 1,
		"createTime": "2022-09-19 11:19:32"
	},
	"message": "请求成功"
}
```

#### 删除部门

- 请求地址：/department/deleteDepartment
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                                           |
| -------- | ------------ | ------------ | ------------ | ------------------------------------------------------ |
| **1**    | departmentId | String       | 是           | 部门id                                                 |
| **2**    | isForce      | int          | 否           | 是否强制删除[1:是, 0:否]强制删除直接删除部门下属子部门 |

- 请求示例

```json
{
    "departmentId": "836013fd-3830-2723-413c-1b3c0a4d112a"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**     | **字段说明** |
| -------- | ------------ | ---------------- | ------------ |
| **1**    | code         | String           | 返回状态码   |
| **2**    | time         | String           | 请求返回时间 |
| **3**    | message      | String           | 返回说明     |
| **4**    | data         | DepartmentEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-10-14 15:55:10.275",
	"code": "00000",
	"data": {
		"departmentId": "836013fd-3830-2723-413c-1b3c0a4d112a",
		"extraContent": {
			"departmentName": "测试部门555555"
		},
		"parentId": "698c0479-6363-c418-a502-8f30682b8053",
		"companyId": "32ac8b28-9108-c838-ade8-5cd1044d33f9",
		"sort": 1,
		"createTime": "2022-09-19 11:19:32"
	},
	"message": "请求成功"
}
```

#### 获取部门列表

- 请求地址：/department/getDepartmentList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**        | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------------- | ------------ | ------------------------------------------------------------ |
| **1**    | page         | int                 | 是           | 页码，默认：1                                                |
| **2**    | limit        | int                 | 是           | 每页条数，默认：10，传0返回全部                              |
| **3**    | condition    | Map<String, Object> | 是           | 查询条件支持直接查询的字段：departmentId/departmentIdList/departmentName/companyId/parentId |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"departmentIdList": [
			"db3fa02f-5727-d4d5-95a9-dac5e10a6e96",
			"8b0eac08-fe63-9cf1-fa8d-65cd88817cb9"
		]
	}
}

```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**           | **字段说明** |
| -------- | ------------ | ---------------------- | ------------ |
| **1**    | dataCount    | int                    | 总数量       |
| **2**    | dataList     | List<DepartmentEntity> | 数据列表     |

- 返回示例

```json
{
	"time": "2022-10-14 16:18:36.528",
	"code": "00000",
	"data": {
		"dataList": [{
				"departmentId": "836013fd-3830-2723-413c-1b3c0a4d112a",
				"extraContent": {
					"departmentName": "测试部门*********"
				},
				"parentId": "",
				"companyId": "32ac8b28-9108-c838-ade8-5cd1044d33f9",
				"sort": 0,
				"createTime": "2022-10-14 07:45:30"
			},
			{
				"departmentId": "*************-2723-413c-1b3c0a4d112a",
				"extraContent": {
					"departmentName": "默认部门"
				},
				"parentId": "",
				"companyId": "32ac8b28-9108-c838-ade8-5cd1044d33f9",
				"sort": 0,
				"createTime": "2022-09-20 10:15:28"
			}
		],
		"dataCount": 2
	},
	"message": "请求成功"
}
```

#### 添加职位

- 请求地址：/position/addPosition
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**        | **是否必传** | **参数说明**                           |
| -------- | ------------ | ------------------- | ------------ | -------------------------------------- |
| **1**    | positionName | String              | 是           | 职位名称                               |
| **2**    | parentId     | String              | 否           | 上级职位id，默认：空                   |
| **3**    | companyId    | String              | 是           | 所属公司id                             |
| **4**    | departmentId | String              | 否           | 所属部门id，默认：空                   |
| **5**    | extraContent | Map<String, Object> | 是           | 职位扩展信息，不处理直接返回，json格式 |
| **6**    | sort         | int                 | 否           | 显示顺序，默认：0                      |

- 请求示例

```json
{
	"companyId": "73001de6-2dc3-dd88-6554-a1174230b8a5",
	"parentId": "e3988a74-1a55-c0e1-f12b-ab56982ada90",
	"departmentId": "1ac42d18-4b08-ac24-8415-4777bd7fe99d",
	"extraContent": {
		"name": "总经理秘书"
	},
	"sort": 1
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | PositionEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-10-14 16:22:22.084",
	"code": "00000",
	"data": {
		"positionId": "8b0eac08-fe63-9cf1-fa8d-65cd88817cb9",
		"parentId": "e3988a74-1a55-c0e1-f12b-ab56982ada90",
		"extraContent": {
			"name": "总经理秘书"
		},
		"companyId": "73001de6-2dc3-dd88-6554-a1174230b8a5",
		"departmentId": “1ac42d18-4b08-ac24-8415-4777bd7fe99d",
		"sort": 1,
		"createTime": "2022-10-14 16:22:22"
	},
	"message": "请求成功"
}
```

#### 修改职位

- 请求地址：/position/updatePosition
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**        | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------------- | ------------ | ------------------------------------------------------------ |
| **1**    | positionId   | String              | 是           | 职位id                                                       |
| **2**    | positionName | String              | 否           | 职位名称，不更新此字段传null或不传，默认：null               |
| **3**    | extraContent | Map<String, Object> | 否           | 职位扩展信息，不处理直接返回，json格式不更新此字段传null或不传，默认：null |
| **4**    | parentId     | String              | 否           | 上级职位id，不更新此字段传null或不传，默认：null             |
| **5**    | departmentId | String              | 否           | 所属部门id，不更新此字段传null或不传，默认：null             |
| **6**    | sort         | int                 | 否           | 显示顺序，默认：0                                            |

- 请求示例

```json
{
	"positionId": "8b0eac08-fe63-9cf1-fa8d-65cd88817cb9",
	"extraContent": {
		"positionName": "测试职位222"
	},
	"departmentId": "1ac42d18-4b08-ac24-8415-4777bd7fe99d",
	"parentId": “e3988a74-1a55-c0e1-f12b-ab56982ada90”,
	"sort": 1
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | PositionEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-10-14 16:25:04.178",
	"code": "00000",
	"data": {
		"positionId": "8b0eac08-fe63-9cf1-fa8d-65cd88817cb9",
		"parentId": "e3988a74-1a55-c0e1-f12b-ab56982ada90",
		"extraContent": {
			"positionName": "测试职位222"
		},
		"companyId": "836013fd-3830-2723-413c-1b3c0a4d112a",
		"departmentId": “1ac42d18-4b08-ac24-8415-4777bd7fe99d",
		"sort": 1,
		"createTime": "2022-09-16 14:49:24"
	},
	"message": "请求成功"
}
```

#### 删除职位

- 接口描述：不能删除有子职位、有关联用户的职位
- 请求地址：/position/deletePosition
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                                           |
| -------- | ------------ | ------------ | ------------ | ------------------------------------------------------ |
| **1**    | positionId   | String       | 是           | 职位id                                                 |
| **2**    | isForce      | int          | 否           | 是否强制删除[1:是, 0:否]强制删除直接删除职位下属子职位 |

- 请求示例

```json
{
    "positionId": "8b0eac08-fe63-9cf1-fa8d-65cd88817cb9"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | PositionEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2022-10-14 16:30:55.168",
	"code": "00000",
	"data": {
		"positionId": "8b0eac08-fe63-9cf1-fa8d-65cd88817cb9",
		"parentId": "e3988a74-1a55-c0e1-f12b-ab56982ada90",
		"extraContent": {
			"name": "总经理秘书"
		},
		"companyId": "836013fd-3830-2723-413c-1b3c0a4d112a",
		"departmentId": "1ac42d18-4b08-ac24-8415-4777bd7fe99d",
		"createTime": "2022-10-14 08:22:22"
	},
	"message": "请求成功"
}
```

#### 获取职位列表

- 请求地址：/position/getPositionList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**        | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------------- | ------------ | ------------------------------------------------------------ |
| **1**    | page         | int                 | 是           | 页码默认：1                                                  |
| **2**    | limit        | int                 | 是           | 每页条数默认：10传0返回全部                                  |
| **3**    | condition    | Map<String, Object> | 是           | 查询条件支持直接查询的字段：positionId/positionIdList/positionName/companyId/departmentId/parentId 其他字段一律按json方式查询 |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"positionIdList": [
			"836013fd-3830-2723-413c-1b3c0a4d112a",
			"1ac42d18-4b08-ac24-8415-4777bd7fe99d"
		]
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**         | **字段说明** |
| -------- | ------------ | -------------------- | ------------ |
| **1**    | dataCount    | int                  | 总数量       |
| **2**    | dataList     | List<PositionEntity> | 数据列表     |

- 返回示例

```json
{
	"time": "2022-10-14 16:38:52.455",
	"code": "00000",
	"data": {
		"dataList": [{
			"positionId": "836013fd-3830-2723-413c-1b3c0a4d112a",
			"parentId": "",
			"extraContent": {
				"name": "总经理秘书"
			},
			"companyId": "1ac42d18-4b08-ac24-8415-4777bd7fe99d",
			"departmentId": "70aff97a-c9e1-c87d-21cc-86d649698446",
			"createTime": "2022-10-14 08:21:57"
		}, {
			"positionId": "*************-2723-413c-1b3c0a4d112a",
			"parentId": "4b0f8a40-a602-4897-c2d8-8ddb96ae25e3",
			"extraContent": {
				"positionName": "默认职位"
			},
			"companyId": "bdfcc6c5-d9b0-f3db-3abd-ae4d4b8d8c47",
			"departmentId": "e3988a74-1a55-c0e1-f12b-ab56982ada90",
			"createTime": "2022-10-0303:46:42"
		}],
		"dataCount": 2
	},
	"message": "请求成功"
}
```

#### 获取组织架构内用户列表

- 应用场景：公司、部门、职位内用户列表
- 请求地址：/user/getCompanyUserList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                    |                                                    |
| -------- | ------------ | ------------ | ------------ | ------------------------------- | -------------------------------------------------- |
| **1**    | page         | int          | 否           | 页码，默认：1                   |                                                    |
| **2**    | limit        | int          | 否           | 每页条数，默认：10，传0返回全部 |                                                    |
| **3**    | companyId    | String       | 否           | 公司id                          | 此三个参数必传一个优先级：职位id > 部门id > 公司id |
| **4**    | departmentId | String       | 否           | 部门id                          |                                                    |
| **5**    | positionId   | String       | 否           | 职位id                          |                                                    |
| **6**    | userName     | String       | 否           | 用户名                          |                                                    |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"positionId": "220e7a80-f4bb-b37c-3aae-7e850c554d3d"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**     | **字段说明** |
| -------- | ------------ | ---------------- | ------------ |
| **1**    | dataCount    | int              | 总数量       |
| **2**    | dataList     | List<UserEntity> | 数据列表     |

- 返回示例

```json
{
	"time": "2022-08-31 11:29:41.787",
	"code": "00000",
	"data": {
		"dataList": [{
			"userId": "fb0dd0b6-936f-2c56-fcc8-618cb7e7c9e6",
			"userName": "18500040115",
			"phone": "18500040115",
			"createTime": "2022-08-29 14:24:07",
			"updateTime": "1900-01-01 00:00:00",
			"extraContent": {
				"avatar": "https://erp.eeeyou.cn/images/default-avatar.png",
				"nickname": "YY_2dJ4kU9b"
			},
			"identityList": [],
			"eventList": []
		}],
		"dataCount": 1
	},
	"message": "请求成功"
}
```

#### 获取组织架构信息

- 请求地址：/company/getCompanyTree
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                         |
| -------- | ------------ | ------------ | ------------ | ------------------------------------ |
| **1**    | companyId    | String       | 是           | 公司id，默认：0                      |
| **2**    | isGetUser    | int          | 否           | 是否获取组织架构下的用户[1:是, 0:否] |

- 返回结果

| **序号** | **字段名称** | **字段类型**      | **字段说明** |
| -------- | ------------ | ----------------- | ------------ |
| **1**    | code         | String            | 返回状态码   |
| **2**    | time         | String            | 请求返回时间 |
| **3**    | message      | String            | 返回说明     |
| **4**    | data         | CompanyTreeEntity | 返回数据     |

#### 获取公司成员

- 请求地址：/company/getCompanyUserList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                    |
| -------- | ------------ | ------------ | ------------ | ------------------------------- |
| **1**    | companyId    | String       | 是           | 公司id                          |
| **2**    | page         | int          | 否           | 页码，默认：1                   |
| **3**    | limit        | int          | 否           | 每页条数，默认：10，传0返回全部 |

- 请求示例

```json
{
	"companyId": "bfff9329-25d2-ea11-1c48-d63d18089b54",
	"page": 1,
	"limit": 2
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**         | **字段说明** |
| -------- | ------------ | -------------------- | ------------ |
| **1**    | dataCount    | int                  | 总数量       |
| **2**    | dataList     | List<IdentityEntity> | 用户id列表   |

- 返回示例

```json
{
	"time": "2022-12-28 13:30:13.555",
	"code": "00000",
	"data": {
		"dataList": [{
			"userId": "b32381e6-a5b7-e001-7a6c-5510976bfcb8",
			"companyId": "b5d59071-0dfb-6a0e-829d-97574ea43022",
			"departmentId": "",
			"positionId": ""
		}],
		"dataCount": 1
	},
	"message": "请求成功"
}
```

#### 获取部门成员

- 请求地址：/department/getDepartmentUserList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型** | **是否必传** | **参数说明**                                              |
| -------- | ------------- | ------------ | ------------ | --------------------------------------------------------- |
| **1**    | departmentId  | String       | 是           | 部门id，36位uuid                                          |
| **2**    | page          | int          | 是           | 页码，默认：1                                             |
| **3**    | limit         | int          | 是           | 每页条数，传0返回全部，默认：10                           |
| **4**    | isGetChildren | int          | 否           | 是否获取子部门成员 [1:获取子部门成员, 0:不获取子部门成员] |

- 请求示例

```json
{
	"page": 1,
	"limit": 2,
	"departmentId": "*************-2723-413c-1b3c0a4d112a"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**            | **字段说明** |
| -------- | ------------ | ----------------------- | ------------ |
| **1**    | dataCount    | int                     | 总数量       |
| **2**    | dataList     | List<CompanyUserEntity> | 用户列表     |

- 返回示例

```json
{
	"time": "2022-10-14 16:40:21.756",
	"code": "00000",
	"data": {
		"dataList": [{
				"userId": "32ac8b28-9108-c838-ade8-5cd1044d33f9",
				"companyId": "8b0eac08-fe63-9cf1-fa8d-65cd88817cb9",
				"departmentId": "836013fd-3830-2723-413c-1b3c0a4d112a",
				"positionId": "db3fa02f-5727-d4d5-95a9-dac5e10a6e96"
			},
			{
				"userId": "698c0479-6363-c418-a502-8f30682b8053",
				"companyId": "bdfcc6c5-d9b0-f3db-3abd-ae4d4b8d8c47",
				"departmentId": "0c2d93e5-ddc2-5a74-e90e-4176509639fb",
				"positionId": "b5d59071-0dfb-6a0e-829d-97574ea43022"
			}
		],
		"dataCount": 13
	},
	"message": "请求成功"
}
```

#### 获取职位成员

- 请求地址：/position/getPositionUserList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                 |
| -------- | ------------ | ------------ | ------------ | ---------------------------- |
| **1**    | positionId   | String       | 是           | 职位id，36位uuid             |
| **2**    | page         | int          | 是           | 页码默认：1                  |
| **3**    | limit        | int          | 是           | 每页条数默认：10 传0返回全部 |

- 请求示例

```json
{
	"page": 1,
	"limit": 2,
	"positionId": "*************-2723-413c-1b3c0a4d112a"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**            | **字段说明** |
| -------- | ------------ | ----------------------- | ------------ |
| **1**    | dataCount    | int                     | 总数量       |
| **2**    | dataList     | List<CompanyUserEntity> | 用户列表     |

- 返回示例

```json
{
	"time": "2022-10-14 16:40:21.756",
	"code": "00000",
	"data": {
		"dataList": [{
				"userId": "32ac8b28-9108-c838-ade8-5cd1044d33f9",
				"companyId": "8b0eac08-fe63-9cf1-fa8d-65cd88817cb9",
				"departmentId": "836013fd-3830-2723-413c-1b3c0a4d112a",
				"positionId": "db3fa02f-5727-d4d5-95a9-dac5e10a6e96"
			},
			{
				"userId": "698c0479-6363-c418-a502-8f30682b8053",
				"companyId": "bdfcc6c5-d9b0-f3db-3abd-ae4d4b8d8c47",
				"departmentId": "0c2d93e5-ddc2-5a74-e90e-4176509639fb",
				"positionId": "b5d59071-0dfb-6a0e-829d-97574ea43022"
			}
		],
		"dataCount": 13
	},
	"message": "请求成功"
}
```

### 标签类（实体类）

#### 添加标签

- 请求地址：/entity/addEntity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型** | **是否必传** | **参数说明**                                    |
| -------- | ------------- | ------------ | ------------ | ----------------------------------------------- |
| **1**    | entityName    | String       | 是           | 标签名称                                        |
| **2**    | type          | String       | 是           | 标签类别                                        |
| **3**    | extraContent  | Object       | 否           | 标签扩展信息，不处理直接返回，json格式          |
| **4**    | appChannelId  | String       | 否           | 用户所属渠道号不传则取header的x-app_channel_id  |
| **5**    | dataChannelId | String       | 否           | 用户所属渠道号不传则取header的x-data_channel_id |

- 请求示例

```json
{
	"entityName": "test entity 1",
	"type": 1,
	"extraContent": {
		"a": 1,
		"b": "2",
		"c": "33"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | PositionEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-05-26 15:41:37.993",
	"code": "00000",
	"data": {
		"entityId": "c00ee907-05fb-0e25-f8fc-483f94140922",
		"entityName": "test entity 3",
		"type": 1,
		"extraContent": {
			"a": 1,
			"b": "2",
			"c": "33"
		},
		"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
		"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
		"createTime": "2023-05-26 15:41:37"
	},
	"message": "请求成功"
}
```

#### 修改标签

- 请求地址：/entity/updateEntity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型** | **是否必传** | **参数说明**                                             |
| -------- | ------------- | ------------ | ------------ | -------------------------------------------------------- |
| **1**    | entityId      | String       | 是           | 标签id                                                   |
| **2**    | entityName    | String       | 否           | 标签名称，不修改传null或不传                             |
| **3**    | type          | String       | 否           | 标签类别，不修改传null或不传                             |
| **4**    | extraContent  | Object       | 否           | 标签扩展信息，不处理直接返回，json格式不修改传null或不传 |
| **5**    | appChannelId  | String       | 否           | 用户所属渠道号不传则取header的x-app_channel_id           |
| **6**    | dataChannelId | String       | 否           | 用户所属渠道号不传则取header的x-data_channel_id          |

- 请求示例

```json
{
	"entityId": "b3570e1f-0c74-2656-c49f-d5cb2b4dde69",
	"entityName": "test entity 1",
	"type": 1,
	"extraContent": {
		"a": 123,
		"b": "2",
		"c": "33"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**   | **字段说明** |
| -------- | ------------ | -------------- | ------------ |
| **1**    | code         | String         | 返回状态码   |
| **2**    | time         | String         | 请求返回时间 |
| **3**    | message      | String         | 返回说明     |
| **4**    | data         | PositionEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-05-26 15:41:37.993",
	"code": "00000",
	"data": {
		"entityId": "c00ee907-05fb-0e25-f8fc-483f94140922",
		"entityName": "test entity 3",
		"type": 1,
		"extraContent": {
			"a": 1,
			"b": "2",
			"c": "33"
		},
		"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
		"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
		"createTime": "2023-05-26 15:41:37"
	},
	"message": "请求成功"
}
```

#### 删除标签

- 请求地址：/entity/deleteEntity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------ | ------------ | ------------------------------------------------------------ |
| **1**    | entityId     | String       | 是           | 标签id                                                       |
| **2**    | isForce      | int          | 否           | 是否强制删除[1:是, 0:否] 强制删除不判断是否关联用户，直接删除标签和所有该标签用户关系 |

- 请求示例

```json
{
	"entityId": "b3570e1f-0c74-2656-c49f-d5cb2b4dde69",
	"isForce": 1
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |

#### 获取标签列表

- 请求地址：/entity/getEntityList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------ | ------------ | ------------------------------------------------------------ |
| **1**    | page         | int          | 是           | 页码默认：1                                                  |
| **2**    | limit        | int          | 是           | 每页条数默认：10传0返回全部                                  |
| **3**    | condition    | Object       | 是           | 查询条件支持直接查询的字段：entityId/entityIdList/entityName（模糊查询）/type/dataChannelId 其他字段一律按json方式查询 |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"entityName": "entity 2"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |
| **4**    | data         | ListData     | 返回数据     |

- - ListData

| **序号** | **字段名称** | **字段类型**         | **字段说明** |
| -------- | ------------ | -------------------- | ------------ |
| **1**    | dataCount    | int                  | 总数量       |
| **2**    | dataList     | List<PositionEntity> | 数据列表     |

- 返回示例

```json
{
	"time": "2023-05-29 10:45:11.033",
	"code": "00000",
	"data": {
		"dataList": [{
			"entityId": "37354129-6015-4b33-663b-781bdaae5d64",
			"entityName": "test entity 2",
			"type": 1,
			"extraContent": {
				"a": 1,
				"b": "2",
				"c": "33"
			},
			"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
			"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
			"createTime": "2023-05-26 15:37:57"
		}],
		"dataCount": 1
	},
	"message": "请求成功"
}
```

#### 添加标签关系

- 请求地址：/entity/addEntityRelation
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**       | **参数类型** | **是否必传** | **参数说明** |
| -------- | ------------------ | ------------ | ------------ | ------------ |
| **1**    | entityIdList       | List<String> | 是           | 标签id列表   |
| **2**    | targetEntityIdList | List<String> | 是           | 目标id列表   |
| **3**    | extraContent       | Object       | 否           | 扩展信息     |

- 请求示例

```json
{
    "entityIdList": [
        "b3570e1f-0c74-2656-c49f-d5cb2b4dde69",
        "37354129-6015-4b33-663b-781bdaae5d64"
    ],
    "targetEntityIdList": [
        "5945aa6d-53de-c705-226f-5446a52f6b40",
        "06e444b4-adf2-377e-a0eb-8c38a8bcee49",
        "8d7dfd73-fa1e-9324-6755-94b63c50e714"
    ]
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**                         | **字段说明** |
| -------- | ------------ | ------------------------------------ | ------------ |
| **1**    | code         | String                               | 返回状态码   |
| **2**    | time         | String                               | 请求返回时间 |
| **3**    | message      | String                               | 返回说明     |
| **4**    | data         | DataListEntity<EntityRelationEntity> | 返回数据     |

- 返回示例

```json
{
	"time": "2023-05-26 16:42:07.363",
	"code": "00000",
	"data": {
		"dataList": [{
			"entityId": "b3570e1f-0c74-2656-c49f-d5cb2b4dde69",
			"targetId": "5945aa6d-53de-c705-226f-5446a52f6b41",
			"createTime": "2023-05-26 16:42:07"
		}, {
			"entityId": "b3570e1f-0c74-2656-c49f-d5cb2b4dde69",
			"targetId": "06e444b4-adf2-377e-a0eb-8c38a8bcee41",
			"createTime": "2023-05-26 16:42:07"
		}, {
			"entityId": "b3570e1f-0c74-2656-c49f-d5cb2b4dde69",
			"targetId": "8d7dfd73-fa1e-9324-6755-94b63c50e712",
			"createTime": "2023-05-26 16:42:07"
		}],
		"dataCount": 9
	},
	"message": "请求成功"
}
```

#### 修改标签关系

- 请求地址：/entity/updateEntityRelation
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**   | **参数类型** | **是否必传** | **参数说明** |
| -------- | -------------- | ------------ | ------------ | ------------ |
| **1**    | entityId       | String       | 是           | 标签id       |
| **2**    | targetEntityId | String       | 是           | 目标id       |
| **3**    | extraContent   | Object       | 否           | 扩展信息     |

- 请求示例

```json
{
	"entityId": "76d6c4ad-9831-ae16-8ea1-62fe1a9310ec",
	"targetId": "5945aa6d-53de-c705-226f-5446a52f6b40",
	"extraContent": {
		"a": "1",
		"b": "22"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**         | **字段说明** |
| -------- | ------------ | -------------------- | ------------ |
| **1**    | code         | String               | 返回状态码   |
| **2**    | time         | String               | 请求返回时间 |
| **3**    | message      | String               | 返回说明     |
| **4**    | data         | EntityRelationEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-25 10:58:47.713",
	"code": "00000",
	"data": {
		"entityId": "76d6c4ad-9831-ae16-8ea1-62fe1a9310ec",
		"targetEntityId": "5945aa6d-53de-c705-226f-5446a52f6b40",
		"extraContent": {
			"a": "1",
			"b": "22"
		},
		"createTime": "2023-06-25 10:29:58",
		"updateTime": "2023-06-25 10:58:47"
	},
	"message": "请求成功"
}
```

#### 删除标签关系

- 请求地址：/entity/deleteEntityRelation
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**   | **参数类型** | **是否必传** | **参数说明** |
| -------- | -------------- | ------------ | ------------ | ------------ |
| **1**    | entityId       | String       | 是           | 标签id       |
| **2**    | targetEntityId | String       | 是           | 用户id       |

- 请求示例

```json
{
	"entityId": "c00ee907-05fb-0e25-f8fc-483f94140922",
	"targetId": "5945aa6d-53de-c705-226f-5446a52f6b40"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |

#### 获取标签关系列表

- 请求地址：/entity/getEntityRelationList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型** | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------ | ------------ | ------------------------------------------------------------ |
| **1**    | page         | int          | 否           | 页码，默认：1                                                |
| **2**    | limit        | int          | 否           | 每页条数，默认：0传0返回全部                                 |
| **3**    | condition    | Object       | 否           | 查询条件支持直接查询的字段：entityId/entityIdList/targetId/targetIdList其他字段一律按json方式查询 |

- 请求示例

```json
{
	"condition": {
		"entityId": "76d6c4ad-9831-ae16-8ea1-62fe1a9310ec"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**                         | **字段说明** |
| -------- | ------------ | ------------------------------------ | ------------ |
| **1**    | code         | String                               | 返回状态码   |
| **2**    | time         | String                               | 请求返回时间 |
| **3**    | message      | String                               | 返回说明     |
| **4**    | data         | DataListEntity<EntityRelationEntity> | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-25 11:30:37.773",
	"code": "00000",
	"data": {
		"dataList": [{
			"entityId": "76d6c4ad-9831-ae16-8ea1-62fe1a9310ec",
			"targetId": "06e444b4-adf2-377e-a0eb-8c38a8bcee49",
			"extraContent": {},
			"createTime": "2023-06-25 10:29:58",
			"updateTime": "2023-06-25 02:29:58"
		}, {
			"entityId": "76d6c4ad-9831-ae16-8ea1-62fe1a9310ec",
			"targetId": "5945aa6d-53de-c705-226f-5446a52f6b40",
			"extraContent": {
				"a": "1",
				"b": "22"
			},
			"createTime": "2023-06-25 10:29:58",
			"updateTime": "2023-06-25 10:58:47"
		}, {
			"entityId": "76d6c4ad-9831-ae16-8ea1-62fe1a9310ec",
			"targetId": "8d7dfd73-fa1e-9324-6755-94b63c50e714",
			"extraContent": {
				"a": 1,
				"b": "2",
				"c": "33"
			},
			"createTime": "2023-06-25 10:29:58",
			"updateTime": "2023-06-25 02:29:58"
		}],
		"dataCount": 3
	},
	"message": "请求成功"
}
```

### 好友类

#### 添加好友

- 请求地址：/friend/addFriend
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**         | **参数类型** | **是否必传** | **参数说明**                                    |
| -------- | -------------------- | ------------ | ------------ | ----------------------------------------------- |
| **1**    | identityId           | String       | 是           | 用户身份id                                      |
| **2**    | targetIdentityIdList | List<String> | 是           | 目标用户身份id列表                              |
| **3**    | extraContent         | Object       | 否           | 好友扩展信息，不处理直接返回，json格式          |
| **4**    | appChannelId         | String       | 否           | 用户所属渠道号不传则取header的x-app_channel_id  |
| **5**    | dataChannelId        | String       | 否           | 用户所属渠道号不传则取header的x-data_channel_id |

- 请求示例

```json
{
	"userId": "6a1fcbeb-f410-2426-7c2c-dc45c3189799",
	"targetUserIdList": [
		"ecbd09dd-8b1a-d774-b219-6b1810e86ec8",
		"0099bfe5-ae10-a518-febf-fa89a1af4c07",
		"499b681e-d360-9d01-428a-f682ef20e64b"
	]
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**           | **字段说明** |
| -------- | ------------ | ---------------------- | ------------ |
| **1**    | code         | String                 | 返回状态码   |
| **2**    | time         | String                 | 请求返回时间 |
| **3**    | message      | String                 | 返回说明     |
| **4**    | data         | List<UserFriendEntity> | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-15 12:48:16.202",
	"code": "00000",
	"data": [{
		"userId": "6a1fcbeb-f410-2426-7c2c-dc45c3189799",
		"targetUserId": "0099bfe5-ae10-a518-febf-fa89a1af4c07",
		"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
		"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
		"createTime": "2023-06-15 12:48:16”,
"extraContent": {
	"abc": "defw"
}
	}, {
		"userId": "6a1fcbeb-f410-2426-7c2c-dc45c3189799",
		"targetUserId": "24c7e1e4-b3d6-08bf-65ed-c0ba21e15b97",
		"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
		"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
		"createTime": "2023-06-15 12:48:16”,
"extraContent": {
	"abc": "defw"
}
	}],
	"message": "请求成功"
}
```

#### 修改好友

- 请求地址：/friend/updateFriend
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**     | **参数类型**        | **是否必传** | **参数说明**                           |
| -------- | ---------------- | ------------------- | ------------ | -------------------------------------- |
| **1**    | identityId       | String              | 是           | 用户身份id                             |
| **2**    | targetIdentityId | String              | 是           | 目标用户身份id                         |
| **3**    | extraContent     | Map<String, Object> | 否           | 好友扩展信息，不处理直接返回，json格式 |

- 请求示例

```json
{
	"userId": "6a1fcbeb-f410-2426-7c2c-dc45c3189799",
	"targetIdList": “ecbd09dd-8b1a-d774-b219-6b1810e86ec8”,
	"extraContent": {
		"a": 1,
		"b": "2",
		"c": "33"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**     | **字段说明** |
| -------- | ------------ | ---------------- | ------------ |
| **1**    | code         | String           | 返回状态码   |
| **2**    | time         | String           | 请求返回时间 |
| **3**    | message      | String           | 返回说明     |
| **4**    | data         | UserFriendEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-15 12:48:16.202",
	"code": "00000",
	"data": {
		"userId": "6a1fcbeb-f410-2426-7c2c-dc45c3189799",
		"targetId": "0099bfe5-ae10-a518-febf-fa89a1af4c07",
		"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
		"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
		"createTime": "2023-06-15 12:48:16”,
		"extraContent": {
		"a": 1,
		"b": "2",
		"c": "33"
	}
	},
	"message": "请求成功"
}
```

#### 删除好友

- 请求地址：/friend/deleteFriend
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**         | **参数类型** | **是否必传** | **参数说明**                                |
| -------- | -------------------- | ------------ | ------------ | ------------------------------------------- |
| **1**    | identityId           | String       | 是           | 用户身份id                                  |
| **2**    | targetIdentityIdList | String       | 是           | 目标用户身份id列表                          |
| **3**    | isDeleteEachOther    | int          | 否           | 是否删除对方的好友关系，默认：1[1:是, 0:否] |

- 请求示例

```json
{
	"userId": "6a1fcbeb-f410-2426-7c2c-dc45c3189799",
	"targetIdList": ["ecbd09dd-8b1a-d774-b219-6b1810e86ec8", "0099bfe5-ae10-a518-febf-fa89a1af4c07", "24c7e1e4-b3d6-08bf-65ed-c0ba21e15b97", "6ab3ae2a-6aa8-0796-c978-324ed600cdcd", "6ab3ae2a-6aa8-0796-c978-324ed600cdcf", "344a799f-d677-b8f7-2a67-9b04b1ce7624", "499b681e-d360-9d01-428a-f682ef20e64b"],
	"isDeleteEachOther": 1
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |

#### 获取好友列表

- 请求地址：/friend/getFriendList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型** | **是否必传** | **参数说明**                                                 |
| -------- | ------------- | ------------ | ------------ | ------------------------------------------------------------ |
| **1**    | page          | int          | 是           | 页码默认：1                                                  |
| **2**    | limit         | int          | 是           | 每页条数默认：10传0返回全部                                  |
| **3**    | condition     | Object       | 是           | 查询条件支持直接查询的字段：identityId/identityIdList/targetIdentityId/targetIdentityIdList/appChannelId/dataChannelId其他字段一律按json方式查询 |
| **5**    | dataChannelId | String       | 否           | 用户所属渠道号 不传则取header的x-data_channel_id             |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"userId": "ecbd09dd-8b1a-d774-b219-6b1810e86ec8"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**                     | **字段说明** |
| -------- | ------------ | -------------------------------- | ------------ |
| **1**    | code         | String                           | 返回状态码   |
| **2**    | time         | String                           | 请求返回时间 |
| **3**    | message      | String                           | 返回说明     |
| **4**    | data         | DataListEntity<UserFriendEntity> | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-15 22:31:43.010",
	"code": "00000",
	"data": {
		"dataList": [{
			"userId": "6a1fcbeb-f410-2426-7c2c-dc45c3189799",
			"targetUserId": "1533c794-81eb-adf7-a2aa-50fbd1dbc0e7",
			"extraContent": {
				"abc": "defw"
			},
			"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
			"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
			"createTime": "2023-06-15 13:52:20",
			"targetUser": {
				"userId": "1533c794-81eb-adf7-a2aa-50fbd1dbc0e7",
				"identityId": "7131881b-ba5f-06a3-1d9a-592e4f1a20b5",
				"token": "ws3y6i5wf1ycvworp5m9ychkh0byb7bd1680052397",
				"userName": "13821496857",
				"isFreeze": 0,
				"createTime": "2023-03-28 12:41:34",
				"lastLoginTime": "2023-03-29 09:13:17",
				"lastLoginIp": "************",
				"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
				"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a"
			}
		}],
		"dataCount": 1
	},
	"message": "请求成功"
}
```

### 群聊类

#### 创建群聊

- 请求地址：/chatGroup/addChatGroup
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**   | **参数类型**        | **是否必传** | **参数说明**                                    |
| -------- | -------------- | ------------------- | ------------ | ----------------------------------------------- |
| **1**    | chatGroupName  | String              | 是           | 群聊名称                                        |
| **2**    | identityIdList | List<String>        | 是           | 群成员用户身份id列表                            |
| **3**    | extraContent   | Map<String, Object> | 否           | 好友扩展信息，不处理直接返回，json格式          |
| **4**    | appChannelId   | String              | 否           | 用户所属渠道号不传则取header的x-app_channel_id  |
| **5**    | dataChannelId  | String              | 否           | 用户所属渠道号不传则取header的x-data_channel_id |

- 请求示例

```json
{
	"chatGroupName": "群聊1",
	"extraContent": {
		"abc": 123,
		"aaa": "987"
	},
	"userIdList": ["ecbd09dd-8b1a-d774-b219-6b1810e86ec8", "0099bfe5-ae10-a518-febf-fa89a1af4c07", "24c7e1e4-b3d6-08bf-65ed-c0ba21e15b97", "6ab3ae2a-6aa8-0796-c978-324ed600cdcd", "6ab3ae2a-6aa8-0796-c978-324ed600cdcf", "344a799f-d677-b8f7-2a67-9b04b1ce7624", "499b681e-d360-9d01-428a-f682ef20e64b"]
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**    | **字段说明** |
| -------- | ------------ | --------------- | ------------ |
| **1**    | code         | String          | 返回状态码   |
| **2**    | time         | String          | 请求返回时间 |
| **3**    | message      | String          | 返回说明     |
| **4**    | data         | ChatGroupEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-15 13:10:21.803",
	"code": "00000",
	"data": {
		"chatGroupId": "8790be05-9422-d1af-8559-563e6455466f",
		"chatGroupName": "群聊6",
		"icon": "",
		"extraContent": {
			"chatGroupId": "4a44c793-1230-7db2-688d-67e5f2bf6604",
			"abc": 123,
			"aaa": "987"
		},
		"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
		"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
		"createTime": "",
		"userList": []
	},
	"message": "请求成功"
}
```

#### 修改群聊

- 请求地址：/chatGroup/updateChatGroup
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**   | **参数类型**        | **是否必传** | **参数说明**                           |
| -------- | -------------- | ------------------- | ------------ | -------------------------------------- |
| **1**    | chatGroupId    | String              | 是           | 群聊id                                 |
| **2**    | chatGroupName  | String              | 是           | 群聊名称                               |
| **3**    | identityIdList | List<String>        | 是           | 群成员用户身份id列表                   |
| **4**    | extraContent   | Map<String, Object> | 否           | 好友扩展信息，不处理直接返回，json格式 |

- 请求示例

```json
{
	"chatGroupId": "55ae5cd5-f7c3-e601-cd9d-9f8b5751690b",
	"chatGroupName": "群聊1",
	"extraContent": {
		"abc": 123,
		"aaa": "987"
	},
	"userIdList": ["ecbd09dd-8b1a-d774-b219-6b1810e86ec8"]
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**    | **字段说明** |
| -------- | ------------ | --------------- | ------------ |
| **1**    | code         | String          | 返回状态码   |
| **2**    | time         | String          | 请求返回时间 |
| **3**    | message      | String          | 返回说明     |
| **4**    | data         | ChatGroupEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-15 13:10:21.803",
	"code": "00000",
	"data": {
		"chatGroupId": "8790be05-9422-d1af-8559-563e6455466f",
		"chatGroupName": "群聊6",
		"icon": "",
		"extraContent": {
			"chatGroupId": "4a44c793-1230-7db2-688d-67e5f2bf6604",
			"abc": 123,
			"aaa": "987"
		},
		"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
		"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
		"createTime": "",
		"userList": []
	},
	"message": "请求成功"
}
```

#### 删除群聊

- 请求地址：/chatGroup/deleteChatGroup
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型** | **是否必传** | **参数说明**                                    |
| -------- | ------------- | ------------ | ------------ | ----------------------------------------------- |
| **1**    | chatGroupId   | String       | 是           | 群聊id                                          |
| **2**    | dataChannelId | String       | 否           | 用户所属渠道号不传则取header的x-data_channel_id |

- 请求示例

```json
{
	"chatGroupId": "55ae5cd5-f7c3-e601-cd9d-9f8b5751690b"
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |

#### 获取群聊列表

- 请求地址：/chatGroup/getChatGroupList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**  | **参数类型** | **是否必传** | **参数说明**                                                 |
| -------- | ------------- | ------------ | ------------ | ------------------------------------------------------------ |
| **1**    | page          | int          | 是           | 页码默认：1                                                  |
| **2**    | limit         | int          | 是           | 每页条数默认：10传0返回全部                                  |
| **3**    | condition     | Object       | 是           | 查询条件支持直接查询的字段：chatGroupId/chatGroupIdList/chatGroupName/appChannelId/dataChannelId 其他字段一律按json方式查询 |
| **5**    | dataChannelId | String       | 否           | 用户所属渠道号 不传则取header的x-data_channel_id             |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"chatGroupId": "ecbd09dd-8b1a-d774-b219-6b1810e86ec8"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**                    | **字段说明** |
| -------- | ------------ | ------------------------------- | ------------ |
| **1**    | code         | String                          | 返回状态码   |
| **2**    | time         | String                          | 请求返回时间 |
| **3**    | message      | String                          | 返回说明     |
| **4**    | data         | DataListEntity<ChatGroupEntity> | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-15 23:05:50.480",
	"code": "00000",
	"data": {
		"dataList": [{
			"chatGroupId": "4a44c793-1230-7db2-688d-67e5f2bf6604",
			"chatGroupName": "群聊6",
			"extraContent": {
				"aaa": "987",
				"abc": 123,
				"chatGroupId": "4a44c793-1230-7db2-688d-67e5f2bf6604"
			},
			"appChannelId": "b58c6e9d-8ac9-5ff2-e7c2-efcb55356040",
			"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a",
			"createTime": "2023-06-15 14:45:50",
			"userList": []
		}],
		"dataCount": 1
	},
	"message": "请求成功"
}
```

#### 添加群聊成员

- 请求地址：/chatGroup/addChatGroupIdentity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**   | **参数类型**        | **是否必传** | **参数说明**                       |
| -------- | -------------- | ------------------- | ------------ | ---------------------------------- |
| **1**    | chatGroupId    | String              | 是           | 群聊id                             |
| **2**    | identityIdList | List<String>        | 是           | 用户身份id列表                     |
| **3**    | extraContent   | Map<String, Object> | 否           | 扩展信息，不处理直接返回，json格式 |

- 请求示例

```json
{
	"chatGroupId": "7e530045-c13c-4e48-3a4a-2c14942ec3e2",
	"identityId": "3e3e892b-8074-fe05-18b5-4143a252e7cf",
	"extraContent": {
		"abcd": "def",
		"xyz": "1234"
	}
}
```

- 返回结果

| **序号** | **字段名称**             | **字段类型**                  | **字段说明**   |
| -------- | ------------------------ | ----------------------------- | -------------- |
| **1**    | code                     | String                        | 返回状态码     |
| **2**    | time                     | String                        | 请求返回时间   |
| **3**    | message                  | String                        | 返回说明       |
| **4**    | data                     | AddChatGroupIdentityData      | 返回数据       |
|          | AddChatGroupIdentityData |                               |                |
| **1**    | chatGroupId              | String                        | 群id           |
| **2**    | chatGroupName            | String                        | 群名称         |
| **3**    | identityList             | List<IdentityChatGroupEntity> | 添加的成员列表 |

- 返回示例

```json
{
	"code": "00000",
	"message": "请求成功",
	"time": "2023-08-16 17:12:44.090",
	"data": {
		"chatGroupId": "b6e11326-cd0e-13f4-84f2-8480dfa2f9c3",
		"chatGroupName": "烤菠萝🍍yyds123",
		"identityList": [{
			"identityId": "12df2c2a-ee9c-bf2a-ca9a-883ae3030a06",
			"chatGroupId": "b6e11326-cd0e-13f4-84f2-8480dfa2f9c3"
		}]
	}
}
```

#### 修改群聊成员

- 请求地址：/chatGroup/updateChatGroupIdentity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**        | **是否必传** | **参数说明**                       |
| -------- | ------------ | ------------------- | ------------ | ---------------------------------- |
| **1**    | chatGroupId  | String              | 是           | 群聊id                             |
| **2**    | identityId   | String              | 是           | 用户身份id                         |
| **3**    | extraContent | Map<String, Object> | 否           | 扩展信息，不处理直接返回，json格式 |

- 请求示例

```json
{
	"chatGroupId": "55ae5cd5-f7c3-e601-cd9d-9f8b5751690b",
	"identityId": "ecbd09dd-8b1a-d774-b219-6b1810e86ec8",
	"extraContent": {
		"abc": 123,
		"def": "alala"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**        | **字段说明** |
| -------- | ------------ | ------------------- | ------------ |
| **1**    | code         | String              | 返回状态码   |
| **2**    | time         | String              | 请求返回时间 |
| **3**    | message      | String              | 返回说明     |
| **4**    | data         | ChatGroupUserEntity | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-15 23:25:01.515",
	"code": "00000",
	"data": {
		"chatGroupId": "7e530045-c13c-4e48-3a4a-2c14942ec3e2",
		"identityId": "3e3e892b-8074-fe05-18b5-4143a252e7cf",
		"extraContent": {
			"abc": 123,
			"def": "alala",
			"hahaha": "opopop"
		},
		"createTime": "2023-06-15 15:19:09"
	},
	"message": "请求成功"
}
```

#### 删除群聊成员

- 请求地址：/chatGroup/deleteChatGroupIdentity
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数**   | **参数类型** | **是否必传** | **参数说明** |
| -------- | -------------- | ------------ | ------------ | ------------ |
| **1**    | chatGroupId    | String       | 是           | 群聊id       |
| **2**    | identityIdList | List<String> | 是           | 用户id       |

- 请求示例

```json
{
	"chatGroupId": "55ae5cd5-f7c3-e601-cd9d-9f8b5751690b",
	"identityIdList": [
		"aaaaaaaa-8b1a-d774-b219-6b1810e86ec8",
		"bbbbbbbb-8b1a-d774-b219-6b1810e86ec8"
	]
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | code         | String       | 返回状态码   |
| **2**    | time         | String       | 请求返回时间 |
| **3**    | message      | String       | 返回说明     |

#### 获取群聊成员列表

- 请求地址：/chatGroup/getChatGroupIdentityList
- 请求方式：POST / JSON
- 请求参数

| **序号** | **请求参数** | **参数类型**        | **是否必传** | **参数说明**                                                 |
| -------- | ------------ | ------------------- | ------------ | ------------------------------------------------------------ |
| **1**    | page         | int                 | 否           | 页码默认：1                                                  |
| **2**    | limit        | int                 | 否           | 每页条数默认：10传0返回全部                                  |
| **3**    | condition    | Map<String, Object> | 否           | 查询条件支持直接查询的字段：chatGroupId/identityId 其他字段一律按json方式查询 |

- 请求示例

```json
{
	"page": 1,
	"limit": 10,
	"condition": {
		"chatGroupId": "ecbd09dd-8b1a-d774-b219-6b1810e86ec8"
	}
}
```

- 返回结果

| **序号** | **字段名称** | **字段类型**                        | **字段说明** |
| -------- | ------------ | ----------------------------------- | ------------ |
| **1**    | code         | String                              | 返回状态码   |
| **2**    | time         | String                              | 请求返回时间 |
| **3**    | message      | String                              | 返回说明     |
| **4**    | data         | DataListEntity<ChatGroupUserEntity> | 返回数据     |

- 返回示例

```json
{
	"time": "2023-06-15 15:12:11.401",
	"code": "00000",
	"data": {
		"dataList": [{
			"chatGroupId": "55ae5cd5-f7c3-e601-cd9d-9f8b5751690b",
			"userId": "ecbd09dd-8b1a-d774-b219-6b1810e86ec8",
			"extraContent": {
				"abc": 123,
				"def": "alala"
			},
			"createTime": "2023-06-15 04:53:51",
			"user": {
				"userId": "ecbd09dd-8b1a-d774-b219-6b1810e86ec8",
				"identityId": "00cb8f93-7144-f3a9-8787-d1ec031fe91c",
				"token": "",
				"userName": "18830933663",
				"isFreeze": 0,
				"createTime": "2023-05-12 22:24:21",
				"groupIdList": ["7004d1f1-9192-4715-e8cb-4b01ca103d83"],
				"lastLoginTime": "2023-05-13 20:50:09",
				"lastLoginIp": "**************",
				"appChannelId": "4da0308d-bfde-c4e2-c2b3-9221c124c3d5",
				"dataChannelId": "6f24b689-3cdf-9c26-1487-ffb9f9f9f35a"
			}
		}],
		"dataCount": 1
	},
	"message": "请求成功"
}
```

### 公共参数

#### DataListEntity

| **序号** | **字段名称** | **字段类型** | **字段说明** |
| -------- | ------------ | ------------ | ------------ |
| **1**    | dataCount    | int          | 总数量       |
| **2**    | dataList     | List<T>      | 数据列表     |

#### UserEntity

| **序号** | **字段名称**  | **字段类型**         | **字段说明**                       |
| -------- | ------------- | -------------------- | ---------------------------------- |
| **1**    | identityId    | String               | 身份id，36位uuid                   |
| **2**    | token         | String               | 用户登录身份token                  |
| **3**    | userName      | String               | 用户名                             |
| **4**    | extraContent  | Map<String, Object>  | 其他用户信息，服务端不处理直接返回 |
| **5**    | identityList  | List<IdentityEntity> | 用户身份列表                       |
| **6**    | eventIdList   | List<String>         | 用户事件权限id列表                 |
| **7**    | groupIdList   | List<String>         | 用户组id列表                       |
| **8**    | lastLoginTime | String               | 上次登录时间                       |
| **9**    | lastLoginIp   | String               | 上次登录IP                         |
| **10**   | appChannelId  | String               | 应用渠道号                         |
| **11**   | dataChannelId | String               | 数据渠道号                         |
| **12**   | createTime    | String               | 创建时间                           |
| **13**   | updateTime    | String               | 更新时间                           |

#### IdentityEntity

| **序号** | **字段名称**         | **字段类型**           | **字段说明**               |
| -------- | -------------------- | ---------------------- | -------------------------- |
| **1**    | identityId           | String                 | 用户身份id                 |
| **2**    | identityName         | String                 | 用户身份名称               |
| **3**    | isSelected           | int                    | 是否为当前选中身份 1是 0否 |
| **4**    | groupList            | List<GroupEntity>      | 关联用户组列表             |
| **5**    | groupIdList          | List<String>           | 关联用户组id列表           |
| **6**    | companyId            | String                 | 公司id                     |
| **7**    | departmentList       | List<DepartmentEntity> | 关联部门列表               |
| **8**    | departmentIdList     | List<String>           | 关联部门id列表             |
| **9**    | positionList         | List<PositionEntity>   | 关联职位列表               |
| **10**   | positionIdList       | List<String>           | 关联职位id列表             |
| **11**   | eventIdList          | List<String>           | 用户事件权限id列表         |
| **12**   | userExtraContent     | Map<String, Object>    | 用户扩展信息               |
| **13**   | companyExtraContent  | Map<String, Object>    | 公司扩展信息               |
| **14**   | identityExtraContent | Map<String, Object>    | 身份扩展信息               |
| **15**   | appChannelId         | String                 | 应用渠道号                 |
| **16**   | dataChannelId        | String                 | 数据渠道号                 |
| **17**   | createTime           | String                 | 创建时间                   |
| **18**   | updateTime           | String                 | 更新时间                   |

#### IdentityRelationEntity

| **序号** | **字段名称** | **字段类型**        | **字段说明**     |
| -------- | ------------ | ------------------- | ---------------- |
| **1**    | relationType | String              | 关联关系类型     |
| **2**    | relateIdList | List<String>        | 关联用户组id列表 |
| **3**    | extraContent | Map<String, Object> | 扩展信息         |

#### GroupEntity

| **序号** | **字段名称**   | **字段类型**        | **字段说明**         |
| -------- | -------------- | ------------------- | -------------------- |
| **1**    | groupId        | String              | 用户组id             |
| **2**    | groupName      | String              | 用户组名称           |
| **3**    | extraContent   | Map<String, Object> | 用户组扩展信息       |
| **4**    | eventIdList    | List<String>        | 用户组关联事件id列表 |
| **5**    | identityIdList | List<String>        | 用户组内用户id列表   |
| **6**    | appChannelId   | String              | 应用渠道号           |
| **7**    | dataChannelId  | String              | 数据渠道号           |
| **8**    | createTime     | String              | 创建时间             |
| **9**    | updateTime     | String              | 更新时间             |

#### CompanyEntity

| **序号** | **字段名称**  | **字段类型**            | **字段说明**       |
| -------- | ------------- | ----------------------- | ------------------ |
| **1**    | companyId     | String                  | 公司id             |
| **2**    | companyName   | String                  | 公司名称           |
| **3**    | extraContent  | Map<String, Object>     | 公司信息，json格式 |
| **4**    | parentId      | String                  | 上级id             |
| **5**    | children      | List<CompanyTreeEntity> | 公司组织架构       |
| **6**    | userCount     | int                     | 公司用户数量       |
| **7**    | appChannelId  | String                  | 应用渠道号         |
| **8**    | dataChannelId | String                  | 数据渠道号         |
| **9**    | createTime    | String                  | 创建时间           |
| **10**   | updateTime    | String                  | 更新时间           |

#### CompanyTreeEntity

| **序号** | **字段名称** | **字段类型**            | **字段说明**                                                 |
| -------- | ------------ | ----------------------- | ------------------------------------------------------------ |
| **1**    | id           | String                  | 数据id                                                       |
| **2**    | name         | String                  | 数据名称                                                     |
| **3**    | extraContent | Map<String, Object>     | 数据内容                                                     |
| **4**    | type         | String                  | 数据类型company：公司department：部门position：职位identity：用户身份 |
| **5**    | children     | List<CompanyTreeEntity> | 子节点                                                       |

#### DepartmentEntity

| **序号** | **字段名称**   | **字段类型**        | **字段说明**       |
| -------- | -------------- | ------------------- | ------------------ |
| **1**    | departmentId   | String              | 部门id             |
| **2**    | departmentName | String              | 部门名称           |
| **3**    | parentId       | String              | 上级id             |
| **4**    | companyId      | String              | 所属公司id         |
| **5**    | extraContent   | Map<String, Object> | 部门信息，json格式 |
| **6**    | sort           | int                 | 显示顺序           |
| **7**    | createTime     | String              | 创建时间           |
| **8**    | updateTime     | String              | 更新时间           |

#### PositionEntity

| **序号** | **字段名称** | **字段类型**        | **字段说明**       |
| -------- | ------------ | ------------------- | ------------------ |
| **1**    | positionId   | String              | 职位id             |
| **2**    | positionName | String              | 职位名称           |
| **3**    | parentId     | String              | 上级id             |
| **4**    | companyId    | String              | 所属公司id         |
| **5**    | departmentId | String              | 所属部门id         |
| **6**    | sort         | int                 | 显示顺序           |
| **7**    | extraContent | Map<String, Object> | 职位信息，json格式 |
| **8**    | createTime   | String              | 创建时间           |
| **9**    | updateTime   | String              | 更新时间           |

#### EntityEntity

| **序号** | **字段名称**  | **字段类型**        | **字段说明** |
| -------- | ------------- | ------------------- | ------------ |
| **1**    | entityId      | String              | 标签id       |
| **2**    | entityName    | String              | 标签名称     |
| **3**    | type          | int                 | 标签类别     |
| **4**    | extraContent  | Map<String, Object> | 扩展信息     |
| **5**    | appChannelId  | String              | 应用渠道号   |
| **6**    | dataChannelId | String              | 数据渠道号   |
| **7**    | createTime    | String              | 创建时间     |

#### EntityRelationEntity

| **序号** | **字段名称** | **字段类型**        | **字段说明** |
| -------- | ------------ | ------------------- | ------------ |
| **1**    | entityId     | String              | 标签id       |
| **2**    | relationId   | String              | 用户id       |
| **3**    | extraContent | Map<String, Object> | 扩展信息     |
| **4**    | createTime   | String              | 创建时间     |
| **5**    | updateTime   | String              | 更新时间     |

#### FriendEntity

| **序号** | **字段名称**     | **字段类型**        | **字段说明**     |
| -------- | ---------------- | ------------------- | ---------------- |
| **1**    | identityId       | String              | 用户身份id       |
| **2**    | targetIdentityId | String              | 好友用户身份id   |
| **3**    | targetIdentity   | IdentityEntity      | 好友用户身份信息 |
| **4**    | extraContent     | Map<String, Object> | 扩展信息         |
| **5**    | appChannelId     | String              | 应用渠道号       |
| **6**    | dataChannelId    | String              | 数据渠道号       |
| **7**    | createTime       | String              | 创建时间         |
| **8**    | updateTime       | String              | 更新时间         |

#### ChatGroupEntity

| **序号** | **字段名称**  | **字段类型**         | **字段说明** |
| -------- | ------------- | -------------------- | ------------ |
| **1**    | chatGroupId   | String               | 群聊id       |
| **2**    | chatGroupName | String               | 群聊名称     |
| **3**    | extraContent  | Map<String, Object>  | 扩展信息     |
| **4**    | identityList  | List<IdentityEntity> | 群成员列表   |
| **5**    | appChannelId  | String               | 应用渠道号   |
| **6**    | dataChannelId | String               | 数据渠道号   |
| **7**    | createTime    | String               | 创建时间     |
| **8**    | updateTime    | String               | 更新时间     |

#### ChatGroupIdentityEntity

| **序号** | **字段名称** | **字段类型**        | **字段说明** |
| -------- | ------------ | ------------------- | ------------ |
| **1**    | chatGroupId  | String              | 群聊id       |
| **2**    | identityId   | String              | 群成员身份id |
| **3**    | extraContent | Map<String, Object> | 扩展信息     |
| **4**    | identity     | IdentityEntity      | 群成员信息   |
| **5**    | createTime   | String              | 创建时间     |
| **6**    | updateTime   | String              | 更新时间     |

### 配置项说明

#### LogEntity 的 type 字段

| **序号** | **值** | **说明**   |
| -------- | ------ | ---------- |
| **1**    | 1      | 登录       |
| **2**    | 2      | 退出       |
| **3**    | 3      | 添加用户   |
| **4**    | 4      | 修改用户   |
| **5**    | 5      | 删除用户   |
| **6**    | 6      | 添加身份   |
| **7**    | 7      | 修改身份   |
| **8**    | 8      | 删除身份   |
| **9**    | 9      | 切换身份   |
| **10**   | 10     | 添加公司   |
| **11**   | 11     | 修改公司   |
| **12**   | 12     | 删除公司   |
| **13**   | 13     | 添加部门   |
| **14**   | 14     | 修改部门   |
| **15**   | 15     | 删除部门   |
| **16**   | 16     | 添加职位   |
| **17**   | 17     | 修改职位   |
| **18**   | 18     | 删除职位   |
| **19**   | 19     | 添加用户组 |
| **20**   | 20     | 修改用户组 |
| **21**   | 21     | 删除用户组 |
| **22**   | 22     | 创建群聊   |
| **23**   | 23     | 修改群聊   |
| **24**   | 24     | 删除群聊   |
| **25**   | 25     | 添加群成员 |
| **26**   | 26     | 修改群成员 |
| **27**   | 27     | 删除群成员 |
| **28**   | 28     | 添加好友   |
| **29**   | 29     | 修改好友   |
| **30**   | 30     | 删除好友   |
| **31**   | 31     | 添加标签   |
| **32**   | 32     | 修改标签   |
| **33**   | 33     | 删除标签   |

#### token的状态值

| **序号** | **值** | **说明**     |
| -------- | ------ | ------------ |
| **1**    | 40000  | 正常退出     |
| **2**    | 40001  | 切换公司     |
| **3**    | 40002  | 其他设备登录 |
| **4**    | 40003  | 权限变动     |
| **5**    | 40004  | 身份变动     |

### 变更记录

| **版本号** | **修改日期** | **修改内容**                                                 | **修改人** |
| ---------- | ------------ | ------------------------------------------------------------ | ---------- |
| 5.0        | 2023-08-18   | 用户微服务改版升级                                           | 何一龙     |
| 4.2        | 2023-06-16   | 增加好友、群聊相关接口                                       | 何一龙     |
| 4.1        | 2023-05-25   | 增加接口描述和使用场景                                       | 何一龙     |
| 4.0        | 2023-04-13   | 合并公司微服务接口                                           | 何一龙     |
| 3.5        | 2023-03-30   | 获取用户登录状态接口增加identityIdList参数                   | 何一龙     |
| 3.4        | 2023-02-02   | 增加身份登录接口                                             | 何一龙     |
| 3.3        | 2023-01-13   | 增加获取组织架构内用户列表接口                               | 何一龙     |
| 3.2        | 2023-01-03   | 增加接口：添加用户接口、修改用户接口、删除用户接口           | 何一龙     |
| 3.1        | 2023-01-03   | 1. 用户表（user_info）删除支付密码（pay_password）和手机号（phone）字段2. 用户组表（user_group）增加保留域字段（extra_content）、删除公司id字段（company_id）、删除事件id列表字段（event_id_list）3. 用户登录状态表（user_token）删除用户id（user_id）、手机厂商代码（device_code）、推送token字段（push_token）4. 用户公司关系表（user_company_relation）删除部门id（department_id）、职位id（position_id）字段5. 增加验证码表（verify_code）6. 增加用户部门关系表（user_department_relation）7. 增加用户职位关系表（user_position_relation）8. 增加用户组和权限关系表（group_event_relation）9. 用户和用户组关系表删除用户id（user_id），增加用户身份id（ucc_id）10. 注册接口废除phone参数，增加userName参数、增加smsOrderNo参数，verifyCode参数改为smsVerifyCode11. 登录接口废除phone参数，增加userName参数、增加smsOrderNo参数，verifyCode参数改为smsVerifyCode12. 修改密码接口废除phone参数，增加smsOrderNo参数，verifyCode参数改为smsVerifyCode13. 增加忘记密码接口14. 退出接口废除userId参数、废除tokenStatus参数，增加token参数15. 添加用户接口（/addUser）改为添加公司用户接口（/addCompanyUser），废除phone参数、废除password参数、废除payPassword参数，废除departmentId和positionId字段，增加departmentIdList和positionIdList字段16. 修改用户接口（/updateUser）改为修改公司用户接口（/updateCompanyUser），废除password参数、废除payPassword参数，废除departmentId和positionId字段，增加departmentIdList和positionIdList字段17. 删除用户接口（/deleteUser）改为删除公司用户接口（/deleteCompanyUser）18. 切换公司接口（/changeCompany）改为切换身份接口（/changeIdentity）19. 添加用户组接口（/addUserGroup）废除companyId字段，增加extraContent字段20. 修改用户组接口（/updateUserGroup）增加extraContent字段21. 获取验证码接口（/getSmsVerifyCode）改为（/getVerifyCode），废除所有请求参数，增加返回值smsVerifyCode和smsOrderNo22. 验证验证码接口（/checkSmsVerifyCode）改为（/checkVerifyCode），增加smsOrderNo参数，verifyCode参数改为smsVerifyCode，废除type参数23. 用户身份实体废除departmentId和positionId字段，增加departmentIdList和positionIdList字段 | 何一龙     |
| 3.0        | 2022-12-28   | 用户id、用户组id改为36位uuid                                 | 何一龙     |
| 2.1        | 2022-12-22   | 添加用户和修改用户增加用户id列表字段添加用户和修改用户删除权限id列表字段用户组实体增加用户id列表字段 | 何一龙     |
| 2.0        | 2022-12-14   | 添加用户和修改用户增加参数                                   | 何一龙     |
| 1.9        | 2022-11-22   | 增加删除用户接口                                             | 何一龙     |
| 1.8        | 2022-09-20   | 修改数据表，去掉业务字段修改验证token接口，返回用户信息      | 何一龙     |
| 1.7        | 2022-09-15   | 获取用户列表接口增加用户id列表查询                           | 何一龙     |
| 1.6        | 2022-09-05   | 增加修改公司接口增加修改事件接口                             | 何一龙     |
| 1.5        | 2022-09-02   | channelId改为从请求头获取去掉请求参数的channelId字段         | 何一龙     |
| 1.4        | 2022-09-01   | 增加获取验证码接口增加验证短信码接口增加用户组列表接口修改用户列表接口查询条件增加接口的请求示例和返回示例 | 何一龙     |
| 1.3        | 2022-08-31   | 修改接口参数                                                 | 何一龙     |
| 1.2        | 2022-08-26   | PHP版本改为8.0.21增加公司表、事件表用户表删除业务字段，增加保留域字段 | 何一龙     |
| 1.1        | 2022-08-25   | 增加一些接口的使用场景描述增加渠道号参数                     | 何一龙     |
| 1.0        | 2022-08-25   | 初版文档                                                     | 何一龙     |

### TODO

1. 实体类数据增加缓存，提高查询效率
2. 用户可通过传参数控制返回的数据结构
3. 增加身份和标签关系类接口
4. Java版SDK
5. 优化实体类扩展字段，由字符串反序列化成Map的方法
6. 按开发文档优化ping接口
7. 对接系统配置微服务、配置项存入系统配置微服务
8. 接口时序图
9. 服务启动打印信息
10. MD版本文档（README.md）